# API Dashboard Thống Kê

## <PERSON>ô tả
API này cung cấp các thông tin thống kê dashboard cho garage, bao gồm:
- S<PERSON> yêu cầu báo giá đã tạo trong tuần này
- <PERSON><PERSON> yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" trong tuần này
- Số đơn hàng ở trạng thái "Đang giao hàng"
- <PERSON><PERSON> đơn hàng ở trạng thái "Hoàn thành" trong tuần này

## Endpoint

### GET /api/v1/dashboard/stats

**Mô tả:** Lấy thống kê dashboard cho garage hiện tại

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Response:**

```json
{
  "success": true,
  "data": {
    "totalQuotationRequestsThisWeek": 15,
    "quotationRequestsPricingThisWeek": 5,
    "purchaseOrdersInShipping": 8,
    "purchaseOrdersCompletedThisWeek": 3
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `totalQuotationRequestsThisWeek` | Long | Số yêu cầu báo giá garage đã tạo trong tuần này (tất cả trạng thái, không bị xóa) |
| `quotationRequestsPricingThisWeek` | Long | Số yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này |
| `purchaseOrdersInShipping` | Long | Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING) |
| `purchaseOrdersCompletedThisWeek` | Long | Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) trong tuần này |

## Chi tiết Logic

### 1. Thời gian "tuần này"
- **Bắt đầu tuần:** Thứ 2 00:00:00 (theo múi giờ Việt Nam)
- **Kết thúc tuần:** Chủ nhật 23:59:59.999 (theo múi giờ Việt Nam)

### 2. Điều kiện thống kê

#### Yêu cầu báo giá đã tạo trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:** 
  - `tenant_id = <current_tenant_id>`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Yêu cầu báo giá ở trạng thái PRICING trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:**
  - `tenant_id = <current_tenant_id>`
  - `status = 'PRICING'`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Đơn hàng đang giao hàng
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'IN_SHIPPING'`
  - Không bị xóa (soft delete)

#### Đơn hàng hoàn thành trong tuần này
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'CLOSED'`
  - `updated_at BETWEEN <start_of_week> AND <end_of_week>` (thời gian hoàn thành)
  - Không bị xóa (soft delete)

## Mapping Trạng thái

### QuotationStatus
- `PRICING` = "Yêu cầu giá chi tiết"

### POStatusEnum
- `IN_SHIPPING` = "Đang giao hàng"
- `CLOSED` = "Hoàn thành"

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "data": null
}
```

## Ví dụ sử dụng

### cURL
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/stats" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
fetch('/api/v1/dashboard/stats', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Dashboard stats:', data.data);
});
```

## Lưu ý
- API này yêu cầu authentication
- Dữ liệu được filter theo tenant hiện tại (garage)
- Thời gian được tính theo múi giờ Việt Nam (Asia/Ho_Chi_Minh)
- API sử dụng soft delete, không tính các bản ghi đã bị xóa
