# API Dashboard Thống Kê

## <PERSON>ô tả
API này cung cấp các thông tin thống kê dashboard cho garage, bao gồm:
- S<PERSON> yêu cầu báo giá đã tạo trong tuần này
- <PERSON><PERSON> yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" trong tuần này
- Số đơn hàng ở trạng thái "Đang giao hàng"
- <PERSON><PERSON> đơn hàng ở trạng thái "Hoàn thành" trong tuần này

## Endpoint

### GET /api/v1/dashboard/stats

**Mô tả:** Lấy thống kê dashboard cho garage hiện tại

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Response:**

```json
{
  "success": true,
  "data": {
    "totalQuotationRequestsThisWeek": 15,
    "quotationRequestsPricingThisWeek": 5,
    "purchaseOrdersInShipping": 8,
    "purchaseOrdersCompletedThisWeek": 3
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `totalQuotationRequestsThisWeek` | Long | Số yêu cầu báo giá garage đã tạo trong tuần này (tất cả trạng thái, không bị xóa) |
| `quotationRequestsPricingThisWeek` | Long | Số yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này |
| `purchaseOrdersInShipping` | Long | Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING) |
| `purchaseOrdersCompletedThisWeek` | Long | Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) trong tuần này |

## Chi tiết Logic

### 1. Thời gian "tuần này"
- **Bắt đầu tuần:** Thứ 2 00:00:00 (theo múi giờ Việt Nam)
- **Kết thúc tuần:** Chủ nhật 23:59:59.999 (theo múi giờ Việt Nam)

### 2. Điều kiện thống kê

#### Yêu cầu báo giá đã tạo trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:** 
  - `tenant_id = <current_tenant_id>`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Yêu cầu báo giá ở trạng thái PRICING trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:**
  - `tenant_id = <current_tenant_id>`
  - `status = 'PRICING'`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Đơn hàng đang giao hàng
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'IN_SHIPPING'`
  - Không bị xóa (soft delete)

#### Đơn hàng hoàn thành trong tuần này
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'CLOSED'`
  - `updated_at BETWEEN <start_of_week> AND <end_of_week>` (thời gian hoàn thành)
  - Không bị xóa (soft delete)

## Mapping Trạng thái

### QuotationStatus
- `PRICING` = "Yêu cầu giá chi tiết"

### POStatusEnum
- `IN_SHIPPING` = "Đang giao hàng"
- `CLOSED` = "Hoàn thành"

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "data": null
}
```

## Ví dụ sử dụng

### cURL
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/stats" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
fetch('/api/v1/dashboard/stats', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Dashboard stats:', data.data);
});
```

## Lưu ý
- API này yêu cầu authentication
- Dữ liệu được filter theo tenant hiện tại (garage)
- Thời gian được tính theo múi giờ Việt Nam (Asia/Ho_Chi_Minh)
- API sử dụng soft delete, không tính các bản ghi đã bị xóa

---

# API Theo Dõi Tổng Quan Số Liệu Chi Tiêu

## Mô tả
API này cung cấp thông tin tổng quan về số liệu chi tiêu của garage, bao gồm:
- Tổng số tiền đã chi trong tuần này
- Tổng chi tiêu trong tháng này
- Tổng chi tiêu trong năm nay
- Số tiền được làm tròn theo quy tắc
- Chuỗi hiển thị với đơn vị

## Endpoint

### GET /api/v1/dashboard/spending-overview

**Mô tả:** Lấy tổng quan số liệu chi tiêu cho garage hiện tại

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Response:**

```json
{
  "success": true,
  "data": {
    "totalSpentThisWeek": 15750000,
    "totalSpentThisWeekRounded": 16000000,
    "totalSpentThisMonth": 125300000,
    "totalSpentThisMonthRounded": 130000000,
    "totalSpentThisYear": 2450000000,
    "totalSpentThisYearRounded": 2500000000,
    "updateTime": "15/06/2025 - 21/06/2025",
    "totalSpentThisWeekDisplay": "16 triệu",
    "totalSpentThisMonthDisplay": "130 triệu",
    "totalSpentThisYearDisplay": "2.5 tỷ"
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `totalSpentThisWeek` | BigDecimal | Tổng số tiền đã chi trong tuần này (số tiền gốc) |
| `totalSpentThisWeekRounded` | BigDecimal | Tổng số tiền đã chi trong tuần này (đã làm tròn) |
| `totalSpentThisMonth` | BigDecimal | Tổng chi tiêu trong tháng này (số tiền gốc) |
| `totalSpentThisMonthRounded` | BigDecimal | Tổng chi tiêu trong tháng này (đã làm tròn) |
| `totalSpentThisYear` | BigDecimal | Tổng chi tiêu trong năm nay (số tiền gốc) |
| `totalSpentThisYearRounded` | BigDecimal | Tổng chi tiêu trong năm nay (đã làm tròn) |
| `updateTime` | String | Thời gian cập nhật (dd/MM/yyyy - dd/MM/yyyy), tối đa 7 ngày |
| `totalSpentThisWeekDisplay` | String | Chuỗi hiển thị số tiền tuần với đơn vị |
| `totalSpentThisMonthDisplay` | String | Chuỗi hiển thị số tiền tháng với đơn vị |
| `totalSpentThisYearDisplay` | String | Chuỗi hiển thị số tiền năm với đơn vị |

## Chi tiết Logic

### 1. Thời gian tính toán
- **Tuần này**: Từ thứ 2 00:00:00 đến chủ nhật 23:59:59.999 (múi giờ VN)
- **Tháng này**: Từ ngày 1 00:00:00 đến ngày cuối tháng 23:59:59.999 (múi giờ VN)
- **Năm nay**: Từ 1/1 00:00:00 đến 31/12 23:59:59.999 (múi giờ VN)

### 2. Điều kiện tính chi tiêu
- **Bảng:** `purchase_orders` JOIN `po_products`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'CLOSED'` (chỉ tính đơn hàng đã hoàn thành)
  - `updated_at` trong khoảng thời gian tương ứng
  - Không bị xóa (soft delete)
- **Công thức:** `SUM(quantity * unit_price)` từ bảng `po_products`

### 3. Quy tắc làm tròn số tiền
- **< 1 triệu**: Hiển thị đủ số tiền (2.000đ, 50.000đ, 500.000đ)
- **Giữa 1 triệu và 1 tỷ**: Làm tròn triệu với 2 chữ số có nghĩa (2 triệu, 20 triệu, 200 triệu)
- **Trên 1 tỷ**: Làm tròn tỷ với 2 chữ số có nghĩa (2 tỷ, 20 tỷ, 200 tỷ)

### 4. Ví dụ làm tròn
| Số tiền gốc | Số tiền làm tròn | Hiển thị |
|-------------|------------------|----------|
| 1,234,567 | 1,200,000 | 1.2 triệu |
| 12,345,678 | 12,000,000 | 12 triệu |
| 123,456,789 | 120,000,000 | 120 triệu |
| 1,234,567,890 | 1,200,000,000 | 1.2 tỷ |
| 12,345,678,900 | 12,000,000,000 | 12 tỷ |

## Ví dụ sử dụng

### cURL
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/spending-overview" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
fetch('/api/v1/dashboard/spending-overview', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Spending overview:', data.data);
  console.log('Tuần này:', data.data.totalSpentThisWeekDisplay);
  console.log('Tháng này:', data.data.totalSpentThisMonthDisplay);
  console.log('Năm nay:', data.data.totalSpentThisYearDisplay);
});
```
