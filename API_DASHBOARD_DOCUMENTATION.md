# API Dashboard Thống Kê

## <PERSON>ô tả
API này cung cấp các thông tin thống kê dashboard cho garage, bao gồm:
- S<PERSON> yêu cầu báo giá đã tạo trong tuần này
- <PERSON><PERSON> yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" trong tuần này
- Số đơn hàng ở trạng thái "Đang giao hàng"
- <PERSON><PERSON> đơn hàng ở trạng thái "Hoàn thành" trong tuần này

## Endpoint

### GET /api/v1/dashboard/stats

**Mô tả:** Lấy thống kê dashboard cho garage hiện tại

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Response:**

```json
{
  "success": true,
  "data": {
    "totalQuotationRequestsThisWeek": 15,
    "quotationRequestsPricingThisWeek": 5,
    "purchaseOrdersInShipping": 8,
    "purchaseOrdersCompletedThisWeek": 3
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `totalQuotationRequestsThisWeek` | Long | Số yêu cầu báo giá garage đã tạo trong tuần này (tất cả trạng thái, không bị xóa) |
| `quotationRequestsPricingThisWeek` | Long | Số yêu cầu báo giá ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này |
| `purchaseOrdersInShipping` | Long | Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING) |
| `purchaseOrdersCompletedThisWeek` | Long | Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) trong tuần này |

## Chi tiết Logic

### 1. Thời gian "tuần này"
- **Bắt đầu tuần:** Thứ 2 00:00:00 (theo múi giờ Việt Nam)
- **Kết thúc tuần:** Chủ nhật 23:59:59.999 (theo múi giờ Việt Nam)

### 2. Điều kiện thống kê

#### Yêu cầu báo giá đã tạo trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:** 
  - `tenant_id = <current_tenant_id>`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Yêu cầu báo giá ở trạng thái PRICING trong tuần này
- **Bảng:** `quotation_asks`
- **Điều kiện:**
  - `tenant_id = <current_tenant_id>`
  - `status = 'PRICING'`
  - `created_at BETWEEN <start_of_week> AND <end_of_week>`
  - Không bị xóa (soft delete)

#### Đơn hàng đang giao hàng
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'IN_SHIPPING'`
  - Không bị xóa (soft delete)

#### Đơn hàng hoàn thành trong tuần này
- **Bảng:** `purchase_orders`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'CLOSED'`
  - `updated_at BETWEEN <start_of_week> AND <end_of_week>` (thời gian hoàn thành)
  - Không bị xóa (soft delete)

## Mapping Trạng thái

### QuotationStatus
- `PRICING` = "Yêu cầu giá chi tiết"

### POStatusEnum
- `IN_SHIPPING` = "Đang giao hàng"
- `CLOSED` = "Hoàn thành"

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "data": null
}
```

## Ví dụ sử dụng

### cURL
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/stats" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
fetch('/api/v1/dashboard/stats', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Dashboard stats:', data.data);
});
```

## Lưu ý
- API này yêu cầu authentication
- Dữ liệu được filter theo tenant hiện tại (garage)
- Thời gian được tính theo múi giờ Việt Nam (Asia/Ho_Chi_Minh)
- API sử dụng soft delete, không tính các bản ghi đã bị xóa

---

# API Theo Dõi Tổng Quan Số Liệu Chi Tiêu

## Mô tả
API này cung cấp thông tin tổng quan về số liệu chi tiêu của garage, bao gồm:
- Tổng số tiền đã chi trong tuần này
- Tổng chi tiêu trong tháng này
- Tổng chi tiêu trong năm nay
- Số tiền được làm tròn theo quy tắc
- Chuỗi hiển thị với đơn vị

## Endpoint

### GET /api/v1/dashboard/spending-overview

**Mô tả:** Lấy tổng quan số liệu chi tiêu cho garage hiện tại

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Response:**

```json
{
  "success": true,
  "data": {
    "totalSpentThisWeek": 15750000,
    "totalSpentThisWeekRounded": 16000000,
    "totalSpentThisMonth": 125300000,
    "totalSpentThisMonthRounded": 130000000,
    "totalSpentThisYear": 2450000000,
    "totalSpentThisYearRounded": 2500000000,
    "updateTime": "15/06/2025 - 21/06/2025",
    "totalSpentThisWeekDisplay": "16 triệu",
    "totalSpentThisMonthDisplay": "130 triệu",
    "totalSpentThisYearDisplay": "2.5 tỷ"
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `totalSpentThisWeek` | BigDecimal | Tổng số tiền đã chi trong tuần này (số tiền gốc) |
| `totalSpentThisWeekRounded` | BigDecimal | Tổng số tiền đã chi trong tuần này (đã làm tròn) |
| `totalSpentThisMonth` | BigDecimal | Tổng chi tiêu trong tháng này (số tiền gốc) |
| `totalSpentThisMonthRounded` | BigDecimal | Tổng chi tiêu trong tháng này (đã làm tròn) |
| `totalSpentThisYear` | BigDecimal | Tổng chi tiêu trong năm nay (số tiền gốc) |
| `totalSpentThisYearRounded` | BigDecimal | Tổng chi tiêu trong năm nay (đã làm tròn) |
| `updateTime` | String | Thời gian cập nhật (dd/MM/yyyy - dd/MM/yyyy), tối đa 7 ngày |
| `totalSpentThisWeekDisplay` | String | Chuỗi hiển thị số tiền tuần với đơn vị |
| `totalSpentThisMonthDisplay` | String | Chuỗi hiển thị số tiền tháng với đơn vị |
| `totalSpentThisYearDisplay` | String | Chuỗi hiển thị số tiền năm với đơn vị |

## Chi tiết Logic

### 1. Thời gian tính toán
- **Tuần này**: Từ thứ 2 00:00:00 đến chủ nhật 23:59:59.999 (múi giờ VN)
- **Tháng này**: Từ ngày 1 00:00:00 đến ngày cuối tháng 23:59:59.999 (múi giờ VN)
- **Năm nay**: Từ 1/1 00:00:00 đến 31/12 23:59:59.999 (múi giờ VN)

### 2. Điều kiện tính chi tiêu
- **Bảng:** `purchase_orders` JOIN `po_products`
- **Điều kiện:**
  - `purchaser_id = <current_tenant_id>`
  - `status = 'CLOSED'` (chỉ tính đơn hàng đã hoàn thành)
  - `updated_at` trong khoảng thời gian tương ứng
  - Không bị xóa (soft delete)
- **Công thức:** `SUM(quantity * unit_price)` từ bảng `po_products`

### 3. Quy tắc làm tròn số tiền
- **< 1 triệu**: Hiển thị đủ số tiền (2.000đ, 50.000đ, 500.000đ)
- **Giữa 1 triệu và 1 tỷ**: Làm tròn triệu với 2 chữ số có nghĩa (2 triệu, 20 triệu, 200 triệu)
- **Trên 1 tỷ**: Làm tròn tỷ với 2 chữ số có nghĩa (2 tỷ, 20 tỷ, 200 tỷ)

### 4. Ví dụ làm tròn
| Số tiền gốc | Số tiền làm tròn | Hiển thị |
|-------------|------------------|----------|
| 1,234,567 | 1,200,000 | 1.2 triệu |
| 12,345,678 | 12,000,000 | 12 triệu |
| 123,456,789 | 120,000,000 | 120 triệu |
| 1,234,567,890 | 1,200,000,000 | 1.2 tỷ |
| 12,345,678,900 | 12,000,000,000 | 12 tỷ |

## Ví dụ sử dụng

### cURL
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/spending-overview" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
fetch('/api/v1/dashboard/spending-overview', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Spending overview:', data.data);
  console.log('Tuần này:', data.data.totalSpentThisWeekDisplay);
  console.log('Tháng này:', data.data.totalSpentThisMonthDisplay);
  console.log('Năm nay:', data.data.totalSpentThisYearDisplay);
});
```

---

# API Biểu Đồ Báo Cáo Chi Tiêu

## Mô tả
API này cung cấp dữ liệu cho biểu đồ báo cáo chi tiêu với các khoảng thời gian khác nhau:
- **Tuần này**: 7 cột tương ứng với 7 ngày (T2-CN)
- **Tháng này**: 4 cột tương ứng với 4 tuần
- **6 tháng gần nhất**: 6 cột tương ứng với 6 tháng (tháng hiện tại + 5 tháng trước)
- **1 năm gần nhất**: 4 cột tương ứng với 4 quý (quý hiện tại + 3 quý trước)

## Endpoint

### GET /api/v1/dashboard/spending-chart

**Mô tả:** Lấy dữ liệu biểu đồ chi tiêu theo khoảng thời gian

**Headers:**
- `Authorization: Bearer <token>` (bắt buộc)

**Query Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `period` | String | No | `this_week` | Loại khoảng thời gian: `this_week`, `this_month`, `last_6_months`, `last_year` |

**Response:**

```json
{
  "success": true,
  "data": {
    "period": "THIS_WEEK",
    "periodDisplayName": "Tuần này",
    "labels": ["T2", "T3", "T4", "T5", "T6", "T7", "CN"],
    "data": [1500000, 2300000, 1800000, 2100000, 1900000, 2500000, 1200000],
    "dataRounded": [1500000, 2300000, 1800000, 2100000, 1900000, 2500000, 1200000],
    "dataDisplay": ["1.5 triệu", "2.3 triệu", "1.8 triệu", "2.1 triệu", "1.9 triệu", "2.5 triệu", "1.2 triệu"],
    "totalSpent": 13300000,
    "totalSpentRounded": 13000000,
    "totalSpentDisplay": "13 triệu",
    "timeRange": "15/06/2025 - 21/06/2025",
    "maxValue": 2500000,
    "minValue": 1200000,
    "averageValue": 1900000
  },
  "message": "success"
}
```

**Response Fields:**

| Field | Type | Mô tả |
|-------|------|-------|
| `period` | SpendingPeriodEnum | Loại khoảng thời gian |
| `periodDisplayName` | String | Tên hiển thị của khoảng thời gian |
| `labels` | List<String> | Danh sách nhãn cho các cột trong biểu đồ |
| `data` | List<BigDecimal> | Dữ liệu chi tiêu gốc (chưa làm tròn) |
| `dataRounded` | List<BigDecimal> | Dữ liệu chi tiêu đã làm tròn |
| `dataDisplay` | List<String> | Chuỗi hiển thị với đơn vị |
| `totalSpent` | BigDecimal | Tổng chi tiêu trong khoảng thời gian |
| `totalSpentRounded` | BigDecimal | Tổng chi tiêu đã làm tròn |
| `totalSpentDisplay` | String | Chuỗi hiển thị tổng chi tiêu với đơn vị |
| `timeRange` | String | Khoảng thời gian chi tiết (dd/MM/yyyy - dd/MM/yyyy) |
| `maxValue` | BigDecimal | Giá trị cao nhất trong dữ liệu |
| `minValue` | BigDecimal | Giá trị thấp nhất trong dữ liệu |
| `averageValue` | BigDecimal | Giá trị trung bình |

## Chi tiết Logic theo Period

### 1. THIS_WEEK (Tuần này)
- **Labels**: `["T2", "T3", "T4", "T5", "T6", "T7", "CN"]`
- **Data Points**: 7 cột (7 ngày từ thứ 2 đến chủ nhật)
- **Thời gian**: Từ thứ 2 00:00:00 đến chủ nhật 23:59:59.999

### 2. THIS_MONTH (Tháng này)
- **Labels**: `["Tuần 1", "Tuần 2", "Tuần 3", "Tuần 4"]`
- **Data Points**: 4 cột (4 tuần trong tháng)
- **Thời gian**: Từ ngày 1 của tháng đến ngày cuối tháng

### 3. LAST_6_MONTHS (6 tháng gần nhất)
- **Labels**: `["10/2024", "11/2024", "12/2024", "01/2025", "02/2025", "03/2025"]`
- **Data Points**: 6 cột (6 tháng)
- **Logic**: Tháng hiện tại (từ ngày 1 đến ngày hiện tại) + 5 tháng trước (trọn vẹn)

**Ví dụ**: Hôm nay 20/03/2025
- Tháng 3/2025: 01/03 - 20/03/2025
- Tháng 2/2025: 01/02 - 28/02/2025
- Tháng 1/2025: 01/01 - 31/01/2025
- Tháng 12/2024: 01/12 - 31/12/2024
- Tháng 11/2024: 01/11 - 30/11/2024
- Tháng 10/2024: 01/10 - 31/10/2024

### 4. LAST_YEAR (1 năm gần nhất)
- **Labels**: `["Q2/2024", "Q3/2024", "Q4/2024", "Q1/2025"]`
- **Data Points**: 4 cột (4 quý)
- **Logic**: Quý hiện tại (từ đầu quý đến ngày hiện tại) + 3 quý trước (trọn vẹn)

**Ví dụ**: Hôm nay 20/03/2025
- Quý 1/2025: T1, T2, T3 (tính đến 20/03/2025)
- Quý 4/2024: T10, T11, T12
- Quý 3/2024: T7, T8, T9
- Quý 2/2024: T4, T5, T6

## Ví dụ sử dụng

### cURL - Tuần này
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/spending-chart?period=this_week" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### cURL - 6 tháng gần nhất
```bash
curl -X GET "http://localhost:8080/api/v1/dashboard/spending-chart?period=last_6_months" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
// Lấy dữ liệu biểu đồ tuần này
fetch('/api/v1/dashboard/spending-chart?period=this_week', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Period:', data.data.periodDisplayName);
  console.log('Labels:', data.data.labels);
  console.log('Data:', data.data.dataDisplay);
  console.log('Total:', data.data.totalSpentDisplay);

  // Sử dụng dữ liệu cho biểu đồ
  const chartData = {
    labels: data.data.labels,
    datasets: [{
      label: 'Chi tiêu',
      data: data.data.data,
      backgroundColor: 'rgba(54, 162, 235, 0.2)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    }]
  };
});
```

## Error Responses

### 400 Bad Request - Invalid Period
```json
{
  "success": false,
  "message": "Invalid period parameter. Supported values: this_week, this_month, last_6_months, last_year",
  "data": null
}
```

## Supported Period Values

| Value | Description | Data Points | Labels Example |
|-------|-------------|-------------|----------------|
| `this_week` | Tuần này | 7 | `["T2", "T3", "T4", "T5", "T6", "T7", "CN"]` |
| `this_month` | Tháng này | 4 | `["Tuần 1", "Tuần 2", "Tuần 3", "Tuần 4"]` |
| `last_6_months` | 6 tháng gần nhất | 6 | `["10/2024", "11/2024", "12/2024", "01/2025", "02/2025", "03/2025"]` |
| `last_year` | 1 năm gần nhất | 4 | `["Q2/2024", "Q3/2024", "Q4/2024", "Q1/2025"]` |
