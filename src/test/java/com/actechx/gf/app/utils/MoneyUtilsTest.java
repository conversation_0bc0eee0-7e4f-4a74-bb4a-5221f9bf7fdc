package com.actechx.gf.app.utils;

import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class MoneyUtilsTest {

    @Test
    void formatMoney_ShouldReturnOriginalAmount_WhenLessThanOneMillion() {
        // Given
        BigDecimal amount1 = new BigDecimal("2000");
        BigDecimal amount2 = new BigDecimal("50000");
        BigDecimal amount3 = new BigDecimal("500000");
        BigDecimal amount4 = new BigDecimal("999999");

        // When
        BigDecimal result1 = MoneyUtils.formatMoney(amount1);
        BigDecimal result2 = MoneyUtils.formatMoney(amount2);
        BigDecimal result3 = MoneyUtils.formatMoney(amount3);
        BigDecimal result4 = MoneyUtils.formatMoney(amount4);

        // Then
        assertEquals(new BigDecimal("2000"), result1);
        assertEquals(new BigDecimal("50000"), result2);
        assertEquals(new BigDecimal("500000"), result3);
        assertEquals(new BigDecimal("999999"), result4);
    }

    @Test
    void formatMoney_ShouldRoundToMillion_WhenBetweenOneMillionAndOneBillion() {
        // Given
        BigDecimal amount1 = new BigDecimal("1234567"); // 1.2 triệu
        BigDecimal amount2 = new BigDecimal("12345678"); // 12 triệu
        BigDecimal amount3 = new BigDecimal("123456789"); // 120 triệu

        // When
        BigDecimal result1 = MoneyUtils.formatMoney(amount1);
        BigDecimal result2 = MoneyUtils.formatMoney(amount2);
        BigDecimal result3 = MoneyUtils.formatMoney(amount3);

        // Then
        assertEquals(new BigDecimal("1200000"), result1); // 1.2 triệu
        assertEquals(new BigDecimal("12000000"), result2); // 12 triệu
        assertEquals(new BigDecimal("120000000"), result3); // 120 triệu
    }

    @Test
    void formatMoney_ShouldRoundToBillion_WhenGreaterThanOneBillion() {
        // Given
        BigDecimal amount1 = new BigDecimal("1234567890"); // 1.2 tỷ
        BigDecimal amount2 = new BigDecimal("12345678900"); // 12 tỷ
        BigDecimal amount3 = new BigDecimal("123456789000"); // 120 tỷ

        // When
        BigDecimal result1 = MoneyUtils.formatMoney(amount1);
        BigDecimal result2 = MoneyUtils.formatMoney(amount2);
        BigDecimal result3 = MoneyUtils.formatMoney(amount3);

        // Then
        assertEquals(new BigDecimal("1200000000"), result1); // 1.2 tỷ
        assertEquals(new BigDecimal("12000000000"), result2); // 12 tỷ
        assertEquals(new BigDecimal("120000000000"), result3); // 120 tỷ
    }

    @Test
    void formatMoney_ShouldReturnZero_WhenAmountIsNull() {
        // When
        BigDecimal result = MoneyUtils.formatMoney(null);

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void formatMoneyWithUnit_ShouldReturnCorrectFormat_ForDifferentAmounts() {
        // Given & When & Then
        assertEquals("2,000đ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("2000")));
        assertEquals("50,000đ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("50000")));
        assertEquals("500,000đ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("500000")));
        
        assertEquals("1.2 triệu", MoneyUtils.formatMoneyWithUnit(new BigDecimal("1200000")));
        assertEquals("12 triệu", MoneyUtils.formatMoneyWithUnit(new BigDecimal("12000000")));
        assertEquals("120 triệu", MoneyUtils.formatMoneyWithUnit(new BigDecimal("120000000")));
        
        assertEquals("1.2 tỷ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("1200000000")));
        assertEquals("12 tỷ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("12000000000")));
        assertEquals("120 tỷ", MoneyUtils.formatMoneyWithUnit(new BigDecimal("120000000000")));
    }

    @Test
    void formatMoneyWithUnit_ShouldReturnZero_WhenAmountIsNull() {
        // When
        String result = MoneyUtils.formatMoneyWithUnit(null);

        // Then
        assertEquals("0đ", result);
    }

    @Test
    void formatMoney_ShouldHandleDecimalAmounts() {
        // Given
        BigDecimal amount1 = new BigDecimal("1234567.89"); // Should round to 1235000
        BigDecimal amount2 = new BigDecimal("1234567890.12"); // Should round to 1200000000

        // When
        BigDecimal result1 = MoneyUtils.formatMoney(amount1);
        BigDecimal result2 = MoneyUtils.formatMoney(amount2);

        // Then
        assertEquals(new BigDecimal("1200000"), result1); // 1.2 triệu
        assertEquals(new BigDecimal("1200000000"), result2); // 1.2 tỷ
    }
}
