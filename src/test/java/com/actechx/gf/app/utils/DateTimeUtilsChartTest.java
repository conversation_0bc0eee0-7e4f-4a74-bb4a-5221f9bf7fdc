package com.actechx.gf.app.utils;

import org.junit.jupiter.api.Test;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DateTimeUtilsChartTest {

    private static final ZoneId VIETNAM_ZONE = ZoneId.of("Asia/Ho_Chi_Minh");

    @Test
    void getDaysOfCurrentWeek_ShouldReturn7Days() {
        // When
        List<Instant[]> days = DateTimeUtils.getDaysOfCurrentWeek();

        // Then
        assertEquals(7, days.size());
        
        // Verify each day is 24 hours apart
        for (int i = 0; i < days.size() - 1; i++) {
            Instant currentDayStart = days.get(i)[0];
            Instant nextDayStart = days.get(i + 1)[0];
            
            long hoursDiff = (nextDayStart.getEpochSecond() - currentDayStart.getEpochSecond()) / 3600;
            assertEquals(24, hoursDiff);
        }
        
        // Verify first day is Monday
        LocalDateTime firstDay = LocalDateTime.ofInstant(days.get(0)[0], VIETNAM_ZONE);
        assertEquals(1, firstDay.getDayOfWeek().getValue()); // Monday = 1
    }

    @Test
    void getWeeksOfCurrentMonth_ShouldReturnWeeksInMonth() {
        // When
        List<Instant[]> weeks = DateTimeUtils.getWeeksOfCurrentMonth();

        // Then
        assertTrue(weeks.size() >= 1 && weeks.size() <= 6); // A month can have 1-6 weeks
        
        // Verify weeks are in chronological order
        for (int i = 0; i < weeks.size() - 1; i++) {
            assertTrue(weeks.get(i)[1].isBefore(weeks.get(i + 1)[0]) || 
                      weeks.get(i)[1].equals(weeks.get(i + 1)[0]));
        }
    }

    @Test
    void getLast6Months_ShouldReturn6Months() {
        // When
        List<Instant[]> months = DateTimeUtils.getLast6Months();

        // Then
        assertEquals(6, months.size());
        
        // Verify months are in chronological order (oldest first)
        for (int i = 0; i < months.size() - 1; i++) {
            assertTrue(months.get(i)[1].isBefore(months.get(i + 1)[0]) || 
                      months.get(i)[1].equals(months.get(i + 1)[0]));
        }
        
        // Verify last month is current month (partial)
        LocalDateTime lastMonthEnd = LocalDateTime.ofInstant(months.get(5)[1], VIETNAM_ZONE);
        LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
        
        // Last month should end around current time (within same day)
        assertEquals(now.getYear(), lastMonthEnd.getYear());
        assertEquals(now.getMonth(), lastMonthEnd.getMonth());
        assertEquals(now.getDayOfMonth(), lastMonthEnd.getDayOfMonth());
    }

    @Test
    void getLast4Quarters_ShouldReturn4Quarters() {
        // When
        List<Instant[]> quarters = DateTimeUtils.getLast4Quarters();

        // Then
        assertEquals(4, quarters.size());
        
        // Verify quarters are in chronological order (oldest first)
        for (int i = 0; i < quarters.size() - 1; i++) {
            assertTrue(quarters.get(i)[1].isBefore(quarters.get(i + 1)[0]) || 
                      quarters.get(i)[1].equals(quarters.get(i + 1)[0]));
        }
        
        // Verify each quarter spans approximately 3 months
        for (Instant[] quarter : quarters) {
            LocalDateTime start = LocalDateTime.ofInstant(quarter[0], VIETNAM_ZONE);
            LocalDateTime end = LocalDateTime.ofInstant(quarter[1], VIETNAM_ZONE);
            
            // Quarter should start on first day of a quarter month (1, 4, 7, 10)
            int startMonth = start.getMonthValue();
            assertTrue(startMonth == 1 || startMonth == 4 || startMonth == 7 || startMonth == 10);
            assertEquals(1, start.getDayOfMonth());
        }
        
        // Verify last quarter is current quarter (partial)
        LocalDateTime lastQuarterEnd = LocalDateTime.ofInstant(quarters.get(3)[1], VIETNAM_ZONE);
        LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
        
        // Last quarter should end around current time (within same day)
        assertEquals(now.getYear(), lastQuarterEnd.getYear());
        assertEquals(now.getMonth(), lastQuarterEnd.getMonth());
        assertEquals(now.getDayOfMonth(), lastQuarterEnd.getDayOfMonth());
    }

    @Test
    void timeRanges_ShouldNotOverlap() {
        // Test days of week
        List<Instant[]> days = DateTimeUtils.getDaysOfCurrentWeek();
        for (int i = 0; i < days.size() - 1; i++) {
            assertTrue(days.get(i)[1].isBefore(days.get(i + 1)[0]) || 
                      days.get(i)[1].equals(days.get(i + 1)[0]));
        }
        
        // Test months
        List<Instant[]> months = DateTimeUtils.getLast6Months();
        for (int i = 0; i < months.size() - 1; i++) {
            assertTrue(months.get(i)[1].isBefore(months.get(i + 1)[0]) || 
                      months.get(i)[1].equals(months.get(i + 1)[0]));
        }
        
        // Test quarters
        List<Instant[]> quarters = DateTimeUtils.getLast4Quarters();
        for (int i = 0; i < quarters.size() - 1; i++) {
            assertTrue(quarters.get(i)[1].isBefore(quarters.get(i + 1)[0]) || 
                      quarters.get(i)[1].equals(quarters.get(i + 1)[0]));
        }
    }

    @Test
    void timeRanges_ShouldHaveValidStartAndEnd() {
        // Test all time ranges have start before end
        List<Instant[]> days = DateTimeUtils.getDaysOfCurrentWeek();
        days.forEach(day -> assertTrue(day[0].isBefore(day[1])));
        
        List<Instant[]> weeks = DateTimeUtils.getWeeksOfCurrentMonth();
        weeks.forEach(week -> assertTrue(week[0].isBefore(week[1])));
        
        List<Instant[]> months = DateTimeUtils.getLast6Months();
        months.forEach(month -> assertTrue(month[0].isBefore(month[1])));
        
        List<Instant[]> quarters = DateTimeUtils.getLast4Quarters();
        quarters.forEach(quarter -> assertTrue(quarter[0].isBefore(quarter[1])));
    }
}
