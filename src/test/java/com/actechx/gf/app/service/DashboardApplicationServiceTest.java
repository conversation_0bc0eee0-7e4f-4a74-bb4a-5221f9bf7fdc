package com.actechx.gf.app.service;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DashboardApplicationServiceTest {

    @Mock
    private QuotationAskRepository quotationAskRepository;

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @InjectMocks
    private DashboardApplicationService dashboardApplicationService;

    private final Long TENANT_ID = 1L;
    private final Instant START_OF_WEEK = Instant.parse("2024-01-01T00:00:00Z");
    private final Instant END_OF_WEEK = Instant.parse("2024-01-07T23:59:59.999Z");

    @BeforeEach
    void setUp() {
        // Mock SecurityUtils để trả về tenantId cố định
        // Trong test thực tế, bạn có thể cần mock SecurityUtils.getCurrentTenantIdAsLong()
    }

    @Test
    void getDashboardStats_ShouldReturnCorrectStats() {
        // Given
        Long expectedTotalQuotations = 10L;
        Long expectedPricingQuotations = 3L;
        Long expectedInShippingOrders = 5L;
        Long expectedCompletedOrders = 2L;

        // Mock repository responses
        when(quotationAskRepository.countByTenantIdAndCreatedAtBetween(eq(TENANT_ID), any(Instant.class), any(Instant.class)))
                .thenReturn(expectedTotalQuotations);
        
        when(quotationAskRepository.countByTenantIdAndStatusAndCreatedAtBetween(eq(TENANT_ID), eq(QuotationStatus.PRICING), any(Instant.class), any(Instant.class)))
                .thenReturn(expectedPricingQuotations);
        
        when(purchaseOrderRepository.countByPurchaserIdAndStatus(eq(TENANT_ID), eq(POStatusEnum.IN_SHIPPING)))
                .thenReturn(expectedInShippingOrders);
        
        when(purchaseOrderRepository.countByPurchaserIdAndStatusAndUpdatedAtBetween(eq(TENANT_ID), eq(POStatusEnum.CLOSED), any(Instant.class), any(Instant.class)))
                .thenReturn(expectedCompletedOrders);

        // Mock DateTimeUtils and SecurityUtils
        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
             MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {

            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentWeek).thenReturn(START_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentWeek).thenReturn(END_OF_WEEK);
            mockedSecurityUtils.when(SecurityUtils::getCurrentTenantIdAsLong).thenReturn(TENANT_ID);

            // When
            DashboardStatsResponseDto result = dashboardApplicationService.getDashboardStats();

            // Then
            assertNotNull(result);
            assertEquals(expectedTotalQuotations, result.getTotalQuotationRequestsThisWeek());
            assertEquals(expectedPricingQuotations, result.getQuotationRequestsPricingThisWeek());
            assertEquals(expectedInShippingOrders, result.getPurchaseOrdersInShipping());
            assertEquals(expectedCompletedOrders, result.getPurchaseOrdersCompletedThisWeek());

            // Verify repository calls
            verify(quotationAskRepository).countByTenantIdAndCreatedAtBetween(eq(TENANT_ID), eq(START_OF_WEEK), eq(END_OF_WEEK));
            verify(quotationAskRepository).countByTenantIdAndStatusAndCreatedAtBetween(eq(TENANT_ID), eq(QuotationStatus.PRICING), eq(START_OF_WEEK), eq(END_OF_WEEK));
            verify(purchaseOrderRepository).countByPurchaserIdAndStatus(eq(TENANT_ID), eq(POStatusEnum.IN_SHIPPING));
            verify(purchaseOrderRepository).countByPurchaserIdAndStatusAndUpdatedAtBetween(eq(TENANT_ID), eq(POStatusEnum.CLOSED), eq(START_OF_WEEK), eq(END_OF_WEEK));
        }
    }

    @Test
    void getDashboardStats_ShouldHandleZeroValues() {
        // Given - all counts return 0
        when(quotationAskRepository.countByTenantIdAndCreatedAtBetween(any(), any(), any())).thenReturn(0L);
        when(quotationAskRepository.countByTenantIdAndStatusAndCreatedAtBetween(any(), any(), any(), any())).thenReturn(0L);
        when(purchaseOrderRepository.countByPurchaserIdAndStatus(any(), any())).thenReturn(0L);
        when(purchaseOrderRepository.countByPurchaserIdAndStatusAndUpdatedAtBetween(any(), any(), any(), any())).thenReturn(0L);

        // Mock DateTimeUtils and SecurityUtils
        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
             MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {

            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentWeek).thenReturn(START_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentWeek).thenReturn(END_OF_WEEK);
            mockedSecurityUtils.when(SecurityUtils::getCurrentTenantIdAsLong).thenReturn(TENANT_ID);

            // When
            DashboardStatsResponseDto result = dashboardApplicationService.getDashboardStats();

            // Then
            assertNotNull(result);
            assertEquals(0L, result.getTotalQuotationRequestsThisWeek());
            assertEquals(0L, result.getQuotationRequestsPricingThisWeek());
            assertEquals(0L, result.getPurchaseOrdersInShipping());
            assertEquals(0L, result.getPurchaseOrdersCompletedThisWeek());
        }
    }
}
