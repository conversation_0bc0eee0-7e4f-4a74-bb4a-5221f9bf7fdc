package com.actechx.gf.app.service;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.response.SpendingOverviewResponseDto;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DashboardApplicationServiceSpendingTest {

    @Mock
    private QuotationAskRepository quotationAskRepository;

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @InjectMocks
    private StatisticsApplicationService dashboardApplicationService;

    private final Long TENANT_ID = 1L;
    private final Instant START_OF_WEEK = Instant.parse("2024-06-15T00:00:00Z");
    private final Instant END_OF_WEEK = Instant.parse("2024-06-21T23:59:59.999Z");
    private final Instant START_OF_MONTH = Instant.parse("2024-06-01T00:00:00Z");
    private final Instant END_OF_MONTH = Instant.parse("2024-06-30T23:59:59.999Z");
    private final Instant START_OF_YEAR = Instant.parse("2024-01-01T00:00:00Z");
    private final Instant END_OF_YEAR = Instant.parse("2024-12-31T23:59:59.999Z");

    @Test
    void getSpendingOverview_ShouldReturnCorrectSpending() {
        // Given
        BigDecimal weekSpending = new BigDecimal("15750000"); // 15.75 triệu
        BigDecimal monthSpending = new BigDecimal("125300000"); // 125.3 triệu
        BigDecimal yearSpending = new BigDecimal("2450000000"); // 2.45 tỷ

        // Mock repository responses
        when(purchaseOrderRepository.sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
                eq(TENANT_ID), eq(POStatusEnum.CLOSED), any(Instant.class), any(Instant.class)))
                .thenReturn(weekSpending) // week
                .thenReturn(monthSpending) // month
                .thenReturn(yearSpending); // year

        // Mock DateTimeUtils and SecurityUtils
        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
             MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentWeek).thenReturn(START_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentWeek).thenReturn(END_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentMonth).thenReturn(START_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentMonth).thenReturn(END_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentYear).thenReturn(START_OF_YEAR);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentYear).thenReturn(END_OF_YEAR);
            mockedDateTimeUtils.when(() -> DateTimeUtils.convertInstantToLocalDateTime(any(Instant.class)))
                .thenCallRealMethod();
            mockedSecurityUtils.when(SecurityUtils::getCurrentTenantIdAsLong).thenReturn(TENANT_ID);

            // When
            SpendingOverviewResponseDto result = dashboardApplicationService.getSpendingOverview();

            // Then
            assertNotNull(result);
            
            // Check original amounts
            assertEquals(weekSpending, result.getTotalSpentThisWeek());
            assertEquals(monthSpending, result.getTotalSpentThisMonth());
            assertEquals(yearSpending, result.getTotalSpentThisYear());
            
            // Check rounded amounts (should be rounded according to MoneyUtils rules)
            assertNotNull(result.getTotalSpentThisWeekRounded());
            assertNotNull(result.getTotalSpentThisMonthRounded());
            assertNotNull(result.getTotalSpentThisYearRounded());

            // Check display strings
            assertNotNull(result.getTotalSpentThisWeekDisplay());
            assertNotNull(result.getTotalSpentThisMonthDisplay());
            assertNotNull(result.getTotalSpentThisYearDisplay());
            
            // Check update time format
            assertNotNull(result.getUpdateTime());
            assertTrue(result.getUpdateTime().matches("\\d{2}/\\d{2}/\\d{4} - \\d{2}/\\d{2}/\\d{4}"));

            // Verify repository calls
            verify(purchaseOrderRepository, times(3)).sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
                    eq(TENANT_ID), eq(POStatusEnum.CLOSED), any(Instant.class), any(Instant.class));
        }
    }

    @Test
    void getSpendingOverview_ShouldHandleNullValues() {
        // Given - all amounts return null
        when(purchaseOrderRepository.sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
                any(), any(), any(), any())).thenReturn(null);

        // Mock DateTimeUtils and SecurityUtils
        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
             MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentWeek).thenReturn(START_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentWeek).thenReturn(END_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentMonth).thenReturn(START_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentMonth).thenReturn(END_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentYear).thenReturn(START_OF_YEAR);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentYear).thenReturn(END_OF_YEAR);
            mockedDateTimeUtils.when(() -> DateTimeUtils.convertInstantToLocalDateTime(any(Instant.class)))
                .thenCallRealMethod();
            mockedSecurityUtils.when(SecurityUtils::getCurrentTenantIdAsLong).thenReturn(TENANT_ID);

            // When
            SpendingOverviewResponseDto result = dashboardApplicationService.getSpendingOverview();

            // Then
            assertNotNull(result);
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisWeek());
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisMonth());
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisYear());
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisWeekRounded());
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisMonthRounded());
            assertEquals(BigDecimal.ZERO, result.getTotalSpentThisYearRounded());
            assertEquals("0đ", result.getTotalSpentThisWeekDisplay());
            assertEquals("0đ", result.getTotalSpentThisMonthDisplay());
            assertEquals("0đ", result.getTotalSpentThisYearDisplay());
        }
    }

    @Test
    void getSpendingOverview_ShouldHandleSmallAmounts() {
        // Given - amounts less than 1 million
        BigDecimal weekSpending = new BigDecimal("500000"); // 500k
        BigDecimal monthSpending = new BigDecimal("750000"); // 750k
        BigDecimal yearSpending = new BigDecimal("999999"); // 999k

        when(purchaseOrderRepository.sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
                eq(TENANT_ID), eq(POStatusEnum.CLOSED), any(Instant.class), any(Instant.class)))
                .thenReturn(weekSpending)
                .thenReturn(monthSpending)
                .thenReturn(yearSpending);

        // Mock DateTimeUtils and SecurityUtils
        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class);
             MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentWeek).thenReturn(START_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentWeek).thenReturn(END_OF_WEEK);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentMonth).thenReturn(START_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentMonth).thenReturn(END_OF_MONTH);
            mockedDateTimeUtils.when(DateTimeUtils::getStartOfCurrentYear).thenReturn(START_OF_YEAR);
            mockedDateTimeUtils.when(DateTimeUtils::getEndOfCurrentYear).thenReturn(END_OF_YEAR);
            mockedDateTimeUtils.when(() -> DateTimeUtils.convertInstantToLocalDateTime(any(Instant.class)))
                .thenCallRealMethod();
            mockedSecurityUtils.when(SecurityUtils::getCurrentTenantIdAsLong).thenReturn(TENANT_ID);

            // When
            SpendingOverviewResponseDto result = dashboardApplicationService.getSpendingOverview();

            // Then
            assertNotNull(result);
            
            // For amounts < 1 million, should not be rounded
            assertEquals(weekSpending, result.getTotalSpentThisWeekRounded());
            assertEquals(monthSpending, result.getTotalSpentThisMonthRounded());
            assertEquals(yearSpending, result.getTotalSpentThisYearRounded());
            
            // Check display strings
            assertEquals("500,000đ", result.getTotalSpentThisWeekDisplay());
            assertEquals("750,000đ", result.getTotalSpentThisMonthDisplay());
            assertEquals("999,999đ", result.getTotalSpentThisYearDisplay());
        }
    }
}
