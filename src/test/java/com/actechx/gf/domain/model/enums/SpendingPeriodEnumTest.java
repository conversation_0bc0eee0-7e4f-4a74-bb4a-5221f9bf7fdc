package com.actechx.gf.domain.model.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SpendingPeriodEnumTest {

    @Test
    void fromValue_ShouldReturnCorrectEnum_WhenValidValue() {
        // Given & When & Then
        assertEquals(SpendingPeriodEnum.THIS_WEEK, SpendingPeriodEnum.fromValue("this_week"));
        assertEquals(SpendingPeriodEnum.THIS_MONTH, SpendingPeriodEnum.fromValue("this_month"));
        assertEquals(SpendingPeriodEnum.LAST_6_MONTHS, SpendingPeriodEnum.fromValue("last_6_months"));
        assertEquals(SpendingPeriodEnum.LAST_YEAR, SpendingPeriodEnum.fromValue("last_year"));
    }

    @Test
    void fromValue_ShouldThrowException_WhenInvalidValue() {
        // Given
        String invalidValue = "invalid_period";

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> SpendingPeriodEnum.fromValue(invalidValue)
        );
        
        assertEquals("Invalid spending period: invalid_period", exception.getMessage());
    }

    @Test
    void getters_ShouldReturnCorrectValues() {
        // Test THIS_WEEK
        assertEquals("this_week", SpendingPeriodEnum.THIS_WEEK.getValue());
        assertEquals("Tuần này", SpendingPeriodEnum.THIS_WEEK.getDisplayName());
        assertEquals(7, SpendingPeriodEnum.THIS_WEEK.getDataPoints());

        // Test THIS_MONTH
        assertEquals("this_month", SpendingPeriodEnum.THIS_MONTH.getValue());
        assertEquals("Tháng này", SpendingPeriodEnum.THIS_MONTH.getDisplayName());
        assertEquals(4, SpendingPeriodEnum.THIS_MONTH.getDataPoints());

        // Test LAST_6_MONTHS
        assertEquals("last_6_months", SpendingPeriodEnum.LAST_6_MONTHS.getValue());
        assertEquals("6 tháng gần nhất", SpendingPeriodEnum.LAST_6_MONTHS.getDisplayName());
        assertEquals(6, SpendingPeriodEnum.LAST_6_MONTHS.getDataPoints());

        // Test LAST_YEAR
        assertEquals("last_year", SpendingPeriodEnum.LAST_YEAR.getValue());
        assertEquals("1 năm gần nhất", SpendingPeriodEnum.LAST_YEAR.getDisplayName());
        assertEquals(4, SpendingPeriodEnum.LAST_YEAR.getDataPoints());
    }

    @Test
    void values_ShouldReturnAllEnums() {
        // When
        SpendingPeriodEnum[] values = SpendingPeriodEnum.values();

        // Then
        assertEquals(4, values.length);
        assertEquals(SpendingPeriodEnum.THIS_WEEK, values[0]);
        assertEquals(SpendingPeriodEnum.THIS_MONTH, values[1]);
        assertEquals(SpendingPeriodEnum.LAST_6_MONTHS, values[2]);
        assertEquals(SpendingPeriodEnum.LAST_YEAR, values[3]);
    }
}
