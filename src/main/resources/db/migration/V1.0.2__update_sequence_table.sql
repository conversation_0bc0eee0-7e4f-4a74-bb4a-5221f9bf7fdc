DROP PROCEDURE IF EXISTS get_next_number(VARCHAR, BIGINT);


CREATE OR REPLACE FUNCTION get_next_number(schemaName VARCHAR(100), sequenceName VARCHAR(100))
RETURNS BIGINT
LANGUAGE plpgsql AS $$
DECLARE
    nextId BIGINT;
    sql_query TEXT;
    full_table_name TEXT;
BEGIN
    -- Approach 1: Thử update trước
    full_table_name := quote_ident(schemaName) || '.sequences';
    sql_query := format('UPDATE %s SET current_value = current_value + increment_by, updated_at = CURRENT_TIMESTAMP WHERE sequence_name = $1 RETURNING current_value', full_table_name);
    EXECUTE sql_query INTO nextId USING sequenceName;

    -- <PERSON><PERSON><PERSON> không có row nào được update (sequence chưa tồn tại)
    IF NOT FOUND THEN
        -- Insert sequence mới với handling conflict
        sql_query := format('INSERT INTO %s (sequence_name, current_value, increment_by, created_at, updated_at) VALUES ($1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) ON CONFLICT (sequence_name) DO UPDATE SET current_value = %s.current_value + %s.increment_by, updated_at = CURRENT_TIMESTAMP RETURNING current_value', full_table_name, full_table_name, full_table_name);
        EXECUTE sql_query INTO nextId USING sequenceName;
    END IF;

    RETURN nextId;
END; $$;