DROP FUNCTION IF EXISTS get_next_number(VARCHAR, VARCHA<PERSON>);

CREATE OR REPLACE FUNCTION get_next_number(schemaName VARCHAR(100), sequenceName VARCHAR(100))
RETURNS BIGINT
LANGUAGE plpgsql AS $$
DECLARE
    nextId BIGINT;
    full_table_name TEXT;
    update_sql TEXT;
    insert_sql TEXT;
BEGIN
    full_table_name := quote_ident(schemaName) || '.sequences';

    -- Step 1: Try to update current_value
    update_sql := format(
        'UPDATE %s
         SET current_value = current_value + increment_by,
             updated_at = CURRENT_TIMESTAMP
         WHERE sequence_name = $1
         RETURNING current_value',
         full_table_name
    );
    EXECUTE update_sql INTO nextId USING sequenceName;

    -- Step 2: If not found, try to insert (first-time use)
    IF nextId IS NULL THEN
            insert_sql := format(
                'INSERT INTO %s (sequence_name, current_value, increment_by, created_at, updated_at)
                 VALUES ($1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                 ON CONFLICT (sequence_name) DO NOTHING',
                full_table_name
            );
        EXECUTE insert_sql USING sequenceName;
        nextId := 1;
    END IF;

RETURN nextId;
END;
$$;
