CREATE TABLE sequences (
   sequence_name VARCHAR(100) PRIMARY KEY,
   current_value BIGINT NOT NULL DEFAULT 0,
   increment_by INTEGER NOT NULL DEFAULT 1,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE OR REPLACE PROCEDURE get_next_number(
    IN sequenceName VARCHAR(100),
    OUT nextId BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Approach 1: Thử update trước
    UPDATE sequences
    SET current_value = current_value + increment_by,
        updated_at = CURRENT_TIMESTAMP
    WHERE sequence_name = sequenceName
    RETURNING current_value INTO nextId;

    -- Nếu không có row nào được update (sequence chưa tồn tại)
    IF NOT FOUND THEN
        -- Insert sequence mới với handling conflict
        INSERT INTO sequences (sequence_name, current_value, increment_by, created_at, updated_at)
        VALUES (sequenceName, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (sequence_name) DO UPDATE
          SET current_value = sequences.current_value + sequences.increment_by,
            updated_at = CURRENT_TIMESTAMP
          RETURNING current_value INTO nextId;
    END IF;

END;
$$;

-- Create index for performance
CREATE INDEX idx_sequences_sequence_name ON sequences(sequence_name);