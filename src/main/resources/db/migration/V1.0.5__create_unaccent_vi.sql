CREATE OR REPLACE FUNCTION unaccent_vi(input TEXT)
RETURNS TEXT AS $$
BEGIN
    -- T<PERSON><PERSON> về NULL nếu input là NULL
    IF input IS NULL THEN
        RETURN NULL;
END IF;

    -- Trả về chuỗi rỗng nếu input là ''
IF input = '' THEN
        RETURN '';
END IF;

    -- Thay dấu tiếng Việt
RETURN translate(input,
        'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ' ||
        'ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ',
        'aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyyd' ||
        'AAAAAAAAAAAAAAAAAEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYYD');
END;
$$ LANGUAGE plpgsql IMMUTABLE;
