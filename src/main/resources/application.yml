server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}

aws:
  region: ${AWS_REGION:ap-southeast-1}

spring:
  application:
    name: gf-purchase
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:postgres}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    driverClassName: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        temp.use_jdbc_metadata_defaults: false
        default_schema: ${DB_SCHEMA:dev-gf-purchase}
    open-in-view: false
  flyway:
    enabled: true
    validate-on-migrate: true
    baseline-on-migrate: true
    schemas: ${DB_SCHEMA:dev-gf-purchase}
    default-schema: ${DB_SCHEMA:dev-gf-purchase}

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

internal-service.api-key: ${INTERNAL_API_KEY:}

tenant-service:
  url: ${TENANT_SERVICE_URL:https://swagger-dev.nonprod.aggregatoricapaci.com/ct-saas-tenant}
agent-service:
  url: ${GF_ERP_AGENT_URL:http://dev-gfds-gf-erp-agent.dev-saas-app.svc.cluster.local:8080/gf-erp-agent}
cms-service:
  url: ${CMS_URL:https://swagger-dev.nonprod.aggregatoricapaci.com/cp-cms-index}
gf-inventory:
  url: ${GF_INVENTORY_URL:https://swagger-dev.nonprod.cardoctor.com.vn/gf-inventory}

# Management endpoints
management:
  #  server.port: ${HEALTH_PORT:8079}
  tracing:
    enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  #      base-path: /
  endpoint:
    health:
      show-details: when-authorized
  prometheus:
    metrics:
      export:
        enabled: true

