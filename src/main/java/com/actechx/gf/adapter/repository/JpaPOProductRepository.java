package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JpaPOProductRepository extends JpaRepository<POProductEntity, Long> {
  List<POProductEntity> findByPoId(Long poId);

  @Query(
      "Select pod from POProductEntity pod join PurchaseOrderEntity po on pod.poId = po.id where po.prId =:prId")
  List<POProductEntity> findByPrId(@Param("prId") Long prId);
}
