package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.controller.form.response.BidCount;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaQuotationBidRepository extends JpaRepository<QuotationBidEntity, Long> {

  List<QuotationBidEntity> findByQuotationAskCode(String quotationAskCode);

  Optional<QuotationBidEntity> findByQuotationAskCodeAndTenantId(
      String quotationAskCode, Long tenantId);

  @Query(
      "SELECT qb.quotationAskCode AS quotationAskCode, COUNT(qb) AS count "
          + "FROM QuotationBidEntity qb "
          + "WHERE qb.quotationAskCode IN :quotationAskCodes "
          + "GROUP BY qb.quotationAskCode")
  List<BidCount> getBidCountByQuotationAskCode(
      @Param("quotationAskCodes") List<String> quotationAskCodes);

  @Query(
      """
        SELECT q.quotationAskCode AS quotationAskCode, COUNT(q) AS count
        FROM QuotationBidEntity q
        WHERE q.quotationAskCode IN :quotationAskCodes
          AND q.status = :status
        GROUP BY q.quotationAskCode
    """)
  List<BidCount> getBidCountByQuotationAskCodeAndStatus(
      @Param("quotationAskCodes") List<String> quotationAskCodes,
      @Param("status") QuotationBidStatus status);

  @Modifying
  @Query(
      "UPDATE QuotationBidEntity e SET e.status = :status WHERE e.tenantId=:tenantId AND e.quotationAskCode=:quotationAskCode")
  int updateQuotationBidStatus(Long tenantId, String quotationAskCode, QuotationBidStatus status);
}
