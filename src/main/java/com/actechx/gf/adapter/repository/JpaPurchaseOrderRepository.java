package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JpaPurchaseOrderRepository
    extends JpaRepository<PurchaseOrderEntity, Long>,
        JpaSpecificationExecutor<PurchaseOrderEntity> {

  @Query(
"""
    SELECT po
    FROM PurchaseOrderEntity po
    JOIN PurchaseRequestEntity pr
      ON pr.id = po.prId
    WHERE pr.code = :purchaseRequestCode
      AND po.supplierId = :supplierId
""")
  Optional<PurchaseOrderEntity> findByPurchaseRequestCodeAndSupplierId(
      @Param("purchaseRequestCode") String purchaseRequestCode,
      @Param("supplierId") Long supplierId);

  List<PurchaseOrderEntity> findByPrIdAndPurchaserId(Long prId, Long purchaserId);

  Optional<PurchaseOrderEntity> findByIdAndPurchaserId(Long id, Long purchaserId);

  Optional<PurchaseOrderEntity> findByCodeAndPurchaserId(String code, Long purchaserId);

  List<PurchaseOrderEntity> findByCodeIn(List<String> codes);

  List<PurchaseOrderEntity> findByCodeInOrSaleOrderCodeIn(
      List<String> codes, List<String> saleOrderCodes);

  @Query(
      """
          SELECT po
          FROM PurchaseOrderEntity po
          JOIN PurchaseRequestEntity pr
            ON pr.id = po.prId
          WHERE pr.code = :purchaseRequestCode
      """)
  List<PurchaseOrderEntity> findByPurchaseRequestCode(String purchaseRequestCode);

  Long countByPurchaserIdAndStatus(Long purchaserId, POStatusEnum status);

  Long countByPurchaserIdAndStatusAndUpdatedAtBetween(Long purchaserId, POStatusEnum status, Instant startTime, Instant endTime);

  @Query("""
      SELECT COALESCE(SUM(p.quantity * p.unitPrice), 0)
      FROM POProductEntity p
      JOIN PurchaseOrderEntity po ON p.poId = po.id
      WHERE po.purchaserId = :purchaserId
        AND po.status = :status
        AND po.updatedAt BETWEEN :startTime AND :endTime
      """)
  BigDecimal sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
      @Param("purchaserId") Long purchaserId,
      @Param("status") POStatusEnum status,
      @Param("startTime") Instant startTime,
      @Param("endTime") Instant endTime);
}
