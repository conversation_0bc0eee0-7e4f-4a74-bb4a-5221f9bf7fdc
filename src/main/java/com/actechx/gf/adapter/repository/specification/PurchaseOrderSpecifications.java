package com.actechx.gf.adapter.repository.specification;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaserOrderRequestDto;
import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.domain.model.enums.POStage;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PurchaseOrderSpecifications {

  public static Specification<PurchaseOrderEntity> withFilters(
      SearchPurchaserOrderRequestDto request) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    return (root, query, cb) -> {
      assert query != null;
      query.distinct(true);

      var predicates = cb.conjunction();
      predicates = cb.and(predicates, cb.equal(root.get("purchaserId"), purchaserId));

      // Lọc theo status (bắt buộc)
      POStage stage = request.getStage();
      if (stage != null) {
        predicates = cb.and(predicates, cb.equal(root.get("stage"), stage));
      }

      String dataSearch = request.getDataSearch();
      // Nếu có dataSearch thì tìm ở cả 3 trường
      if (StringUtils.isNotBlank(dataSearch)) {
        String keyword = "%" + dataSearch.trim().toLowerCase() + "%";

        // Subquery lọc bảng con
        var subquery = query.subquery(Long.class);
        var child = subquery.from(POProductEntity.class);
        subquery.select(child.get("poId"));

        var childPredicate = cb.or(cb.like(cb.lower(child.get("requestedProductName")), keyword));
        subquery.where(childPredicate);

        // OR giữa bảng cha và bảng con
        predicates =
            cb.and(
                predicates,
                cb.or(
                    cb.like(cb.lower(root.get("code")), keyword),
                    cb.like(cb.lower(root.get("quotationAskCode")), keyword),
                    cb.in(root.get("id")).value(subquery)));
      }

      return predicates;
    };
  }
}
