package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface JpaAskedSparePartRepository extends JpaRepository<AskedSparePartEntity, Long> {
  List<AskedSparePartEntity> findByCodeIn(List<String> codes);

  Optional<AskedSparePartEntity> findByCode(String code);

  @Query("SELECT e.id FROM AskedSparePartEntity e WHERE e.id IN :ids")
  List<Long> findExistingIdsByIds(List<Long> ids);

  Optional<AskedSparePartEntity> findByIdAndTenantIdAndQuotationAskId(
      Long id, Long tenantId, Long quotationId);
}
