package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaPurchaseRequestDataEntityRepository
    extends JpaRepository<PurchaseRequestDataEntity, Long> {
  List<PurchaseRequestDataEntity> findByPurchaseRequestId(Long purchaseRequestId);

  void deleteByPurchaseRequestId(Long purchaseRequestId);
}
