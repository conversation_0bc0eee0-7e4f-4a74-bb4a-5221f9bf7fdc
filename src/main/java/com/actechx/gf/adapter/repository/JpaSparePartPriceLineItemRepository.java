package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaSparePartPriceLineItemRepository
    extends JpaRepository<SparePartPriceLineItemEntity, Long> {
  @Modifying
  @Query(
      "UPDATE SparePartPriceLineItemEntity e SET e.detailStatus = true WHERE e.id IN :ids AND e.detailStatus=false")
  int updateDetailStatusByIds(List<Long> ids);

  @Query(
      "SELECT e.id FROM SparePartPriceLineItemEntity e WHERE e.sparePartInputCode=:sparePartInputCode AND e.quotationBid.quotationAskCode=:quotationAskCode")
  List<Long> findBySparePartInputCodeAndQuotationCode(
      String sparePartInputCode, String quotationAskCode);

  List<SparePartPriceLineItemEntity> findBySparePartInputCodeOrderBySegmentAscPriceAsc(String code);

  List<SparePartPriceLineItemEntity> findByIdInAndDetailStatusFalseAndQuotationBid_StatusIn(
      List<Long> ids, List<QuotationBidStatus> statuses);

  Optional<SparePartPriceLineItemEntity> findByIdAndDetailStatusFalseAndDeletedFalse(Long id);

  Optional<SparePartPriceLineItemEntity> findByIdAndDetailStatusTrueAndDeletedFalse(Long id);

  Optional<SparePartPriceLineItemEntity>
      findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
          String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId);
}
