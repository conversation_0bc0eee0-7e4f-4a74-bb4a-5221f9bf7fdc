package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaQuotationAskPricingProposalRepository
    extends JpaRepository<QuotationAskPricingProposalEntity, Long> {
  Optional<QuotationAskPricingProposalEntity> findByQuotationAskCodeAndReplyTenantId(
      String quotationAskCode, Long replyTenantId);
}
