package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaPurchaseRequestEntityRepository
    extends JpaRepository<PurchaseRequestEntity, Long>,
        JpaSpecificationExecutor<PurchaseRequestEntity> {
  Optional<PurchaseRequestEntity> findByCode(String code);

  Optional<PurchaseRequestEntity> findByCodeAndPurchaserId(String code, Long purchaserId);

  Optional<PurchaseRequestEntity> findByIdAndPurchaserId(Long id, Long purchaserId);
}
