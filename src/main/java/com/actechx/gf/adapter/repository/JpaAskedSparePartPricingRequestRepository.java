package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingRequestEntity;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaAskedSparePartPricingRequestRepository
    extends JpaRepository<AskedSparePartPricingRequestEntity, Long> {
  List<AskedSparePartPricingRequestEntity>
      findByTenantIdAndCodeInAndProposalStatusFalseAndQuotationAskPricingQuotationAskCode(
          Long tenantId, List<String> codes, String quotationAskCode);

  Optional<AskedSparePartPricingRequestEntity>
      findByCodeAndTenantIdAndSegmentAndProposalStatusFalseAndQuotationAskPricing_QuotationAskCode(
          String code, Long tenantId, Segment segment, String quotationAskCode);

  Optional<AskedSparePartPricingRequestEntity>
      findByCodeAndTenantIdAndSegmentAndProposalStatusTrueAndQuotationAskPricing_QuotationAskCodeAndRefCode(
          String code, Long tenantId, Segment segment, String quotationAskCode, String refCode);
}
