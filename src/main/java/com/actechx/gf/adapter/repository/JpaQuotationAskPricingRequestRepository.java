package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingRequestEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaQuotationAskPricingRequestRepository
    extends JpaRepository<QuotationAskPricingRequestEntity, Long> {
  Optional<QuotationAskPricingRequestEntity> findByQuotationAskCode(String code);
}
