package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.cart.CartEntity;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaCartRepository extends JpaRepository<CartEntity, Long> {
  Optional<CartEntity> findByProductIdAndQuotationAskIdAndPurchaserIdAndSupplierIdAndDeletedFalse(
      Long productId, Long quotationAskId, Long purchaseId, Long supplierId);

  List<CartEntity> findByIdIn(Collection<Long> ids);

  List<CartEntity> findByPurchaserIdAndDeletedFalse(Long purchaserId);

  List<CartEntity> findByIdInAndPurchaserIdAndDeletedFalse(Set<Long> ids, Long purchaserId);

  Optional<CartEntity> findByIdAndPurchaserIdAndDeletedFalse(Long id, Long purchaserId);
}
