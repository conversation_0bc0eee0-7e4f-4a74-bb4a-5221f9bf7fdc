package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaQuotationAskRepository
    extends JpaRepository<QuotationAskEntity, Long>, JpaSpecificationExecutor<QuotationAskEntity> {

  Optional<QuotationAskEntity> findByCode(String code);

  Optional<QuotationAskEntity> findByCodeAndTenantId(String code, Long tenantId);

  Optional<QuotationAskEntity> findByIdAndTenantId(Long id, Long tenantId);

  @Modifying
  @Query("UPDATE QuotationAskEntity e SET e.status = :status WHERE e.code=:code")
  int updateQuotationStatus(String code, QuotationStatus status);
}
