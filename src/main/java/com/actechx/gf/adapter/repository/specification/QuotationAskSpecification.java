package com.actechx.gf.adapter.repository.specification;

import com.actechx.common.spring.jpa.BaseSpecification;
import com.actechx.gf.adapter.controller.form.request.SearchQuotationAskRequestDto;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import org.springframework.data.jpa.domain.Specification;

public class QuotationAskSpecification extends BaseSpecification<QuotationAskEntity> {

  public static Specification<QuotationAskEntity> withFilters(
      SearchQuotationAskRequestDto searchRequest) {
    return (root, query, cb) -> {
      QuotationAskSpecification spec = new QuotationAskSpecification();
      spec.addEqualPredicateIfNotNull(searchRequest.getTenantId(), "tenantId", cb, root);
      spec.addEqualPredicateIfNotNull(searchRequest.getStatus(), "status", cb, root);
      return cb.and(spec.getPredicatesArray());
    };
  }
}
