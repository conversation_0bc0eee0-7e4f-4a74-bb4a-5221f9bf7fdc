package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingProposalEntity;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaAskedSparePartPricingProposalRepository
    extends JpaRepository<AskedSparePartPricingProposalEntity, Long> {
  List<AskedSparePartPricingProposalEntity>
      findBySparePartInputCodeAndSegmentAndQuotationAskPricingProposal_QuotationAskCodeAndQuotationAskPricingProposal_ReplyTenantId(
          String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId);
}
