package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.order.POTransitionHistoryEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaPOTransitionHistoryRepository
    extends JpaRepository<POTransitionHistoryEntity, Long> {
  Optional<POTransitionHistoryEntity> findTopByPoIdOrderByCreatedAtDesc(Long poId);
}
