package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.purchase.request.PRTransitionHistoryEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaPRTransitionHistoryRepository
    extends JpaRepository<PRTransitionHistoryEntity, Long> {
  Optional<PRTransitionHistoryEntity> findTopByPrIdOrderByCreatedAtDesc(Long prId);
}
