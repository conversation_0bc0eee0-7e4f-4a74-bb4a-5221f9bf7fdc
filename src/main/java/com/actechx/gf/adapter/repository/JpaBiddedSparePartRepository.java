package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaBiddedSparePartRepository extends JpaRepository<BiddedSparePartEntity, Long> {
  Optional<BiddedSparePartEntity> findByCode(String code);

  Optional<BiddedSparePartEntity> findByPartNameInputAndTenantIdAndQuotationBid_QuotationAskCode(
      String name, Long tenantId, String quotationAskCode);
}
