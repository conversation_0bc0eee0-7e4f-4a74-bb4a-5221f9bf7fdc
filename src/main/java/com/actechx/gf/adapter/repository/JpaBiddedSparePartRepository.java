package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaBiddedSparePartRepository extends JpaRepository<BiddedSparePartEntity, Long> {
  Optional<BiddedSparePartEntity> findByCodeAndDeletedFalse(String code);

  Optional<BiddedSparePartEntity>
      findByPartNameInputAndPartNameUnitAndTenantIdAndQuotationBid_QuotationAskCodeAndDeletedFalse(
          String name, String unit, Long tenantId, String quotationAskCode);
}
