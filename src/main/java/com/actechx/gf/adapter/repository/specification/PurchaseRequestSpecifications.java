package com.actechx.gf.adapter.repository.specification;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaseRequestRequestDto;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PurchaseRequestSpecifications {

  public static Specification<PurchaseRequestEntity> withFilters(
      SearchPurchaseRequestRequestDto request) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    return (root, query, cb) -> {
      assert query != null;
      query.distinct(true);

      var predicates = cb.conjunction();
      predicates = cb.and(predicates, cb.equal(root.get("purchaserId"), purchaserId));

      // Lọc theo status (bắt buộc)
      PurchaseRequestStatus status = request.getStatus();
      if (status != null) {
        if (status == PurchaseRequestStatus.INSUFFICIENT_QUANTITY
            || status == PurchaseRequestStatus.INSUFFICIENT_PRODUCT) {
          predicates =
              cb.and(
                  predicates,
                  root.get("status")
                      .in(
                          PurchaseRequestStatus.INSUFFICIENT_QUANTITY,
                          PurchaseRequestStatus.INSUFFICIENT_PRODUCT));
        } else {
          predicates = cb.and(predicates, cb.equal(root.get("status"), status));
        }
      }

      String dataSearch = request.getDataSearch();
      // Nếu có dataSearch thì tìm ở cả 3 trường
      if (StringUtils.isNotBlank(dataSearch)) {
        String keyword = "%" + dataSearch.trim().toLowerCase() + "%";

        // Subquery lọc bảng con
        var subquery = query.subquery(Long.class);
        var child = subquery.from(PurchaseRequestDataEntity.class);
        subquery.select(child.get("purchaseRequestId"));

        var childPredicate =
            cb.or(
                cb.like(cb.lower(child.get("requestedProductName")), keyword),
                cb.like(cb.lower(child.get("quotationAskCode")), keyword));
        subquery.where(childPredicate);

        // OR giữa bảng cha và bảng con
        predicates =
            cb.and(
                predicates,
                cb.or(
                    cb.like(cb.lower(root.get("code")), keyword),
                    cb.in(root.get("id")).value(subquery)));
      }

      return predicates;
    };
  }
}
