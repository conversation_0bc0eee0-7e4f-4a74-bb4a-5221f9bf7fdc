package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface JpaAddedSparePartPriceLineItemRepository
    extends JpaRepository<AddedSparePartPriceLineItemEntity, Long> {
  List<AddedSparePartPriceLineItemEntity> findByIdInAndDetailStatusFalseAndQuotationBid_StatusIn(
      List<Long> ids, List<QuotationBidStatus> statuses);

  Optional<AddedSparePartPriceLineItemEntity> findByIdAndDetailStatusFalse(Long id);

  Optional<AddedSparePartPriceLineItemEntity>
      findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
          String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId);
}
