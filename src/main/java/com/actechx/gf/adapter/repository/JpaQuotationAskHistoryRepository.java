package com.actechx.gf.adapter.repository;

import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaQuotationAskHistoryRepository
    extends JpaRepository<QuotationAskHistoryEntity, Long>,
        JpaSpecificationExecutor<QuotationAskHistoryEntity> {
  Page<QuotationAskHistoryEntity> findByQuotationAskCodeOrderByCreatedAtDesc(
      String quotationAskCode, Pageable pageable);

  Page<QuotationAskHistoryEntity> findByQuotationAskCodeAndUpdatedByTenantIdOrderByCreatedAtDesc(
      String quotationAskCode, Long tenantId, Pageable pageable);
}
