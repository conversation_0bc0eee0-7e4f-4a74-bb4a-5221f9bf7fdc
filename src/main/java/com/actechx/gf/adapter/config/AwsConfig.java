// package com.actechx.gf.adapter.config;
//
// import com.amazonaws.auth.AWSCredentialsProvider;
// import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
// import com.amazonaws.regions.Regions;
// import com.amazonaws.services.sns.AmazonSNS;
// import com.amazonaws.services.sns.AmazonSNSClientBuilder;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class AwsConfig {
//
//  @Value("${aws.region}")
//  private String awsRegion;
//
//  @Bean
//  public AWSCredentialsProvider awsCredentialsProvider() {
//    return DefaultAWSCredentialsProviderChain.getInstance();
//  }
//
//  @Bean
//  public AmazonSNS amazonSNS(AWSCredentialsProvider credentialsProvider) {
//    return AmazonSNSClientBuilder.standard()
//        .withCredentials(credentialsProvider)
//        .withRegion(Regions.fromName(awsRegion))
//        .build();
//  }
// }
