package com.actechx.gf.adapter.config;

import com.actechx.common.security.utils.SecurityUtils;
import java.util.Optional;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaConfig {
  @Bean
  public AuditorAware<String> auditorProvider() {
    return new SpringSecurityAuditorAware();
  }

  public static class SpringSecurityAuditorAware implements AuditorAware<String> {
    @Override
    public Optional<String> getCurrentAuditor() {
      try {
        String username = SecurityUtils.getCurrentUserId();
        return Optional.ofNullable(username);
      } catch (Exception e) {
        // Fallback to system user if JWT parsing fails
        return Optional.of("system");
      }
    }
  }
}
