package com.actechx.gf.adapter.config;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.client.AgentClient;
import com.actechx.gf.adapter.client.CmsClient;
import com.actechx.gf.adapter.client.GFInventoryClient;
import com.actechx.gf.adapter.client.TenantClient;
import com.actechx.gf.adapter.controller.error.GenericResponseErrorHandlerFactory;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

@RequiredArgsConstructor
@Configuration
public class HttpClientConfig {

  @Value("${tenant-service.url}")
  private String tenantServiceBaseUrl;

  @Value("${agent-service.url}")
  private String agentServiceBaseUrl;

  @Value("${cms-service.url}")
  private String cmsServiceBaseUrl;

  @Value("${gf-inventory.url}")
  private String gfInventoryServiceBaseUrl;

  @Value("${internal-service.api-key}")
  private String internalApiKey;

  private final GenericResponseErrorHandlerFactory handlerFactory;

  @Bean
  public TenantClient tenantClient() {
    return createClient(TenantClient.class, "tenant-client", tenantServiceBaseUrl, true);
  }

  @Bean
  public AgentClient agentClient() {
    return createClient(AgentClient.class, "aggent-client", agentServiceBaseUrl, false);
  }

  @Bean
  public CmsClient cmsClient() {
    return createClient(CmsClient.class, "cms-client", cmsServiceBaseUrl, false);
  }

  @Bean
  public GFInventoryClient geInventoryClient() {
    return createClient(
        GFInventoryClient.class, "gf-inventory-client", gfInventoryServiceBaseUrl, false);
  }

  private <T> T createClient(
      Class<T> clientClass,
      String clientName,
      String baseUrl,
      boolean forwardAuthenticationHeader) {
    var builder =
        RestClient.builder()
            .baseUrl(baseUrl)
            .defaultStatusHandler(
                HttpStatusCode::isError,
                (request, response) ->
                    handlerFactory
                        .create(clientName)
                        .handleError(request.getURI(), request.getMethod(), response))
            .defaultHeader("Content-Type", "application/json")
            .defaultHeader("Accept", "application/json")
            .defaultHeader("x-api-key", internalApiKey);

    // Add custom headers
    if (forwardAuthenticationHeader) {
      builder.requestInterceptor(this::addAuthenticationHeader);
    }

    HttpServiceProxyFactory factory =
        HttpServiceProxyFactory.builderFor(RestClientAdapter.create(builder.build())).build();

    return factory.createClient(clientClass);
  }

  private ClientHttpResponse addAuthenticationHeader(
      HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
    String token = SecurityUtils.getCurrentJwtToken();

    if (StringUtils.hasText(token)) {
      request.getHeaders().add("Authorization", "Bearer " + token);
    }
    return execution.execute(request, body);
  }
}
