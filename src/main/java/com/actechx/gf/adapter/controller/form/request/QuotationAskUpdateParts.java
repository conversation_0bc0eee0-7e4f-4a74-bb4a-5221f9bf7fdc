package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.CarType;
import com.actechx.gf.domain.model.enums.OwnerType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuotationAskUpdateParts {
  @Valid private AddOrDeleteParts addParts;

  @Valid private UpdateParts updateParts;

  @Valid private AddOrDeleteParts deleteParts;

  @NotNull private OwnerType updatedRole;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class AddOrDeleteParts {
    @Valid private List<Attachment> attachments;
    @Valid private List<SparePart> spareParts;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class UpdateParts {
    @Valid private Vehicle vehicle;
    @Valid private List<Attachment> attachments;
    @Valid private List<SparePart> spareParts;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Attachment {
    @NotBlank private String attachmentUrl;
    @NotNull private OwnerType owner;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class SparePart {
    @NotBlank private String code;
    @NotBlank private String partNameInput;
    @NotBlank private String partNameUnit;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Vehicle {
    @NotBlank private String carBrand;
    @NotBlank private String carModel;
    @NotBlank private String yearOfManufacture;
    @NotNull private CarType carType;
    @NotBlank private String trimsLevel;

    @Size(max = 17)
    private String vin;
  }
}
