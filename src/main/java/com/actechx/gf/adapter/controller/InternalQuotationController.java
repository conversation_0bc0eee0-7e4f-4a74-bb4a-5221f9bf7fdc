package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.request.CreateQuotationBidDto;
import com.actechx.gf.adapter.controller.form.request.QuotationAskPricingProposalRequest;
import com.actechx.gf.adapter.controller.form.request.UpdateQuotationAskDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingProposalResponse;
import com.actechx.gf.adapter.controller.form.response.QuotationAskResponseDto;
import com.actechx.gf.adapter.controller.form.response.QuotationBidResponseDto;
import com.actechx.gf.app.service.QuotationAskApplicationService;
import com.actechx.gf.app.service.QuotationAskPricingApplicationService;
import com.actechx.gf.app.service.QuotationBidApplicationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/protected/v1")
@RequiredArgsConstructor
public class InternalQuotationController {
  private final QuotationAskApplicationService quotationAskApplicationService;
  private final QuotationBidApplicationService quotationBidApplicationService;
  private final QuotationAskPricingApplicationService quotationAskPricingApplicationService;

  @PutMapping("/quotation-asks")
  public ResponseEntity<ApiResponse<QuotationAskResponseDto>> updateQuotationAsk(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody UpdateQuotationAskDto updateQuotationAskDto) {
    QuotationAskResponseDto response =
        quotationAskApplicationService.updateQuotationAsk(updateQuotationAskDto);
    return ResponseEntity.status(HttpStatus.OK).body(ApiResponse.success(response));
  }

  @PostMapping("/quotation-bids")
  public ResponseEntity<ApiResponse<QuotationBidResponseDto>> createQuotationBid(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody CreateQuotationBidDto createQuotationBidDto) {
    QuotationBidResponseDto response =
        quotationBidApplicationService.createQuotationBid(createQuotationBidDto);
    return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success(response));
  }

  @PostMapping("/pricing-proposals")
  public ResponseEntity<ApiResponse<QuotationAskPricingProposalResponse>>
      quotationAskPricingProposalClientController(
          @RequestHeader(name = "x-api-key") String apiKey,
          @Valid @RequestBody QuotationAskPricingProposalRequest request) {
    log.info("quotationAskPricingProposalClientController: {}", request);
    QuotationAskPricingProposalResponse response =
        quotationAskPricingApplicationService.quotationAskPricingProposalService(request);
    return ResponseEntity.ok().body(ApiResponse.success(response));
  }
}
