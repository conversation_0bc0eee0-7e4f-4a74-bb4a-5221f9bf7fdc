package com.actechx.gf.adapter.controller.error;

import com.actechx.common.dto.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
  // Handling generic BadRequest errors (400) globally
  @ExceptionHandler({IllegalArgumentException.class, IllegalStateException.class})
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ResponseBody
  public ResponseEntity<ErrorResponse> handleBadRequest(Exception ex) {
    // Custom error message for bad requests
    log.debug("Bad request: {}", ex.getMessage(), ex);
    ErrorResponse apiError = new ErrorResponse();
    return new ResponseEntity<>(apiError, HttpStatus.BAD_REQUEST);
  }
}
