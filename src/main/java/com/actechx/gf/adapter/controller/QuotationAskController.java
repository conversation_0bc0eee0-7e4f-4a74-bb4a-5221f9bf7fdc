package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.PagedApiResponse;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.request.CreateQuotationAskDto;
import com.actechx.gf.adapter.controller.form.request.SearchQuotationAskHistoryRequestDto;
import com.actechx.gf.adapter.controller.form.request.SearchQuotationAskRequestDto;
import com.actechx.gf.adapter.controller.form.request.SearchSparePartRequestDto;
import com.actechx.gf.adapter.controller.form.response.*;
import com.actechx.gf.app.service.CommonService;
import com.actechx.gf.app.service.QuotationAskApplicationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/quotation-asks")
@RequiredArgsConstructor
public class QuotationAskController {

  private final QuotationAskApplicationService quotationAskApplicationService;
  private final CommonService commonService;

  @PostMapping
  public ResponseEntity<ApiResponse<QuotationAskResponseDto>> createQuotationAskController(
      @Valid @RequestBody CreateQuotationAskDto request) {
    log.info("createQuotationAskController: {}", request);
    QuotationAskResponseDto response =
        quotationAskApplicationService.createQuotationAskService(request);
    return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success(response));
  }

  @GetMapping("/{id}")
  public ResponseEntity<ApiResponse<DetailQuotationAskResponseDto>> getQuotationAskByIdController(
      @PathVariable Long id) {
    log.info("getQuotationAskByIdController: {}", id);
    DetailQuotationAskResponseDto response = quotationAskApplicationService.getByIdService(id);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping("/detail/{code}")
  public ResponseEntity<ApiResponse<DetailQuotationAskResponseDto>> getQuotationAskByCodeController(
      @PathVariable String code) {
    log.info("getQuotationAskByCodeController: {}", code);
    DetailQuotationAskResponseDto response = quotationAskApplicationService.getByCodeService(code);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping
  public ResponseEntity<PagedApiResponse<SearchQuotationAskResponseDto>> getQuotationAsks(
      @ModelAttribute SearchQuotationAskRequestDto request) {
    Page<SearchQuotationAskResponseDto> responses = quotationAskApplicationService.search(request);
    return ResponseEntity.ok(ResponseUtil.successPaged(responses));
  }

  @GetMapping("/spare-parts")
  public ResponseEntity<PagedApiResponse<SearchSparePartResponseDto>> getSpareParts(
      @ModelAttribute SearchSparePartRequestDto request) {
    Page<SearchSparePartResponseDto> responses =
        quotationAskApplicationService.searchSparePart(request);
    return ResponseEntity.ok(ResponseUtil.successPaged(responses));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> cancelQuotationAskById(@PathVariable Long id) {
    quotationAskApplicationService.cancelById(id);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/{quotationAskCode}/histories")
  public ResponseEntity<PagedApiResponse<QuotationAskHistoryResponseDto>>
      getQuotationAskHistoryById(
          @PathVariable String quotationAskCode,
          @ModelAttribute SearchQuotationAskHistoryRequestDto request) {
    Page<QuotationAskHistoryResponseDto> responses =
        quotationAskApplicationService.getHistory(quotationAskCode, request);
    return ResponseEntity.ok(ResponseUtil.successPaged(responses));
  }
}
