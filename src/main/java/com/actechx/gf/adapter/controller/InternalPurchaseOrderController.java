package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.request.PurchaseRequestTinyRequestDto;
import com.actechx.gf.adapter.controller.form.request.UpdatePurchaseOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.DeliveringPurchaseOrderRequest;
import com.actechx.gf.adapter.controller.form.request.purchase.UpdatePurchaseRequestStatusRequest;
import com.actechx.gf.adapter.controller.form.response.PurchaseOrderResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseRequestTinyResponseDto;
import com.actechx.gf.app.service.purchase.PurchaseOrderApplicationService;
import com.actechx.gf.app.service.purchase.PurchaseRequestApplicationService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/protected/v1")
@RequiredArgsConstructor
public class InternalPurchaseOrderController {
  private final PurchaseRequestApplicationService purchaseRequestApplicationService;

  private final PurchaseOrderApplicationService purchaseOrderApplicationService;

  @PutMapping("/purchase-orders")
  public ResponseEntity<ApiResponse<PurchaseOrderResponseDto>> updatePurchaseOrder(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody UpdatePurchaseOrderRequestDto request) {
    log.info("UpdatePurchaseOrderRequest: {}", request);
    PurchaseOrderResponseDto response =
        purchaseOrderApplicationService.updatePurchaseOrderApplication(request);
    log.info("UpdatePurchaseOrderResponse: {}", response);
    return ResponseEntity.ok().body(ApiResponse.success(response));
  }

  @PutMapping("/purchase-requests/status")
  public ResponseEntity<ApiResponse<String>> updatePurchaseRequestStatus(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody UpdatePurchaseRequestStatusRequest request) {
    log.info("updatePurchaseRequestStatusRequest: {}", request);
    purchaseRequestApplicationService.updateStatus(request);
    log.info("updatePurchaseRequestStatusResponse: OK");
    return ResponseEntity.ok().body(ApiResponse.success("OK"));
  }

  @PutMapping("/purchase-orders/stage")
  public ResponseEntity<ApiResponse<String>> updatePurchaseOrderStage(
      @RequestHeader(name = "x-api-key") String apiKey,
      @Valid @RequestBody DeliveringPurchaseOrderRequest request) {
    log.info("updatePurchaseOrderStage: {}", request);
    purchaseOrderApplicationService.updatePurchaserOrderStageService(request);
    log.info("updatePurchaseOrderStage: OK");
    return ResponseEntity.ok().body(ApiResponse.success("OK"));
  }

  @GetMapping("/purchase-orders")
  public ResponseEntity<ApiResponse<List<PurchaseRequestTinyResponseDto>>> getPurchaseOrders(
      @ModelAttribute PurchaseRequestTinyRequestDto request) {
    var result = purchaseOrderApplicationService.getPurchaseOrders(request);
    return ResponseEntity.ok().body(ApiResponse.success(result));
  }
}
