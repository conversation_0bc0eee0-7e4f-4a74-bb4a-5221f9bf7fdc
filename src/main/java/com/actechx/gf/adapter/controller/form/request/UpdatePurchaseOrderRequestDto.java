package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.POStage;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.PaymentMethod;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class UpdatePurchaseOrderRequestDto {
  private Long id;

  private String purchaseRequestCode;

  @NotBlank private String code; // saleOrderCode

  private String source;

  private Long transportOrderId;

  private Long poId;

  private BigDecimal transportCost;

  @NotNull private Long supplierId;

  @NotNull private Long purchaserId;

  private Long purchaseRequestId;

  private String purchaserName;

  private PaymentMethod paymentMethod;

  private Boolean isBestPrice;

  @NotBlank private String quotationAskCode;

  @NotNull private POStage stage;

  @NotNull private POStatusEnum status;

  private String note;

  @Valid private List<POProductRequestDTO> soProducts;

  private LocalDateTime createdAt;

  private String createdBy;
}
