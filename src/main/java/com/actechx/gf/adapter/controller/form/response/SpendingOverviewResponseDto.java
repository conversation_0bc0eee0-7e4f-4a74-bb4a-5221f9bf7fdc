package com.actechx.gf.adapter.controller.form.response;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpendingOverviewResponseDto {
  
  /**
   * Tổng số tiền đã chi trong tuần này (số tiền gốc)
   * <PERSON><PERSON><PERSON><PERSON> tính dựa trên số đơn hàng đã hoàn thành trong tuần
   */
  private BigDecimal totalSpentThisWeek;
  
  /**
   * Tổng số tiền đã chi trong tuần này (đã làm tròn)
   * Áp dụng quy tắc làm tròn: <1 triệu hiển thị đủ, 1-1000 triệu làm tròn triệu, >1 tỷ làm tròn tỷ
   */
  private BigDecimal totalSpentThisWeekRounded;
  
  /**
   * Tổng chi tiêu trong tháng này (số tiền gốc)
   * <PERSON><PERSON><PERSON><PERSON> tính dựa trên số đơn hàng đã hoàn thành trong tháng
   */
  private BigDecimal totalSpentThisMonth;
  
  /**
   * Tổng chi tiêu trong tháng này (đã làm tròn)
   * Áp dụng quy tắc làm tròn: <1 triệu hiển thị đủ, 1-1000 triệu làm tròn triệu, >1 tỷ làm tròn tỷ
   */
  private BigDecimal totalSpentThisMonthRounded;
  
  /**
   * Tổng chi tiêu trong năm nay (số tiền gốc)
   * Được tính dựa trên số đơn hàng đã hoàn thành trong năm
   */
  private BigDecimal totalSpentThisYear;
  
  /**
   * Tổng chi tiêu trong năm nay (đã làm tròn)
   * Áp dụng quy tắc làm tròn: <1 triệu hiển thị đủ, 1-1000 triệu làm tròn triệu, >1 tỷ làm tròn tỷ
   */
  private BigDecimal totalSpentThisYearRounded;
  
  /**
   * Thời gian cập nhật (format: dd/MM/yyyy - dd/MM/yyyy)
   * Ví dụ: "15/06/2025 - 23/06/2025"
   * Tối đa là 7 ngày
   */
  private String updateTime;
  
  /**
   * Chuỗi hiển thị số tiền tuần với đơn vị
   * Ví dụ: "1.2 triệu", "15 tỷ"
   */
  private String totalSpentThisWeekDisplay;
  
  /**
   * Chuỗi hiển thị số tiền tháng với đơn vị
   * Ví dụ: "1.2 triệu", "15 tỷ"
   */
  private String totalSpentThisMonthDisplay;
  
  /**
   * Chuỗi hiển thị số tiền năm với đơn vị
   * Ví dụ: "1.2 triệu", "15 tỷ"
   */
  private String totalSpentThisYearDisplay;
}
