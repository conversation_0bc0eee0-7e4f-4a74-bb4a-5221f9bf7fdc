package com.actechx.gf.adapter.controller.form.response.purchase;

import com.actechx.gf.domain.model.enums.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
public class PurchaseOrderDetailResponseDto {
  private CommonInfo commonInfo;
  private PersonalInfo personalInfo;

  private List<SparePartInfo> sparePartInfoConfirmed;

  private PaymentInfo paymentInfo;
  private AssociatedCarInfo associatedCarInfo;

  @Data
  @Builder
  public static class CommonInfo {
    private Instant createdAt;
    private PurchaseRequestStatus purchaseRequestStatus;
    private POStatusEnum stage;
    private String purchaseRequestCode;
    private String purchaseOrderCode;
    private String prId;
  }

  @Data
  @Builder
  public static class PersonalInfo {
    private String name;
    private String phoneNumber;
    private String address;
  }

  @Data
  @Builder
  public static class SparePartInfo {
    private String vendorName;
    BigDecimal totalPrice;
    private List<SparePartInfoConfirmed> sparePartInfoConfirmed;
  }

  @Data
  @Builder
  public static class SparePartInfoConfirmed {
    private String requestProductName;
    private Segment segment;
    private Long quantity;
    private BigDecimal price;
    private Long productId;
    private Long supplierId;
    private Long poId;
    private String unit;
    private PurchaseRequestDataStatus purchaseRequestStatus;
  }

  @Data
  @Builder
  public static class PaymentInfo {
    private BigDecimal totalPrice;
    private BigDecimal totalAmount;
    private PaymentMethod paymentMethod;
  }

  @Data
  public static class AssociatedCarInfo {
    private String carName;
    private String phoneNumber;
    private String address;
    private String timeReceive;
  }
}
