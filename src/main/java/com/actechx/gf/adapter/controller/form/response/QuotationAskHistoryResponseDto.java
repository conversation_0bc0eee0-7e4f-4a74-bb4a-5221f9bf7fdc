package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.OwnerType;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QuotationAskHistoryResponseDto {
  private Long id;

  private String quotationAskCode;

  private OwnerType updatedRole;

  private Long updatedByTenantId;

  private String updatedType;

  private List<QuotationAskHistoryItemResponseDto> items;

  private Instant createdAt;

  private Instant updatedAt;

  private String createdBy;

  private String updatedBy;

  @Data
  @Builder
  public static class QuotationAskHistoryItemResponseDto {
    private Long id;
    private String fieldName;
    private String oldData;
    private String newData;
    private String note;
    private String tier;
    private String type;
  }
}
