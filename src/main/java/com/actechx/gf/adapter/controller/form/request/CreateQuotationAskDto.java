package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.CarType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
public class CreateQuotationAskDto {

  private String askNote;

  @Valid private AskedVehicleDto askedVehicle;

  @Valid private List<AttachmentDto> attachments;

  @NotEmpty @Valid private List<SparePartDto> spareParts;

  @Data
  public static class AskedVehicleDto {
    @NotBlank private String carBrand;

    @NotBlank private String carModel;

    private String yearOfManufacture;

    private CarType carType;

    private String trimsLevel;

    @Size(max = 17)
    private String vin;
  }

  @Data
  public static class AttachmentDto {
    @NotBlank private String attachmentUrl;

    private String note;
  }

  @Data
  public static class SparePartDto {
    @NotBlank private String partNameInput;

    @NotBlank private String partNameUnit;

    private String refCode;
  }
}
