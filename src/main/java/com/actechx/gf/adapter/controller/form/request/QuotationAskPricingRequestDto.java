package com.actechx.gf.adapter.controller.form.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class QuotationAskPricingRequestDto {
  @NotNull private Long id;
  @NotEmpty @Valid private List<AddSparePartPricingRequestDto> spareParts;

  @Data
  public static class AddSparePartPricingRequestDto {
    @NotNull private Long id;
    @Valid private List<SparePartLineItem> sparePartLineItems;
  }

  @Data
  public static class SparePartLineItem {
    @Valid private List<SparePartLineItemSegment> sparePartLineItemSegments;
  }

  @Data
  public static class SparePartLineItemSegment {
    @NotNull private Long id;
    @NotNull private Boolean detailStatus;
    private Integer quantity;
    @Valid private List<AddSparePartLineItem> addSparePartLineItems;
  }

  @Data
  public static class AddSparePartLineItem {
    @NotNull private Long id;
    @NotNull private Boolean detailStatus;
    private Integer quantity;
  }
}
