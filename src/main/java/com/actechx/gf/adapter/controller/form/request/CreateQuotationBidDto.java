package com.actechx.gf.adapter.controller.form.request;

import com.actechx.common.enums.TenantType;
import com.actechx.gf.domain.model.enums.BidType;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateQuotationBidDto {

  private Long id;

  @NotNull private Long tenantId;

  private Long originTenantId;

  private String tenantName;

  @NotNull private TenantType tenantType;

  @NotBlank private String quotationAskCode;

  @NotNull private BidType bidType;

  @NotNull private QuotationBidStatus status;

  @Valid private List<SparePartPriceLineItemsDto> sparePartPriceLineItems = new ArrayList<>();

  @Valid private List<SparePartPriceLineItemsDto> addedSparePartPriceLineItems = new ArrayList<>();

  @Valid private List<BiddedSparePartsDto> biddedSpareParts = new ArrayList<>();

  private String updatedInformation;

  private String note;

  private LocalDateTime createdAt;

  private String createdBy;

  private Boolean updated;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class SparePartPriceLineItemsDto {
    private Long id;

    @NotBlank private String sparePartInputCode;

    @NotNull private Segment segment;

    private BigDecimal price;

    @NotBlank private String currency;

    private String note;

    @NotBlank private String unit;

    private boolean pickedToPO;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class BiddedSparePartsDto {
    private Long id;

    @NotBlank private String code;

    @NotBlank private String partNameInput;

    @NotBlank private String partNameUnit;

    @NotBlank private String refCode;

    @NotNull private Long tenantId;
  }
}
