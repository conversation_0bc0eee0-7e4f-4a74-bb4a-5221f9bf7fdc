package com.actechx.gf.adapter.controller.form.response.purchase;

import com.actechx.gf.domain.model.enums.POStage;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchPurchaseOrderResponseDto {
  private Long id;

  private String code;

  private String quotationAskCode;

  private POStage stage;

  private Instant createdAt;

  private List<SearchPurchaseOrderDataResponseDto> purchaserOrderList;

  @Getter
  @Setter
  public static class SearchPurchaseOrderDataResponseDto {
    private Long id;

    private String requestedProductName;
  }
}
