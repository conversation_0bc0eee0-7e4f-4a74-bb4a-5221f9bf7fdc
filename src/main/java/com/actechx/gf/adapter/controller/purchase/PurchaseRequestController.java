package com.actechx.gf.adapter.controller.purchase;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.PagedApiResponse;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.request.purchase.CancelPurchaserRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.ConfirmPurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.CreatePurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaseRequestRequestDto;
import com.actechx.gf.adapter.controller.form.response.purchase.ChatPurchaseRequestResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.SaasTenantBasicResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.SearchPurchaseRequestResponseDto;
import com.actechx.gf.app.service.purchase.PurchaseRequestApplicationService;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/purchase-request")
@RequiredArgsConstructor
public class PurchaseRequestController {
  private final PurchaseRequestApplicationService service;

  @PostMapping
  public ResponseEntity<ApiResponse<PurchaseRequest>> createPurchaseRequest(
      @Valid @RequestBody CreatePurchaseRequestDto request) {
    log.info("createPurchaseRequest: {}", request);
    PurchaseRequest response = service.createPurchaseRequest(request);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @PutMapping("/confirm/{id}")
  public ResponseEntity<ApiResponse<String>> confirmController(
      @PathVariable Long id, @Valid @RequestBody ConfirmPurchaseRequestDto request) {
    log.info("confirmPurchaseRequest: {}", request);
    service.confirm(id, request);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }

  @PutMapping("/cancel")
  public ResponseEntity<ApiResponse<String>> cancelController(
      @Valid @RequestBody CancelPurchaserRequestDto request) {
    log.info("cancelPurchaseRequest: {}", request);
    service.cancelService(request);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }

  @GetMapping("/{purchaseId}")
  public ResponseEntity<ApiResponse<PurchaseOrderDetailResponseDto>>
      detailPurchaseRequestByIdController(@PathVariable Long purchaseId) {
    log.info("detailPurchaseRequestByIdController: {}", purchaseId);
    PurchaseOrderDetailResponseDto response = service.getDetailByIdService(purchaseId);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping("/detail/{code}")
  public ResponseEntity<ApiResponse<PurchaseOrderDetailResponseDto>>
      detailPurchaseRequestByCodeController(@PathVariable String code) {
    log.info("detailPurchaseRequestByCodeController: {}", code);
    PurchaseOrderDetailResponseDto response = service.getDetailByCodeService(code);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping("/search")
  public ResponseEntity<PagedApiResponse<SearchPurchaseRequestResponseDto>>
      searchPurchaseRequestController(@Valid SearchPurchaseRequestRequestDto request) {
    log.info("searchPurchaseRequestController: {}", request);
    Page<SearchPurchaseRequestResponseDto> responses = service.searchService(request);
    return ResponseEntity.ok(ResponseUtil.successPaged(responses));
  }

  @GetMapping("/saas-tenant")
  public ResponseEntity<ApiResponse<SaasTenantBasicResponseDto>> getSaasTenantBasicController() {
    log.info("getSaasTenantBasicController");
    SaasTenantBasicResponseDto responses = service.getSaasTenantBasicService();
    return ResponseEntity.ok(ResponseUtil.success(responses));
  }

  @GetMapping("/chat/{code}")
  public ResponseEntity<ApiResponse<ChatPurchaseRequestResponseDto>>
      getChatPurchaseRequestController(@PathVariable String code) {
    log.info("getChatPurchaseRequestController: {}", code);
    ChatPurchaseRequestResponseDto response = service.getChatPurchaseRequestService(code);
    return ResponseEntity.ok(ResponseUtil.success(response));
  }
}
