package com.actechx.gf.adapter.controller.form.response.purchase;

import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchPurchaseRequestResponseDto {
  private Long id;

  private String code;

  private PurchaseRequestStatus status;

  private Instant createdAt;

  private List<SearchPurchaseRequestDataResponseDto> purchaserRequestDataList;

  @Getter
  @Setter
  public static class SearchPurchaseRequestDataResponseDto {
    private Long id;

    private String requestedProductName;

    private String quotationAskCode;
  }
}
