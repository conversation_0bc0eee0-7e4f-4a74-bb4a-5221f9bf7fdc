package com.actechx.gf.adapter.controller.form.request.purchase;

import com.actechx.gf.domain.model.enums.POStage;
import com.actechx.gf.domain.model.enums.ShipmentOrderType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class ShipmentOrderClientRequest {
  private ShipmentOrderType type; // RECEIPT DELIVERY

  // DELIVERING, DELIVERED
  @NotNull private POStage status;

  @NotEmpty private List<String> codes;
}
