package com.actechx.gf.adapter.controller.purchase;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.request.purchase.UpdateCartRequestDto;
import com.actechx.gf.adapter.controller.form.response.purchase.CartResponseDto;
import com.actechx.gf.app.service.purchase.CartApplicationService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/cart")
@RequiredArgsConstructor
public class CartController {
  private final CartApplicationService service;

  @GetMapping()
  public ResponseEntity<ApiResponse<List<CartResponseDto>>> getCartController() {
    List<CartResponseDto> response = service.getAllService();
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @PostMapping("/add/{sparePartPriceLineItemId}")
  public ResponseEntity<ApiResponse<Integer>> addCartController(
      @PathVariable Long sparePartPriceLineItemId) {
    int response = service.addCartService(sparePartPriceLineItemId);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @PutMapping()
  public ResponseEntity<ApiResponse<String>> updateCartByIdController(
      @Valid @RequestBody UpdateCartRequestDto request) {
    service.updateByIdService(request);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<ApiResponse<String>> deleteCartByIdController(@PathVariable Long id) {
    service.deleteByIdService(id);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }
}
