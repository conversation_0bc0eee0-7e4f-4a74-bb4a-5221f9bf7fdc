package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuotationAskPricingProposalResponseDto {

  private Long id;

  private String quotationAskCode;

  private Long replyTenantId;

  private List<AskedSparePartPricingProposalResponseDto> sparePartPriceLineItems;

  @Getter
  @Setter
  public static class AskedSparePartPricingProposalResponseDto {

    private Long id;

    private String sparePartInputCode;

    private Segment segment;

    private BigDecimal materialPrice;

    private BigDecimal servicingPrice;

    private String currency;

    private Integer quantity;

    // them vao
    private String partNameInput;

    private String unit;

    private String refCode;

    private String note;

    private boolean updatedStatus;
  }
}
