package com.actechx.gf.adapter.controller.form.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardStatsResponseDto {
  
  /**
   * Số yêu cầu báo giá garage đã tạo: count yêu cầu báo giá trên tất cả các trạng thái, 
   * có thời gian tạo trong tuần này và không bị xóa
   */
  private Long totalQuotationRequestsThisWeek;
  
  /**
   * Số yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết": 
   * Count yêu cầu báo giá trên trạng thái "Yêu cầu giá chi tiết" (PRICING), 
   * c<PERSON> thời gian tạo trong tuần này và không bị xóa
   */
  private Long quotationRequestsPricingThisWeek;
  
  /**
   * Số đơn hàng ở trạng thái "Đang giao hàng": 
   * Count đơn hàng trên trạng thái "Đang giao hàng" (IN_SHIPPING) và không bị xóa
   */
  private Long purchaseOrdersInShipping;
  
  /**
   * Số đơn hàng ở trạng thái "Hoàn thành": 
   * Count đơn hàng trên trạng thái "Hoàn thành" (CLOSED), 
   * thời gian hoàn thành đơn hàng trong tuần này và không bị xóa
   */
  private Long purchaseOrdersCompletedThisWeek;
}
