package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.Segment;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QuotationAskPricingResponseDto {
  private Long id;

  private String quotationAskCode;

  private Long originTenantId;

  private LocalDateTime createdAt;

  private String createdBy;

  private List<AskedSparePartPricingResponse> askedSpareParts;

  @Data
  @Builder
  public static class AskedSparePartPricingResponse {
    private Long id;

    private String code;

    private String partNameInput;

    private String partNameUnit;

    private Integer quantity;

    private Segment segment;

    private String refCode;

    private Long tenantId;
  }
}
