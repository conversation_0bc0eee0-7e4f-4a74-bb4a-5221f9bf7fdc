package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingOverviewResponseDto;
import com.actechx.gf.app.service.DashboardApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@RequiredArgsConstructor
public class DashboardController {

  private final DashboardApplicationService dashboardApplicationService;

  /**
   * API lấy thống kê dashboard cho garage
   * 
   * @return DashboardStatsResponseDto chứa:
   *         - <PERSON><PERSON> yêu cầu báo giá garage đã tạo trong tuần này
   *         - <PERSON><PERSON> yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết" trong tuần này  
   *         - Số đơn hàng ở trạng thái "Đang giao hàng"
   *         - Số đơn hàng ở trạng thái "Hoàn thành" trong tuần này
   */
  @GetMapping("/stats")
  public ResponseEntity<ApiResponse<DashboardStatsResponseDto>> getDashboardStats() {
    log.info("Getting dashboard statistics");
    DashboardStatsResponseDto response = dashboardApplicationService.getDashboardStats();
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  /**
   * API theo dõi tổng quan số liệu chi tiêu cho garage
   *
   * @return SpendingOverviewResponseDto chứa:
   *         - Tổng số tiền đã chi trong tuần này (gốc và làm tròn)
   *         - Tổng chi tiêu trong tháng này (gốc và làm tròn)
   *         - Tổng chi tiêu trong năm nay (gốc và làm tròn)
   *         - Thời gian cập nhật (dd/MM/yyyy - dd/MM/yyyy)
   *         - Chuỗi hiển thị với đơn vị
   */
  @GetMapping("/spending-overview")
  public ResponseEntity<ApiResponse<SpendingOverviewResponseDto>> getSpendingOverview() {
    log.info("Getting spending overview");
    SpendingOverviewResponseDto response = dashboardApplicationService.getSpendingOverview();
    return ResponseEntity.ok(ApiResponse.success(response));
  }
}
