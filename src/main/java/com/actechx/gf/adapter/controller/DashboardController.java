package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.app.service.DashboardApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@RequiredArgsConstructor
public class DashboardController {

  private final DashboardApplicationService dashboardApplicationService;

  /**
   * API lấy thống kê dashboard cho garage
   * 
   * @return DashboardStatsResponseDto chứa:
   *         - <PERSON><PERSON> yê<PERSON> cầu báo giá garage đã tạo trong tuần này
   *         - <PERSON><PERSON> yêu cầu báo giá garage ở trạng thái "<PERSON><PERSON>u cầu giá chi tiết" trong tuần này  
   *         - Số đơn hàng ở trạng thái "Đang giao hàng"
   *         - Số đơn hàng ở trạng thái "Hoàn thành" trong tuần này
   */
  @GetMapping("/stats")
  public ResponseEntity<ApiResponse<DashboardStatsResponseDto>> getDashboardStats() {
    log.info("Getting dashboard statistics");
    DashboardStatsResponseDto response = dashboardApplicationService.getDashboardStats();
    return ResponseEntity.ok(ApiResponse.success(response));
  }
}
