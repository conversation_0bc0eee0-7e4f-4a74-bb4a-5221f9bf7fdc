package com.actechx.gf.adapter.controller.form.response.purchase;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SaasTenantBasicResponseDto {

  private Long id;
  private String name;
  private String companyPhoneNumber;
  private String companyHoAddress;
  private List<SaasTenantTransporterRegistryResponse> transporterRegistry;

  @Getter
  @Setter
  public static class SaasTenantTransporterRegistryResponse {
    private Long id;
    private String transporterName;
    private String routeName;
    private String routeContactPhoneNumber;
    private String routeStartedAt;
  }
}
