package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.Segment;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class POProductRequestDTO {
  private Long id;

  private Long productId;

  private Long quantity;

  private BigDecimal unitPrice;

  private String unit;

  @NotNull private Segment segment;

  private Long supplierId;

  @NotBlank private String requestedProductName;

  private Long saleOrderId;

  private String quotationAskCode;
}
