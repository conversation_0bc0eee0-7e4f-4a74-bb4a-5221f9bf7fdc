package com.actechx.gf.adapter.controller.form.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateQuotationAskDto {
  @NotBlank private String quotationAskCode;

  @NotNull private Long originTenantId;

  @Valid @NotNull private String quotationAskUpdateParts;
}
