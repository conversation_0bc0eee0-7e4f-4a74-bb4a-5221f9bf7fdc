package com.actechx.gf.adapter.controller.form.request.purchase;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class CreatePurchaseRequestDto {
  @NotNull private Long transporterRegistryId;

  @Valid @NotEmpty private List<CreateSparePartRequestDto> cartList;

  @Data
  public static class CreateSparePartRequestDto {

    @NotNull private Long cartId;
  }
}
