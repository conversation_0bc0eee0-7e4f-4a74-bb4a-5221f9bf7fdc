package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.Segment;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class QuotationAskPricingProposalRequest {

  @NotBlank private String quotationAskCode;

  @NotNull private Long replyTenantId;

  private String replyTenantName;

  private Long originTenantId;

  @Valid @NotEmpty private List<AskedSparePartPricingProposalRequest> sparePartPriceLineItems;

  @Data
  public static class AskedSparePartPricingProposalRequest {
    @NotBlank private String sparePartInputCode;

    @NotNull private Segment segment;

    @NotNull private BigDecimal materialPrice;

    @NotNull private BigDecimal servicingPrice;

    @NotBlank
    @Size(max = 10)
    private String currency;

    @NotNull private Integer quantity;

    @NotBlank private String unit;

    private String refCode;

    private String note;
  }
}
