package com.actechx.gf.adapter.controller.form.request;

import com.actechx.common.dto.BaseSearchRequest;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SearchQuotationAskRequestDto extends BaseSearchRequest {
  private QuotationStatus status;

  @Schema(hidden = true)
  private Long tenantId;
}
