package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.QuotationStatus;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;

@Data
@Builder
@Setter
public class SearchQuotationAskResponseDto {
  private Long id;
  private String code;
  private String tenantId;
  private String askNote;
  private Boolean isInvoiceRequired;
  private Integer quantityQuotationBid;
  private Integer quantitySpareParts;
  private Integer quantityQuotationProposal;
  private QuotationStatus status;
  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;
  private AskedVehicleResponseDto askedVehicle;
  private List<AttachmentResponseDto> attachments;

  @Data
  @Builder
  public static class AskedVehicleResponseDto {
    private Long id;
    private String carBrand;
    private String carModel;
    private String yearOfManufacture;
    private String carType;
    private String trimsLevel;
    private String vin;
  }

  @Data
  @Builder
  public static class AttachmentResponseDto {
    private Long id;
    private String owner;
    private String attachmentUrl;
    private String note;
  }

  @Data
  @Builder
  public static class SparePartResponseDto {
    private Long id;
    private String partNameInput;
    private String partNameUnit;
  }
}
