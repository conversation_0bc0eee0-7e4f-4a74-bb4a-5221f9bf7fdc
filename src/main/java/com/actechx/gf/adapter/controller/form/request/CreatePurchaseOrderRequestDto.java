package com.actechx.gf.adapter.controller.form.request;

import com.actechx.gf.domain.model.enums.PaymentMethod;
import com.actechx.gf.domain.model.enums.PurchaseSource;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class CreatePurchaseOrderRequestDto {

  @NotNull private PurchaseSource source;

  @NotNull private Long purchaserId;

  @NotNull private Long supplierId;

  private String supplierName;

  private PaymentMethod paymentMethod;

  private Boolean isBestPrice;

  private String quotationAskCode;

  private Long transportOrderId;

  private Long transportRouteId;

  private List<POProductRequestDTO> poProducts;
}
