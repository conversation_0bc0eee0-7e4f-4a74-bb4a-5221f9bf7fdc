package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Builder
public class DetailQuotationAskResponseDto {
  private Long id;
  private String code;
  private String tenantId;
  private String askNote;
  private Boolean isInvoiceRequired;
  private String status;
  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;
  private AskedVehicleDetailResponseDto askedVehicle;
  private List<AttachmentDetailResponseDto> attachments;
  private List<AskedSparePartDetailResponseDto> spareParts;

  @Data
  @Builder
  public static class AskedVehicleDetailResponseDto {
    private Long id;
    private String carBrand;
    private String carModel;
    private String yearOfManufacture;
    private String carType;
    private String trimsLevel;
    private String vin;
  }

  @Data
  @Builder
  public static class AttachmentDetailResponseDto {
    private Long id;
    private String owner;
    private String attachmentUrl;
    private String note;
  }

  @Data
  @Builder
  public static class AskedSparePartDetailResponseDto {
    // sparePartId
    private Long id;
    private String partNameInput;
    private String partNameUnit;
    private String code;
    private String refCode;
    private List<SparePartLineItemDetail> sparePartLineItems;
  }

  @Getter
  @Setter
  public static class SparePartLineItemDetail {
    private Segment segment;
    private List<SparePartLineItemSegmentDetail> sparePartLineItemSegments;
  }

  @Getter
  @Setter
  public static class SparePartLineItemSegmentDetail {
    // sparePartPriceLineItemId
    private Long id;
    private Long tenantId;
    private String sparePartInputCode;
    private Segment segment;
    private BigDecimal price;
    private String currency;
    private String note;
    private String unit;
    private Boolean detailStatus;
    private Boolean receiveDetailStatus;
    private Integer quantity;
    private BigDecimal materialPrice;
    private BigDecimal servicingPrice;
    private BigDecimal receiveDetailPriceTotal;
    private QuotationBidStatus status;
    private Instant updatedAt;
    private List<AddSparePartLineItemDetail> addSparePartLineItems;
  }

  @Getter
  @Setter
  public static class AddSparePartLineItemDetail {
    // addSparePartPriceLineItemId
    private Long id;
    private Long tenantId;
    private String partNameInput;
    private String partNameUnit;
    private String refCode;

    private String sparePartInputCode;
    private Segment segment;
    private BigDecimal price;
    private String currency;
    private String note;
    private String unit;
    private Boolean detailStatus;
    private Boolean receiveDetailStatus;
    private Integer quantity;
    private BigDecimal materialPrice;
    private BigDecimal servicingPrice;
    private Instant updatedAt;
  }
}
