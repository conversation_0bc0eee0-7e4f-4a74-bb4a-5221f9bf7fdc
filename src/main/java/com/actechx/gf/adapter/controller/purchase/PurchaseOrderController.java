package com.actechx.gf.adapter.controller.purchase;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.PagedApiResponse;
import com.actechx.common.utils.ResponseUtil;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaserOrderRequestDto;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.SearchPurchaseOrderResponseDto;
import com.actechx.gf.app.service.purchase.PurchaseOrderApplicationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/purchase-order")
@RequiredArgsConstructor
public class PurchaseOrderController {

  private final PurchaseOrderApplicationService service;

  @GetMapping("/{purchaseId}")
  public ResponseEntity<ApiResponse<PurchaseOrderDetailResponseDto>>
      detailPurchaseOrderByIdController(@PathVariable Long purchaseId) {
    log.info("detailPurchaseOrderByIdController: {}", purchaseId);
    PurchaseOrderDetailResponseDto response = service.getDetailByIdService(purchaseId);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping("/detail/{code}")
  public ResponseEntity<ApiResponse<PurchaseOrderDetailResponseDto>>
      detailPurchaseOrderByCodeController(@PathVariable String code) {
    log.info("detailPurchaseOrderByCodeController: {}", code);
    PurchaseOrderDetailResponseDto response = service.getDetailByCodeService(code);
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  @GetMapping("/search")
  public ResponseEntity<PagedApiResponse<SearchPurchaseOrderResponseDto>>
      searchPurchaseOrderController(@Valid SearchPurchaserOrderRequestDto request) {
    log.info("searchPurchaseOrderController: {}", request);
    Page<SearchPurchaseOrderResponseDto> responses = service.searchService(request);
    return ResponseEntity.ok(ResponseUtil.successPaged(responses));
  }

  @PostMapping("/{purchaseOrderId}/confirm-received")
  public ResponseEntity<ApiResponse<String>> confirmReceivedPurchaseOrderByIdController(
      @PathVariable Long purchaseOrderId) {
    log.info("confirmReceivedPurchaseOrderByIdController: {}", purchaseOrderId);
    service.confirmGoodsReceivedByIdService(purchaseOrderId);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }

  @PostMapping("/confirm-received/{code}")
  public ResponseEntity<ApiResponse<String>> confirmReceivedPurchaseOrderByCodeController(
      @PathVariable String code) {
    log.info("confirmReceivedPurchaseOrderByCodeController: {}", code);
    service.confirmGoodsReceivedByCodeService(code);
    return ResponseEntity.ok(ApiResponse.success("Success"));
  }
}
