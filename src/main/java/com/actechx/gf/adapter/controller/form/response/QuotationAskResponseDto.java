package com.actechx.gf.adapter.controller.form.response;

import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QuotationAskResponseDto {
  private Long id;
  private String code;
  private String tenantId;
  private String tenantName;
  private String tenantPhoneNumber;
  private String tenantOpsArea;
  private String tenantAddress;
  private String tenantOpsRegion;
  private String askNote;
  private Boolean isInvoiceRequired;
  private AskedVehicleResponseDto askedVehicle;
  private List<AttachmentResponseDto> attachments;
  private List<SparePartResponseDto> spareParts;
  private String status;
  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;

  @Data
  @Builder
  public static class AskedVehicleResponseDto {
    private Long id;
    private String carBrand;
    private String carModel;
    private String yearOfManufacture;
    private String carType;
    private String trimsLevel;
    private String vin;
  }

  @Data
  @Builder
  public static class AttachmentResponseDto {
    private Long id;
    private String owner;
    private String attachmentUrl;
    private String note;
  }

  @Data
  @Builder
  public static class SparePartResponseDto {
    private Long id;
    private String partNameInput;
    private String partNameUnit;
    private String code;
    private String refCode;
    private Long tenantId;
  }
}
