package com.actechx.gf.adapter.controller.form.response;

import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuotationBidResponseDto {
  private Long id;
  private Long tenantId;
  private String tenantType;
  private String quotationAskCode;
  private String bidType;
  private String status;
  private List<SparePartPriceLineItemsDto> sparePartPriceLineItems;
  private List<SparePartPriceLineItemsDto> addedSparePartPriceLineItems;
  private List<BiddedSparePartsDto> biddedSpareParts;
  private String updatedInformation;
  private String note;
  private Instant createdAt;
  private String createdBy;
  private Instant updatedAt;
  private String updatedBy;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class SparePartPriceLineItemsDto {
    private Long id;
    private String sparePartInputCode;
    private String segment;
    private double price;
    private String currency;
    private String note;
    private String unit;
    private boolean pickedToPO;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class BiddedSparePartsDto {
    private Long id;
    private String code;
    private String partNameInput;
    private String partNameUnit;
    private String refCode;
    private Long tenantId;
  }
}
