package com.actechx.gf.adapter.controller.form.response.purchase;

import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CartResponseDto {

  private Long tenantId;

  private String name;

  private List<SparePartCartDto> spareParts;

  @Getter
  @Setter
  public static class SparePartCartDto {

    private Long id;

    private Long tenantId;

    private String name;

    private String unit;

    private Segment segment;

    private Integer quantity;

    private Integer minQuantity;

    private BigDecimal price;

    private Boolean isPicked;

    private String refPrCode;

    private String note;
  }
}
