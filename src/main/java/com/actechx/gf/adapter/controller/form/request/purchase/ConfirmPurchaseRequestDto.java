package com.actechx.gf.adapter.controller.form.request.purchase;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class ConfirmPurchaseRequestDto {

  @Schema(description = "Chỉ cần truyền những product nào được checked")
  @NotNull
  private List<ConfirmSparePartRequestDto> products;

  @Data
  public static class ConfirmSparePartRequestDto {
    private Long purchaseRequestDataId;
  }
}
