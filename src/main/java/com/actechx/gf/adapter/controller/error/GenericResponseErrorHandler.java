package com.actechx.gf.adapter.controller.error;

import com.actechx.common.exception.ExternalServiceException;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;

@RequiredArgsConstructor
@Slf4j
public class GenericResponseErrorHandler implements ResponseErrorHandler {
  private final String serviceName;

  @Override
  public boolean hasError(ClientHttpResponse response) throws IOException {
    return response.getStatusCode().isError();
  }

  @Override
  public void handleError(URI url, HttpMethod method, ClientHttpResponse response)
      throws IOException {
    HttpStatusCode statusCode = response.getStatusCode();
    String responseBody = getResponseBody(response);

    log.error(
        "[{}] Service error - Method: {} URL: {} Status: {}, Response: {}",
        serviceName,
        method,
        url,
        statusCode.value(),
        responseBody);

    // TODO
    /*switch (statusCode.value()) {
      case 400 -> throw new BadRequestException(responseBody, serviceName);
      case 401 -> throw new UnauthorizedException("Unauthorized access", serviceName);
      case 403 -> throw new ForbiddenException("Forbidden access", serviceName);
      case 404 -> throw new NotFoundException("Resource not found", serviceName);
      case 409 -> throw new ConflictException(responseBody, serviceName);
      case 500 -> throw new InternalServerException("Internal server error", serviceName);
      case 503 -> throw new ServiceUnavailableException("Service unavailable", serviceName);
      default -> {
        if (statusCode.is4xxClientError()) {
          throw new BadRequestException("Client error: " + responseBody, serviceName);
        } else if (statusCode.is5xxServerError()) {
          throw new InternalServerException("Server error: " + responseBody, serviceName);
        }
      }
    }*/
    throw new ExternalServiceException(serviceName, url.getPath(), responseBody);
  }

  private String getResponseBody(ClientHttpResponse response) {
    try {
      return new String(response.getBody().readAllBytes(), StandardCharsets.UTF_8);
    } catch (IOException e) {
      log.warn("Failed to read response body", e);
      return "Unable to read response body";
    }
  }
}
