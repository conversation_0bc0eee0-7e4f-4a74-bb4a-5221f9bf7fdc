package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.adapter.controller.form.request.POProductRequestDTO;
import com.actechx.gf.domain.model.enums.POStage;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.PaymentMethod;
import com.actechx.gf.domain.model.enums.PurchaseSource;
import java.util.List;
import lombok.Data;

@Data
public class PurchaseOrderResponseDto {
  private Long id;

  private String code;

  private PurchaseSource source;

  private Long transportOrderId;

  private Long transportRouteId;

  private Long purchaserId;

  private Long supplierId;

  private String supplierName;

  private PaymentMethod paymentMethod;

  private Boolean isBestPrice;

  private String quotationAskCode;

  private POStage stage;

  private POStatusEnum status;

  private String note;

  private List<POProductRequestDTO> poProducts;
}
