package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.request.QuotationAskPricingRequestDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingResponseDto;
import com.actechx.gf.app.service.QuotationAskPricingApplicationService;
import jakarta.validation.Valid;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/quotation-pricing")
@RequiredArgsConstructor
public class QuotationAskPricingController {

  private final QuotationAskPricingApplicationService service;

  @PostMapping("/request")
  public ResponseEntity<ApiResponse<QuotationAskPricingResponseDto>>
      quotationAskPricingRequestController(
          @RequestBody @Valid QuotationAskPricingRequestDto request) {
    log.info("quotationAskPricingRequestController: {}", request);
    QuotationAskPricingResponseDto response = service.quotationAskPricingRequestService(request);
    return ResponseEntity.ok()
        .body(ApiResponse.success(response, Objects.isNull(response) ? "None created" : "success"));
  }
}
