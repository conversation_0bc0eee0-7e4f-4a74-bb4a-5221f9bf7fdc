package com.actechx.gf.adapter.controller.form.response;

import com.actechx.gf.domain.model.enums.SpendingPeriodEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpendingChartResponseDto {
  
  /**
   * Loạ<PERSON> khoảng thời gian (THIS_WEEK, THIS_MONTH, LAST_6_MONTHS, LAST_YEAR)
   */
  private SpendingPeriodEnum period;
  
  /**
   * Tên hiển thị của khoảng thời gian
   */
  private String periodDisplayName;
  
  /**
   * Danh sách nhãn cho các cột trong biểu đồ
   * - THIS_WEEK: ["T2", "T3", "T4", "T5", "T6", "T7", "CN"]
   * - THIS_MONTH: ["Tuần 1", "Tuần 2", "Tuần 3", "Tuần 4"]
   * - LAST_6_MONTHS: ["10/2024", "11/2024", "12/2024", "01/2025", "02/2025", "03/2025"]
   * - LAST_YEAR: ["Q2/2024", "Q3/2024", "Q4/2024", "Q1/2025"]
   */
  private List<String> labels;
  
  /**
   * Danh sách dữ liệu chi tiêu tương ứng với các nhãn
   * Số tiền gốc (chưa làm tròn)
   */
  private List<BigDecimal> data;
  
  /**
   * Danh sách dữ liệu chi tiêu đã làm tròn
   * Áp dụng quy tắc làm tròn như MoneyUtils
   */
  private List<BigDecimal> dataRounded;
  
  /**
   * Danh sách chuỗi hiển thị với đơn vị
   * Ví dụ: ["1.2 triệu", "15 tỷ", "500,000đ"]
   */
  private List<String> dataDisplay;
  
  /**
   * Tổng chi tiêu trong khoảng thời gian
   */
  private BigDecimal totalSpent;
  
  /**
   * Tổng chi tiêu đã làm tròn
   */
  private BigDecimal totalSpentRounded;
  
  /**
   * Chuỗi hiển thị tổng chi tiêu với đơn vị
   */
  private String totalSpentDisplay;
  
  /**
   * Thông tin khoảng thời gian chi tiết
   * Ví dụ: "15/06/2025 - 21/06/2025" cho tuần này
   */
  private String timeRange;
  
  /**
   * Data point có giá trị cao nhất
   */
  private BigDecimal maxValue;
  
  /**
   * Data point có giá trị thấp nhất
   */
  private BigDecimal minValue;
  
  /**
   * Giá trị trung bình
   */
  private BigDecimal averageValue;
}
