package com.actechx.gf.adapter.controller.form.request.purchase;

import com.actechx.common.dto.BaseSearchRequest;
import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SearchPurchaseRequestRequestDto extends BaseSearchRequest {
  private String dataSearch;

  private PurchaseRequestStatus status;
}
