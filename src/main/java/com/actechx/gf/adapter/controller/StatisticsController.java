package com.actechx.gf.adapter.controller;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingChartResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingOverviewResponseDto;
import com.actechx.gf.app.service.StatisticsApplicationService;
import com.actechx.gf.domain.model.enums.SpendingPeriodEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@RequiredArgsConstructor
public class StatisticsController {

  private final StatisticsApplicationService dashboardApplicationService;

  /**
   * API lấy thống kê dashboard cho garage
   * 
   * @return DashboardStatsResponseDto chứa:
   *         - Số yêu cầu báo giá garage đã tạo trong tuần này
   *         - Số yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết" trong tuần này  
   *         - Số đơn hàng ở trạng thái "Đang giao hàng"
   *         - Số đơn hàng ở trạng thái "Hoàn thành" trong tuần này
   */
  @GetMapping("/stats")
  public ResponseEntity<ApiResponse<DashboardStatsResponseDto>> getDashboardStats() {
    log.info("Getting dashboard statistics");
    DashboardStatsResponseDto response = dashboardApplicationService.getDashboardStats();
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  /**
   * API theo dõi tổng quan số liệu chi tiêu cho garage
   *
   * @return SpendingOverviewResponseDto chứa:
   *         - Tổng số tiền đã chi trong tuần này (gốc và làm tròn)
   *         - Tổng chi tiêu trong tháng này (gốc và làm tròn)
   *         - Tổng chi tiêu trong năm nay (gốc và làm tròn)
   *         - Thời gian cập nhật (dd/MM/yyyy - dd/MM/yyyy)
   *         - Chuỗi hiển thị với đơn vị
   */
  @GetMapping("/spending-overview")
  public ResponseEntity<ApiResponse<SpendingOverviewResponseDto>> getSpendingOverview() {
    log.info("Getting spending overview");
    SpendingOverviewResponseDto response = dashboardApplicationService.getSpendingOverview();
    return ResponseEntity.ok(ApiResponse.success(response));
  }

  /**
   * API cung cấp dữ liệu cho biểu đồ báo cáo chi tiêu
   *
   * @param period Loại khoảng thời gian:
   *               - this_week: Tuần này (7 cột - 7 ngày)
   *               - this_month: Tháng này (4 cột - 4 tuần)
   *               - last_6_months: 6 tháng gần nhất (6 cột - 6 tháng)
   *               - last_year: 1 năm gần nhất (4 cột - 4 quý)
   * @return SpendingChartResponseDto chứa dữ liệu biểu đồ
   */
  @GetMapping("/spending-chart")
  public ResponseEntity<ApiResponse<SpendingChartResponseDto>> getSpendingChart(
      @RequestParam(value = "period", defaultValue = "this_week") String period) {
    log.info("Getting spending chart with period: {}", period);

    try {
      SpendingPeriodEnum periodEnum = SpendingPeriodEnum.fromValue(period);
      SpendingChartResponseDto response = dashboardApplicationService.getSpendingChart(periodEnum);
      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (IllegalArgumentException e) {
      log.error("Invalid period parameter: {}", period, e);
      throw new IllegalArgumentException("Invalid period parameter. Supported values: this_week, this_month, last_6_months, last_year");
    }
  }
}
