package com.actechx.gf.adapter.persistence.quotation.ask;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(exclude = "quotationAsk", callSuper = true)
@Entity
@Table(name = "asked_spare_parts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = "quotationAsk")
public class AskedSparePartEntity extends AuditableEntity {

  @Id private Long id;

  @Column(nullable = false, unique = true, length = 20)
  private String code;

  @Column(name = "part_name_input", nullable = false)
  private String partNameInput;

  @Column(name = "part_name_unit", nullable = false, length = 50)
  private String partNameUnit;

  private String refCode;

  @Column(nullable = false)
  private Long tenantId;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  private boolean deleted;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "quotation_ask_id", nullable = false)
  @JsonIgnore
  private QuotationAskEntity quotationAsk;

  protected void updateInfo(
      String code, String partNameInput, String partNameUnit, String refCode, Long tenantId) {
    this.code = code;
    this.partNameInput = partNameInput;
    this.partNameUnit = partNameUnit;
    this.refCode = refCode;
    this.tenantId = tenantId;
  }
}
