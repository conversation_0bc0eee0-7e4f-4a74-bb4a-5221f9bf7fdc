package com.actechx.gf.adapter.persistence.purchase.request;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "pr_quotation_ref")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PRQuotationRefEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private Long prId;

  @Column(nullable = false)
  private Long quotationAskId;
}
