package com.actechx.gf.adapter.persistence.quotation.ask;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.app.utils.Consts;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.enums.Segment;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.*;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "quotation_asks")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = {"askedVehicle", "attachments", "spareParts"})
public class QuotationAskEntity extends AuditableEntity {

  @Id private Long id;

  @Column(name = "code", unique = true, length = 50, nullable = false)
  private String code;

  @Column(name = "tenant_id", nullable = false)
  private Long tenantId;

  @Column(name = "tenant_name", nullable = false)
  private String tenantName;

  @Column(name = "tenant_phone_number", length = 20)
  private String tenantPhoneNumber;

  @Column(name = "tenant_ops_area")
  private String tenantOpsArea;

  @Column(name = "tenant_address", columnDefinition = "TEXT")
  private String tenantAddress;

  @Column(name = "tenant_ops_region")
  private String tenantOpsRegion;

  @Column(name = "ask_note", columnDefinition = "TEXT")
  private String askNote;

  @Column(name = "is_invoice_required", nullable = false)
  private Boolean isInvoiceRequired;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false, length = 20)
  private QuotationStatus status;

  @Column(name = "is_processed", nullable = false)
  @Builder.Default
  private Boolean isProcessed = false;

  @Column(name = "processed_at")
  private LocalDateTime processedAt;

  @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "vehicle_id", referencedColumnName = "id", nullable = false)
  private AskedVehicleEntity askedVehicle;

  @OneToMany(
      mappedBy = "quotationAsk",
      cascade = {CascadeType.MERGE, CascadeType.PERSIST},
      fetch = FetchType.LAZY,
      orphanRemoval = true)
  private List<AskedAttachmentEntity> attachments;

  @OneToMany(
      mappedBy = "quotationAsk",
      cascade = {CascadeType.MERGE, CascadeType.PERSIST},
      fetch = FetchType.LAZY,
      orphanRemoval = true)
  private List<AskedSparePartEntity> spareParts;

  @Version private Long version;

  public List<AskedAttachmentEntity> getAttachments() {
    return this.attachments.stream().filter(x -> !x.isDeleted()).toList();
  }

  public List<AskedSparePartEntity> getSpareParts() {
    return this.spareParts.stream().filter(x -> !x.isDeleted()).toList();
  }

  public void updateSparePart(List<AskedSparePartEntity> parts) {
    // Delete item
    for (AskedSparePartEntity item : this.getSpareParts()) {
      if (parts.stream().noneMatch(a -> a.getCode().equals(item.getCode()))) {
        item.setDeleted(true);
      }
    }

    // Add item
    for (var newItem : parts) {
      AskedSparePartEntity existingItem =
          this.getSpareParts().stream()
              .filter(a -> a.getCode().equals(newItem.getCode()))
              .findFirst()
              .orElse(null);
      if (existingItem == null) {
        newItem.setQuotationAsk(this);
        this.spareParts.add(newItem);
      } else {
        // Update existing item
        existingItem.updateInfo(
            newItem.getCode(),
            newItem.getPartNameInput(),
            newItem.getPartNameUnit(),
            newItem.getRefCode(),
            newItem.getTenantId());
      }
    }
  }

  public void updateAttachment(List<AskedAttachmentEntity> attachments) {
    // Add item
    for (var newItem : attachments) {
      if (newItem.getId() == null) {
        newItem.setQuotationAsk(this);
        this.attachments.add(newItem);
      }
    }
    // Delete item
    for (AskedAttachmentEntity item : this.getAttachments()) {
      if (attachments.stream().noneMatch(a -> Objects.equals(item.getId(), a.getId()))) {
        item.setDeleted(true);
      }
    }
  }

  public void updateVehicle(AskedVehicleEntity vehicle) {
    this.getAskedVehicle()
        .updateInfo(
            vehicle.getCarBrand(),
            vehicle.getCarModel(),
            vehicle.getYearOfManufacture(),
            vehicle.getCarType(),
            vehicle.getTrimsLevel(),
            vehicle.getVin());
  }

  public Map<String, String> getSparePartsMapName() {
    Map<String, String> result = new HashMap<>();
    this.getSpareParts()
        .forEach(
            part -> {
              if (StringUtils.isNotBlank(part.getPartNameInput())) {
                result.put(
                    String.format(
                        Consts.SPARE_PART_PATTERN, Consts.ASKED_SPARE_PART, part.getCode()),
                    part.getPartNameInput());
                Arrays.stream(Segment.values())
                    .forEach(
                        segment ->
                            result.put(
                                String.format(
                                    Consts.SPARE_PART_PRICE_PATTERN,
                                    Consts.SPARE_PART_PRICE_LINE_ITEM,
                                    part.getCode(),
                                    segment.name()),
                                part.getPartNameInput()));
              }
            });

    return result;
  }
}
