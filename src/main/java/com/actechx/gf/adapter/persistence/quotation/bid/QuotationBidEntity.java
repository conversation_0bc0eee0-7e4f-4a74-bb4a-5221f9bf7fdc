package com.actechx.gf.adapter.persistence.quotation.bid;

import com.actechx.common.enums.TenantType;
import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.app.utils.Consts;
import com.actechx.gf.domain.model.enums.BidType;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import jakarta.persistence.*;
import java.util.*;
import java.util.stream.Collectors;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@Entity
@Table(name = "quotation_bids")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"sparePartPriceLineItems", "addedSparePartPriceLineItems", "biddedSpareParts"})
@EqualsAndHashCode(
    callSuper = true,
    exclude = {"sparePartPriceLineItems", "addedSparePartPriceLineItems", "biddedSpareParts"})
public class QuotationBidEntity extends AuditableEntity {

  @Id private Long id;

  @Column(name = "tenant_id", length = 50, nullable = false)
  private Long tenantId;

  @Enumerated(EnumType.STRING)
  @Column(name = "tenant_type", nullable = false, length = 20)
  private TenantType tenantType;

  @Column(name = "quotation_ask_code", length = 50)
  private String quotationAskCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "bid_type", nullable = false, length = 20)
  private BidType bidType;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false, length = 20)
  private QuotationBidStatus status;

  @Column(name = "note")
  private String note;

  @OneToMany(
      mappedBy = "quotationBid",
      cascade = CascadeType.ALL,
      fetch = FetchType.LAZY,
      orphanRemoval = true)
  private List<SparePartPriceLineItemEntity> sparePartPriceLineItems = new ArrayList<>();

  @OneToMany(
      mappedBy = "quotationBid",
      cascade = CascadeType.ALL,
      fetch = FetchType.LAZY,
      orphanRemoval = true)
  private List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItems = new ArrayList<>();

  @OneToMany(
      mappedBy = "quotationBid",
      cascade = CascadeType.ALL,
      fetch = FetchType.LAZY,
      orphanRemoval = true)
  private List<BiddedSparePartEntity> biddedSpareParts = new ArrayList<>();

  @Version private Long version;

  @Column(name = "created_by", nullable = false, updatable = false)
  private String createdBy;

  @Column(name = "updated_by")
  private String updatedBy;

  public List<SparePartPriceLineItemEntity> getSparePartPriceLineItems() {
    return this.sparePartPriceLineItems.stream().filter(x -> !x.isDeleted()).toList();
  }

  public List<AddedSparePartPriceLineItemEntity> getAddedSparePartPriceLineItems() {
    return this.addedSparePartPriceLineItems.stream().filter(x -> !x.isDeleted()).toList();
  }

  public List<BiddedSparePartEntity> getBiddedSpareParts() {
    return this.biddedSpareParts.stream().filter(x -> !x.isDeleted()).toList();
  }

  public void updateGeneralInfo(
      BidType bidType, QuotationBidStatus status, String note, String updatedBy) {
    this.bidType = bidType;
    this.status = status;
    this.note = note;
    this.updatedBy = updatedBy;
  }

  public void updateSparePartPriceLineItems(
      List<SparePartPriceLineItemEntity> newSparePartPriceLineItems) {
    Map<String, SparePartPriceLineItemEntity> newItemsMap =
        getSparePartPriceLineItemsMap(newSparePartPriceLineItems);
    Map<String, SparePartPriceLineItemEntity> existItemsMap =
        getSparePartPriceLineItemsMap(this.getSparePartPriceLineItems());
    // Delete item
    for (SparePartPriceLineItemEntity item : this.getSparePartPriceLineItems()) {
      String deletedItemKey = generateKey(item.getSparePartInputCode(), item.getSegment().name());
      if (!newItemsMap.containsKey(deletedItemKey)) {
        item.setDeleted(true);
      }
    }

    // Add item
    for (var newItem : newSparePartPriceLineItems) {
      String newItemKey = generateKey(newItem.getSparePartInputCode(), newItem.getSegment().name());
      if (!existItemsMap.containsKey(newItemKey)) {
        newItem.setQuotationBid(this);
        this.sparePartPriceLineItems.add(newItem);
      } else {
        // Update existing item
        SparePartPriceLineItemEntity existingItem = existItemsMap.get(newItemKey);
        existingItem.updateGeneral(
            newItem.getPrice(), newItem.getCurrency(), newItem.getNote(), newItem.isPickedToPO());
      }
    }
  }

  public void updateAddedSparePartPriceLineItems(
      List<AddedSparePartPriceLineItemEntity> newAddedSparePartPriceLineItems) {
    Map<String, AddedSparePartPriceLineItemEntity> newItemsMap =
        getAddedSparePartPriceLineItemsMap(newAddedSparePartPriceLineItems);
    Map<String, AddedSparePartPriceLineItemEntity> existItemsMap =
        getAddedSparePartPriceLineItemsMap(this.getAddedSparePartPriceLineItems());
    // Delete item
    for (AddedSparePartPriceLineItemEntity item : this.getAddedSparePartPriceLineItems()) {
      String deletedItemKey = generateKey(item.getSparePartInputCode(), item.getSegment().name());
      if (!newItemsMap.containsKey(deletedItemKey)) {
        item.setDeleted(true);
      }
    }

    // Add item
    for (var newItem : newAddedSparePartPriceLineItems) {
      String newItemKey = generateKey(newItem.getSparePartInputCode(), newItem.getSegment().name());
      if (!existItemsMap.containsKey(newItemKey)) {
        newItem.setQuotationBid(this);
        this.addedSparePartPriceLineItems.add(newItem);
      } else {
        // Update existing item
        AddedSparePartPriceLineItemEntity existingItem = existItemsMap.get(newItemKey);
        existingItem.updateGeneral(
            newItem.getPrice(), newItem.getCurrency(), newItem.getNote(), newItem.isPickedToPO());
      }
    }
  }

  public void updateBiddedSpareParts(List<BiddedSparePartEntity> newBiddedSpareParts) {
    Map<String, BiddedSparePartEntity> newItemsMap = getBiddedSparePartsMap(newBiddedSpareParts);
    Map<String, BiddedSparePartEntity> existItemsMap =
        getBiddedSparePartsMap(this.getBiddedSpareParts());

    // Delete item
    for (BiddedSparePartEntity item : this.getBiddedSpareParts()) {
      String deletedItemKey = generateKey(item.getCode());
      if (!newItemsMap.containsKey(deletedItemKey)) {
        item.setDeleted(true);
      }
    }

    // Add item
    for (var newItem : newBiddedSpareParts) {
      String newItemKey = generateKey(newItem.getCode());
      if (!existItemsMap.containsKey(newItemKey)) {
        newItem.setQuotationBid(this);
        this.biddedSpareParts.add(newItem);
      } else {
        // Update existing item
        BiddedSparePartEntity existingItem = existItemsMap.get(newItemKey);
        existingItem.updateGeneral(
            newItem.getCode(),
            newItem.getRefCode(),
            newItem.getTenantId(),
            newItem.getPartNameInput(),
            newItem.getPartNameUnit());
      }
    }
  }

  private static String generateKey(String... args) {
    return String.join(":", args);
  }

  private Map<String, SparePartPriceLineItemEntity> getSparePartPriceLineItemsMap(
      List<SparePartPriceLineItemEntity> sparePartPriceLineItems) {
    return sparePartPriceLineItems.stream()
        .collect(
            Collectors.toMap(
                item -> generateKey(item.getSparePartInputCode(), item.getSegment().name()),
                item -> item));
  }

  private Map<String, AddedSparePartPriceLineItemEntity> getAddedSparePartPriceLineItemsMap(
      List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItems) {
    return addedSparePartPriceLineItems.stream()
        .collect(
            Collectors.toMap(
                item -> generateKey(item.getSparePartInputCode(), item.getSegment().name()),
                item -> item));
  }

  private Map<String, BiddedSparePartEntity> getBiddedSparePartsMap(
      List<BiddedSparePartEntity> biddedSpareParts) {
    return biddedSpareParts.stream()
        .collect(Collectors.toMap(item -> generateKey(item.getCode()), item -> item));
  }

  public Map<String, String> getSparePartsMapName() {
    Map<String, String> result = new HashMap<>();
    this.biddedSpareParts.forEach(
        part -> {
          if (StringUtils.isNotBlank(part.getPartNameInput())) {
            result.put(
                String.format(Consts.SPARE_PART_PATTERN, Consts.BIDDED_SPARE_PART, part.getCode()),
                part.getPartNameInput());
            Arrays.stream(Segment.values())
                .forEach(
                    segment ->
                        result.put(
                            String.format(
                                Consts.SPARE_PART_PRICE_PATTERN,
                                Consts.ADDED_SPARE_PART_PRICE_LINE_ITEM,
                                part.getCode(),
                                segment.name()),
                            part.getPartNameInput()));
          }
        });
    return result;
  }
}
