package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingRequestEntity;
import com.actechx.gf.adapter.repository.JpaAskedSparePartPricingRequestRepository;
import com.actechx.gf.domain.repository.AskedSparePartPricingRequestRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class AskedSparePartPricingRequestRepositoryImpl
    implements AskedSparePartPricingRequestRepository {
  private final JpaAskedSparePartPricingRequestRepository repository;

  @Override
  public List<AskedSparePartPricingRequestEntity> findByTenantIdAndCodesAndQuotationAskCode(
      Long tenantId, List<String> codes, String quotationAskCode) {
    return repository
        .findByTenantIdAndCodeInAndProposalStatusFalseAndQuotationAskPricingQuotationAskCode(
            tenantId, codes, quotationAskCode);
  }

  @Override
  public int saveDataAll(List<AskedSparePartPricingRequestEntity> entities) {
    return repository.saveAll(entities).size();
  }
}
