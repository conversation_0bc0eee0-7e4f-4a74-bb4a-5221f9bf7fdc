package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.controller.form.request.SearchSparePartRequestDto;
import com.actechx.gf.adapter.controller.form.response.SearchSparePartResponseDto;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.repository.JpaAskedSparePartRepository;
import com.actechx.gf.domain.repository.AskedSparePartRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.text.Normalizer;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class AskedSparePartRepositoryImpl implements AskedSparePartRepository {
  private final EntityManager entityManager;
  private final JpaAskedSparePartRepository repository;

  @Value("${spring.jpa.properties.hibernate.default_schema}")
  private String defaultSchema;

  @Override
  @SuppressWarnings("unchecked")
  public Page<SearchSparePartResponseDto> findDistinctPartNameUnit(
      SearchSparePartRequestDto request) {
    int page = request.getPage() - 1;
    int size = request.getSize();

    String schema = defaultSchema;

    StringBuilder sb = new StringBuilder();
    sb.append("SELECT part_name_input, part_name_unit, created_at, COUNT(*) OVER() AS total_count ")
        .append("FROM ( ")
        .append("    SELECT part_name_input, part_name_unit, created_at, ")
        .append(
            "           ROW_NUMBER() OVER (PARTITION BY part_name_input, part_name_unit ORDER BY created_at DESC) AS rn ")
        .append("    FROM \"")
        .append(schema)
        .append("\".asked_spare_parts ")
        .append("    WHERE 1=1 ")
        .append("    AND deleted = false ");

    if (request.getTenantId() != null) {
      sb.append(" AND tenant_id = :tenantId ");
    }
    String normalizedPartNameInput = null;
    if (StringUtils.isNotBlank(request.getPartNameInput())) {
      normalizedPartNameInput =
          Normalizer.normalize(request.getPartNameInput(), Normalizer.Form.NFD)
              .replaceAll("\\p{M}", "")
              .toLowerCase();
      sb.append(" AND LOWER(\"")
          .append(schema)
          .append("\".unaccent_vi(part_name_input)) ")
          .append("LIKE CONCAT('%%', LOWER(:partNameInput), '%%') ");
    }
    sb.append(") sub ")
        .append("WHERE rn = 1 ")
        .append("ORDER BY created_at DESC ")
        .append("LIMIT :limit OFFSET :offset");

    String sql = sb.toString();
    Query nativeQuery = entityManager.createNativeQuery(sql);

    if (request.getTenantId() != null) {
      nativeQuery.setParameter("tenantId", request.getTenantId());
    }
    if (normalizedPartNameInput != null) {
      nativeQuery.setParameter("partNameInput", normalizedPartNameInput);
    }
    nativeQuery.setParameter("limit", size);
    nativeQuery.setParameter("offset", page * size);

    List<Object[]> rows = nativeQuery.getResultList();
    List<SearchSparePartResponseDto> results =
        rows.stream()
            .map(
                row ->
                    new SearchSparePartResponseDto(
                        (String) row[0], (String) row[1], (Instant) row[2]))
            .toList();

    long total = rows.isEmpty() ? 0L : ((Number) rows.getFirst()[3]).longValue();

    return new PageImpl<>(results, PageRequest.of(page, size), total);
  }

  @Override
  public List<AskedSparePartEntity> getByCodeList(List<String> codes) {
    return repository.findByCodeIn(codes);
  }

  @Override
  public Optional<AskedSparePartEntity> getByCode(String code) {
    return repository.findByCode(code);
  }

  @Override
  public List<Long> findExistingIdsByIdsRepo(List<Long> ids) {
    return repository.findExistingIdsByIds(ids);
  }

  @Override
  public Optional<AskedSparePartEntity> findByIdAndTenantIdAndQuotationAskIdRepo(
      Long id, Long tenantId, Long quotationId) {
    return repository.findByIdAndTenantIdAndQuotationAskId(id, tenantId, quotationId);
  }
}
