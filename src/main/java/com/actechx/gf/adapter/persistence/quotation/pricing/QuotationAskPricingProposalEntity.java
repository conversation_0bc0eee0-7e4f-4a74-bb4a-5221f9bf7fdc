package com.actechx.gf.adapter.persistence.quotation.pricing;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.app.utils.Consts;
import jakarta.persistence.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "quotation_asks_pricing_proposal")
public class QuotationAskPricingProposalEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(length = 50, nullable = false)
  private String quotationAskCode;

  @Column(nullable = false)
  private Long replyTenantId;

  @OneToMany(
      mappedBy = "quotationAskPricingProposal",
      cascade = {CascadeType.MERGE, CascadeType.PERSIST},
      fetch = FetchType.LAZY)
  private List<AskedSparePartPricingProposalEntity> sparePartPriceLineItems;

  public void addItems(List<AskedSparePartPricingProposalEntity> items) {
    for (AskedSparePartPricingProposalEntity item : items) {
      item.setQuotationAskPricingProposal(this);
    }
  }

  public Map<String, String> getSparePartsMapName(Map<String, String> askedSparePartsMapName) {
    Map<String, String> result = new HashMap<>();
    for (var entry : askedSparePartsMapName.entrySet()) {
      if (entry.getKey().contains(Consts.ADDED_SPARE_PART_PRICE_LINE_ITEM)) {
        String key =
            entry
                .getKey()
                .replace(
                    Consts.ADDED_SPARE_PART_PRICE_LINE_ITEM, Consts.PRICING_SPARE_PART_LINE_ITEM);
        result.put(key, entry.getValue());
      } else if (entry.getKey().contains(Consts.SPARE_PART_PRICE_LINE_ITEM)) {
        String key =
            entry
                .getKey()
                .replace(Consts.SPARE_PART_PRICE_LINE_ITEM, Consts.PRICING_SPARE_PART_LINE_ITEM);
        result.put(key, entry.getValue());
      }
    }
    return result;
  }
}
