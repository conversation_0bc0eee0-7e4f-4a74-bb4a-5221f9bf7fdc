package com.actechx.gf.adapter.persistence.quotation.ask;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.CarType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(exclude = "quotationAsk", callSuper = true)
@Entity
@Table(name = "asked_vehicles")
@ToString(exclude = "quotationAsk")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class AskedVehicleEntity extends AuditableEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne(mappedBy = "askedVehicle", fetch = FetchType.LAZY)
  @JsonIgnore
  private QuotationAskEntity quotationAsk;

  @Column(name = "car_brand", nullable = false)
  private String carBrand;

  @Column(name = "car_model", nullable = false)
  private String carModel;

  @Column(name = "year_of_manufacture")
  private String yearOfManufacture;

  @Enumerated(EnumType.STRING)
  @Column(name = "car_type", length = 20)
  private CarType carType;

  @Column(name = "trims_level")
  private String trimsLevel;

  @Column(name = "vin", length = 17)
  private String vin;

  protected void updateInfo(
      String carBrand,
      String carModel,
      String yearOfManufacture,
      CarType carType,
      String trimsLevel,
      String vin) {
    this.carBrand = carBrand;
    this.carModel = carModel;
    this.yearOfManufacture = yearOfManufacture;
    this.carType = carType;
    this.trimsLevel = trimsLevel;
    this.vin = vin;
  }
}
