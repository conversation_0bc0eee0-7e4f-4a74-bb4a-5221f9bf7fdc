package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.purchase.order.POSupplierEntity;
import com.actechx.gf.adapter.repository.JpaPOSupplierRepository;
import com.actechx.gf.domain.repository.POSupplierRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class POSupplierRepositoryImpl implements POSupplierRepository {
  private final JpaPOSupplierRepository jpaPOSupplierRepository;

  @Override
  public void store(POSupplierEntity entity) {
    jpaPOSupplierRepository.save(entity);
  }

  @Override
  public Optional<POSupplierEntity> findByPOIdAndSupplierId(Long poId, Long supplierId) {
    return jpaPOSupplierRepository.findByPoIdAndSupplierId(poId, supplierId);
  }
}
