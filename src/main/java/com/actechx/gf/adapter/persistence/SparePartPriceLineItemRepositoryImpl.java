package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.repository.JpaSparePartPriceLineItemRepository;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import com.actechx.gf.domain.repository.SparePartPriceLineItemRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class SparePartPriceLineItemRepositoryImpl implements SparePartPriceLineItemRepository {
  private final JpaSparePartPriceLineItemRepository repository;

  @Override
  public List<SparePartPriceLineItemEntity> getListByCode(String code) {
    return repository.findBySparePartInputCodeOrderBySegmentAscPriceAsc(code);
  }

  @Override
  public List<SparePartPriceLineItemEntity>
      getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
          List<Long> ids, List<QuotationBidStatus> statuses) {
    return repository.findByIdInAndDetailStatusFalseAndQuotationBid_StatusIn(ids, statuses);
  }

  @Override
  public Optional<SparePartPriceLineItemEntity> getById(Long id) {
    return repository.findById(id);
  }

  @Override
  public Optional<SparePartPriceLineItemEntity> getByIdAndDetailStatusFalse(Long id) {
    return repository.findByIdAndDetailStatusFalseAndDeletedFalse(id);
  }

  @Override
  public Optional<SparePartPriceLineItemEntity> getByIdAndDetailStatusTrue(Long id) {
    return repository.findByIdAndDetailStatusTrueAndDeletedFalse(id);
  }

  @Override
  public void saveAllRepo(List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities) {
    repository.saveAll(sparePartPriceLineItemEntities);
  }

  @Override
  public List<Long> findBySparePartInputCodeAndQuotationCodeRepo(
      String sparePartInputCode, String quotationAskCode) {
    return repository.findBySparePartInputCodeAndQuotationCode(
        sparePartInputCode, quotationAskCode);
  }

  @Override
  public Optional<SparePartPriceLineItemEntity> getSparePartPriceLineItemProposal(
      String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId) {
    return repository
        .findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
            sparePartInputCode, segment, quotationAskCode, tenantId);
  }
}
