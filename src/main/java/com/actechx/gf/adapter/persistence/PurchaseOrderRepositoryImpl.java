package com.actechx.gf.adapter.persistence;

import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.persistence.mapper.PurchaseOrderEntityMapper;
import com.actechx.gf.adapter.persistence.purchase.order.POTransitionHistoryEntity;
import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.adapter.repository.*;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PurchaseOrderRepositoryImpl implements PurchaseOrderRepository {
  private final JpaPurchaseOrderRepository purchaseOrderRepository;
  private final JpaPOProductRepository poProductRepository;
  private final JpaPOQuotationRefRepository poQuotationRefRepository;
  private final JpaPOSupplierRepository poSupplierRepository;
  private final JpaPOTransitionHistoryRepository poTransitionHistoryRepository;
  private final PurchaseOrderEntityMapper purchaseOrderEntityMapper;
  private final EntityManagerFactory entityManagerFactory;

  @Override
  public PurchaseOrder create(PurchaseOrder purchaseOrder) {
    PurchaseOrderEntity purchaseOrderEntity = purchaseOrderEntityMapper.toEntity(purchaseOrder);
    var poProductEntities =
        purchaseOrder.getPoProducts().stream().map(purchaseOrderEntityMapper::toEntity).toList();
    var poQuotationRefEntities =
        purchaseOrder.getPoQuotationRefs().stream()
            .map(purchaseOrderEntityMapper::toEntity)
            .toList();
    var poSupplier = purchaseOrderEntityMapper.toEntity(purchaseOrder.getPoSupplier());

    savePOTransitionHistory(purchaseOrderEntity);
    var savedPurcchaseOrderEntity = purchaseOrderRepository.save(purchaseOrderEntity);
    var savedPOProductEntities = poProductRepository.saveAll(poProductEntities);
    poQuotationRefRepository.saveAll(poQuotationRefEntities);
    poSupplierRepository.save(poSupplier);

    var savedPurchaseOrder = purchaseOrderEntityMapper.toModel(savedPurcchaseOrderEntity);
    var savedPOProducts =
        savedPOProductEntities.stream().map(purchaseOrderEntityMapper::toModel).toList();
    savedPurchaseOrder.setPoProducts(savedPOProducts);
    return savedPurchaseOrder;
  }

  @Override
  public PurchaseOrderEntity save(PurchaseOrderEntity purchaseOrderEntity) {
    savePOTransitionHistory(purchaseOrderEntity);
    return purchaseOrderRepository.save(purchaseOrderEntity);
  }

  @Override
  public Optional<PurchaseOrderEntity> findByPurchaseRequestCodeAndSupplierId(
      String purchaseRequestCode, Long supplierId) {
    return purchaseOrderRepository.findByPurchaseRequestCodeAndSupplierId(
        purchaseRequestCode, supplierId);
  }

  @Override
  public List<PurchaseOrderEntity> findByPrIdAndTenantId(Long prId, Long tenantId) {
    return purchaseOrderRepository.findByPrIdAndPurchaserId(prId, tenantId);
  }

  @Override
  public Page<PurchaseOrderEntity> searchData(
      Specification<PurchaseOrderEntity> spec, PageRequest pageable) {
    return purchaseOrderRepository.findAll(spec, pageable);
  }

  @Override
  public Optional<PurchaseOrderEntity> findByIdAndTenantId(Long id, Long tenantId) {
    return purchaseOrderRepository.findByIdAndPurchaserId(id, tenantId);
  }

  @Override
  public Optional<PurchaseOrderEntity> findByCodeAndTenantId(String code, Long tenantId) {
    return purchaseOrderRepository.findByCodeAndPurchaserId(code, tenantId);
  }

  @Override
  public List<PurchaseOrderEntity> findByPurchaseOrderCodeOrSaleOrderCode(
      List<String> purchaseOrderCode, List<String> saleOrderCode) {
    return purchaseOrderRepository.findByCodeInOrSaleOrderCodeIn(purchaseOrderCode, saleOrderCode);
  }

  @Override
  public List<PurchaseOrderEntity> findByPurchaseRequestCode(String purchaseRequestCode) {
    return purchaseOrderRepository.findByPurchaseRequestCode(purchaseRequestCode);
  }

  @Override
  public Long countByPurchaserIdAndStatus(Long purchaserId, POStatusEnum status) {
    return purchaseOrderRepository.countByPurchaserIdAndStatus(purchaserId, status);
  }

  @Override
  public Long countByPurchaserIdAndStatusAndUpdatedAtBetween(Long purchaserId, POStatusEnum status, Instant startTime, Instant endTime) {
    return purchaseOrderRepository.countByPurchaserIdAndStatusAndUpdatedAtBetween(purchaserId, status, startTime, endTime);
  }

  @Override
  public BigDecimal sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(Long purchaserId, POStatusEnum status, Instant startTime, Instant endTime) {
    return purchaseOrderRepository.sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(purchaserId, status, startTime, endTime);
  }

  private void savePOTransitionHistory(PurchaseOrderEntity purchaseOrderEntity) {
    PurchaseOrderEntity oldEntity = null;

    if (purchaseOrderEntity.getId() != null) {
      try (EntityManager em2 = entityManagerFactory.createEntityManager()) {
        oldEntity = em2.find(PurchaseOrderEntity.class, purchaseOrderEntity.getId());
      }
    }

    // Chỉ lưu history nếu có thay đổi stage
    boolean isNew = oldEntity == null; // Trường hợp tạo mới
    boolean stageChanged =
        !isNew
            && oldEntity.getStage() != null
            && purchaseOrderEntity.getStage() != null
            && !oldEntity.getStage().equals(purchaseOrderEntity.getStage());

    if (isNew || stageChanged) {
      var lastTransitionHistory =
          poTransitionHistoryRepository
              .findTopByPoIdOrderByCreatedAtDesc(purchaseOrderEntity.getId())
              .orElse(null);
      List<String> previousStages = new ArrayList<>();
      if (lastTransitionHistory != null) {
        previousStages = JsonUtils.toList(lastTransitionHistory.getPreviousStages(), String.class);
      }
      // Append stage mới
      previousStages.add(purchaseOrderEntity.getStage().name());

      // Tạo bản ghi history mới
      POTransitionHistoryEntity history =
          new POTransitionHistoryEntity(
              purchaseOrderEntity.getId(),
              JsonUtils.toJson(purchaseOrderEntity),
              JsonUtils.toJson(previousStages));

      poTransitionHistoryRepository.save(history);
    }
  }
}
