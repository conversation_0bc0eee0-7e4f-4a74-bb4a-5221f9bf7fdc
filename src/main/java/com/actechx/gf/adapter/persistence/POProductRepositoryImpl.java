package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.repository.JpaPOProductRepository;
import com.actechx.gf.domain.repository.POProductRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class POProductRepositoryImpl implements POProductRepository {
  private final JpaPOProductRepository jpaPOProductRepository;

  @Override
  public List<POProductEntity> findByPOId(Long poId) {
    return jpaPOProductRepository.findByPoId(poId);
  }

  @Override
  public POProductEntity store(POProductEntity entity) {
    return jpaPOProductRepository.save(entity);
  }

  @Override
  public List<POProductEntity> findByPrId(Long prId) {
    return jpaPOProductRepository.findByPrId(prId);
  }
}
