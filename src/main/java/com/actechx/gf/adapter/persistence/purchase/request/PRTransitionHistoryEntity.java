package com.actechx.gf.adapter.persistence.purchase.request;

import com.actechx.common.spring.jpa.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "pr_transition_history")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PRTransitionHistoryEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "pr_id", nullable = false)
  private Long prId;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "previous_stages", columnDefinition = "jsonb")
  private String previousStages;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "transition_data", columnDefinition = "jsonb")
  private String transitionData;

  public PRTransitionHistoryEntity(Long prId, String transitionData, String previousStages) {
    this.prId = prId;
    this.transitionData = transitionData;
    this.previousStages = previousStages;
  }
}
