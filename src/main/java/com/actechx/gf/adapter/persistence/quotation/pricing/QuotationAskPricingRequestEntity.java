package com.actechx.gf.adapter.persistence.quotation.pricing;

import com.actechx.common.spring.jpa.AuditableEntity;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "quotation_asks_pricing_request")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QuotationAskPricingRequestEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(length = 50, nullable = false)
  private String quotationAskCode;

  @Column(nullable = false)
  private Long originTenantId;

  @OneToMany(
      mappedBy = "quotationAskPricing",
      cascade = {CascadeType.MERGE, CascadeType.PERSIST},
      fetch = FetchType.LAZY)
  private List<AskedSparePartPricingRequestEntity> askedSpareParts;

  public void addItems(List<AskedSparePartPricingRequestEntity> items) {
    for (AskedSparePartPricingRequestEntity item : items) {
      item.setQuotationAskPricing(this);
    }
  }
}
