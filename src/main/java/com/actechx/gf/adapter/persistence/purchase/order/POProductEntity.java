package com.actechx.gf.adapter.persistence.purchase.order;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.Segment;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "po_products")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class POProductEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "po_id", nullable = false)
  private Long poId;

  @Column(name = "product_id", nullable = false)
  private Long productId;

  @Column(nullable = false, columnDefinition = "varchar(255) default ''")
  private String requestedProductName;

  @Column(name = "quantity", nullable = false)
  private Long quantity;

  @Column(name = "unit_price", nullable = false)
  private BigDecimal unitPrice;

  @Column(name = "unit", nullable = false)
  private String unit;

  @Enumerated(EnumType.STRING)
  @Column(name = "segment", length = 10, nullable = false)
  private Segment segment;

  @Column(name = "supplier_id", nullable = false)
  private Long supplierId;

  @Column(name = "quotation_ask_code")
  private String quotationAskCode;

  public void updateInfo(Long quantity, BigDecimal unitPrice, String unit) {
    this.quantity = quantity;
    this.unitPrice = unitPrice;
    this.unit = unit;
  }

  public void confirm(boolean checked) {
    if (!checked) {
      this.quantity = 0L;
    }
  }
}
