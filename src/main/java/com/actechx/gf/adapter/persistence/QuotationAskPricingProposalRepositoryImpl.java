package com.actechx.gf.adapter.persistence;

import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.gf.adapter.persistence.mapper.QuotationAskHistoryEntityMapper;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingProposalEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingRequestEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import com.actechx.gf.adapter.repository.*;
import com.actechx.gf.app.mapper.QuotationAskPricingMapper;
import com.actechx.gf.app.utils.Consts;
import com.actechx.gf.app.utils.ObjectDiffer;
import com.actechx.gf.domain.model.enums.OwnerType;
import com.actechx.gf.domain.model.enums.UpdatedType;
import com.actechx.gf.domain.model.quotationpricing.PricingNumberSuccess;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposal;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposalResponseDomain;
import com.actechx.gf.domain.repository.QuotationAskPricingProposalRepository;
import com.actechx.gf.domain.repository.SparePartPriceLineItemRepository;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Slf4j
@Repository
@RequiredArgsConstructor
public class QuotationAskPricingProposalRepositoryImpl
    implements QuotationAskPricingProposalRepository {
  private final QuotationAskPricingMapper pricingMapper;
  private final JpaQuotationAskPricingProposalRepository repository;
  private final JpaAskedSparePartPricingProposalRepository
      jpaAskedSparePartPricingProposalRepository;
  private final JpaAskedSparePartPricingRequestRepository jpaAskedSparePartPricingRequestRepository;
  private final SparePartPriceLineItemRepository sparePartPriceLineItemRepository;
  private final JpaSparePartPriceLineItemRepository jpaSparePartPriceLineItemRepository;
  private final JpaAddedSparePartPriceLineItemRepository jpaAddedSparePartPriceLineItemRepository;
  private final JpaAskedSparePartRepository japAskedSparePartRepository;
  private final JpaQuotationAskHistoryRepository jpaAskHistoryRepository;
  private final QuotationAskHistoryEntityMapper quotationAskHistoryEntityMapper;
  private final JpaQuotationAskRepository jpaAskRepository;
  private final JpaQuotationBidRepository jpaBidRepository;

  @Override
  public Optional<QuotationAskPricingProposalEntity> getByQuotationAskCodeAndReplyTenantId(
      String quotationAskCode, Long replyTenantId) {
    return repository.findByQuotationAskCodeAndReplyTenantId(quotationAskCode, replyTenantId);
  }

  @Override
  public PricingNumberSuccess createData(QuotationAskPricingProposal proposal) {
    String quotationAskCode = proposal.getQuotationAskCode();
    Long tenantId = proposal.getReplyTenantId();
    QuotationAskPricingProposalEntity quotationAskPricingProposalEntity =
        pricingMapper.toPricingProposalEntity(proposal);
    List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntities =
        quotationAskPricingProposalEntity.getSparePartPriceLineItems();
    quotationAskPricingProposalEntity.addItems(askedSparePartPricingProposalEntities);
    repository.save(quotationAskPricingProposalEntity);
    this.updateProposalStatusSparePartPricingRequest(askedSparePartPricingProposalEntities);
    this.updateQuotationBid(quotationAskCode, tenantId, askedSparePartPricingProposalEntities);
    PricingNumberSuccess pricingNumberSuccess = new PricingNumberSuccess();
    pricingNumberSuccess.setCreateNumber(askedSparePartPricingProposalEntities.size());
    return pricingNumberSuccess;
  }

  @Override
  public PricingNumberSuccess updateData(
      QuotationAskPricingProposalEntity proposalEntity, QuotationAskPricingProposal proposal) {
    String quotationAskCode = proposal.getQuotationAskCode();
    Long tenantId = proposal.getReplyTenantId();
    QuotationAskPricingProposalEntity clone = quotationAskHistoryEntityMapper.clone(proposalEntity);
    List<QuotationAskPricingProposal.AskedSparePartPricingProposal> askedSparePartPricingProposals =
        proposal.getSparePartPriceLineItems();
    List<AskedSparePartPricingProposalEntity> proposalEntities =
        proposalEntity.getSparePartPriceLineItems();
    AtomicInteger updateCount = new AtomicInteger();
    AtomicInteger createCount = new AtomicInteger();
    List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntities =
        new ArrayList<>();
    askedSparePartPricingProposals.forEach(
        x -> {
          List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntityList =
              proposalEntities.stream()
                  .filter(
                      a ->
                          a.getSparePartInputCode().equals(x.getSparePartInputCode())
                              && a.getSegment().equals(x.getSegment()))
                  .toList();
          if (CollectionUtils.isEmpty(askedSparePartPricingProposalEntityList)) {
            AskedSparePartPricingProposalEntity askedSparePartPricingProposalEntity =
                pricingMapper.toPricingProposalEntity(x);
            askedSparePartPricingProposalEntity.setQuotationAskPricingProposal(proposalEntity);
            askedSparePartPricingProposalEntities.add(askedSparePartPricingProposalEntity);
            updateCount.getAndIncrement();
          }
          //          else {
          //            if (askedSparePartPricingProposalEntityList.size() > 1) {
          //              throw new ResourceNotFoundException(
          //                  "askedSparePartPricingProposalEntityList duplication",
          //                  askedSparePartPricingProposalEntityList.size());
          //            }
          //            AskedSparePartPricingProposalEntity askedSparePartPricingProposalEntity =
          //                askedSparePartPricingProposalEntityList.getFirst();
          //            askedSparePartPricingProposalEntity.setMaterialPrice(x.getMaterialPrice());
          //
          // askedSparePartPricingProposalEntity.setServicingPrice(x.getServicingPrice());
          //            askedSparePartPricingProposalEntity.setUpdatedStatus(true);
          //            updateCount.getAndIncrement();
          //          }
        });
    if (!CollectionUtils.isEmpty(askedSparePartPricingProposalEntities)) {
      jpaAskedSparePartPricingProposalRepository.saveAll(askedSparePartPricingProposalEntities);
      this.updateQuotationBid(quotationAskCode, tenantId, askedSparePartPricingProposalEntities);
    }
    //    repository.save(proposalEntity);
    this.updateProposalStatusSparePartPricingRequest(askedSparePartPricingProposalEntities);
    PricingNumberSuccess pricingNumberSuccess = new PricingNumberSuccess();
    pricingNumberSuccess.setCreateNumber(createCount.getAndIncrement());
    pricingNumberSuccess.setUpdateNumber(updateCount.getAndIncrement());

    // Store History
    if (!CollectionUtils.isEmpty(askedSparePartPricingProposalEntities)) {
      QuotationAskPricingProposalEntity newData = new QuotationAskPricingProposalEntity();
      List<AskedSparePartPricingProposalEntity> newListData = new ArrayList<>();
      newListData.addAll(proposalEntity.getSparePartPriceLineItems());
      newListData.addAll(askedSparePartPricingProposalEntities);
      newData.setSparePartPriceLineItems(newListData);
      storeHistory(clone, newData);
    }
    return pricingNumberSuccess;
  }

  @Override
  public QuotationAskPricingProposalResponseDomain getQuotationAskPricingProposal(
      Long sparePartPriceLineItemId) {
    SparePartPriceLineItemEntity sparePartPriceLineItemEntity =
        sparePartPriceLineItemRepository
            .getById(sparePartPriceLineItemId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "sparePartPriceLineItemId not found ", sparePartPriceLineItemId));
    AskedSparePartEntity askedSparePartEntity =
        japAskedSparePartRepository
            .findByCode(sparePartPriceLineItemEntity.getSparePartInputCode())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "AskedSparePartEntity not found  code",
                        sparePartPriceLineItemEntity.getSparePartInputCode()));
    List<QuotationAskPricingProposalResponseDomain.AskedSparePartPricingProposalResponse>
        askedSparePartPricingProposalResponses = new ArrayList<>();
    List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntities =
        jpaAskedSparePartPricingProposalRepository
            .findBySparePartInputCodeAndSegmentAndQuotationAskPricingProposal_QuotationAskCodeAndQuotationAskPricingProposal_ReplyTenantId(
                sparePartPriceLineItemEntity.getSparePartInputCode(),
                sparePartPriceLineItemEntity.getSegment(),
                sparePartPriceLineItemEntity.getQuotationBid().getQuotationAskCode(),
                sparePartPriceLineItemEntity.getQuotationBid().getTenantId());
    askedSparePartPricingProposalEntities.forEach(
        x -> {
          QuotationAskPricingProposalResponseDomain.AskedSparePartPricingProposalResponse
              askedSparePartPricingProposalResponse = pricingMapper.toProposalResponsesDomain(x);
          if (StringUtils.isNotBlank(x.getRefCode())) {
            AskedSparePartPricingRequestEntity askedSparePartPricingRequestEntity =
                jpaAskedSparePartPricingRequestRepository
                    .findByCodeAndTenantIdAndSegmentAndProposalStatusTrueAndQuotationAskPricing_QuotationAskCodeAndRefCode(
                        x.getSparePartInputCode(),
                        x.getQuotationAskPricingProposal().getReplyTenantId(),
                        x.getSegment(),
                        x.getQuotationAskPricingProposal().getQuotationAskCode(),
                        x.getRefCode())
                    .orElseThrow(
                        () ->
                            new ResourceNotFoundException(
                                "AskedSparePartEntity not found  code", x.getRefCode()));
            askedSparePartPricingProposalResponse.setPartNameInput(
                askedSparePartPricingRequestEntity.getPartNameInput());
          } else {
            askedSparePartPricingProposalResponse.setPartNameInput(
                askedSparePartEntity.getPartNameInput());
          }
          askedSparePartPricingProposalResponses.add(askedSparePartPricingProposalResponse);
        });
    QuotationAskPricingProposalResponseDomain responseDomain =
        new QuotationAskPricingProposalResponseDomain();
    responseDomain.setId(sparePartPriceLineItemEntity.getId());
    responseDomain.setQuotationAskCode(sparePartPriceLineItemEntity.getSparePartInputCode());
    responseDomain.setSparePartPriceLineItems(askedSparePartPricingProposalResponses);
    return responseDomain;
  }

  private void updateProposalStatusSparePartPricingRequest(
      List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntities) {
    List<AskedSparePartPricingRequestEntity> askedSparePartPricingRequestEntities =
        new ArrayList<>();
    askedSparePartPricingProposalEntities.forEach(
        x -> {
          QuotationAskPricingProposalEntity quotationAskPricingProposal =
              x.getQuotationAskPricingProposal();
          Optional<AskedSparePartPricingRequestEntity> askedSparePartPricingProposalEntityOptional =
              jpaAskedSparePartPricingRequestRepository
                  .findByCodeAndTenantIdAndSegmentAndProposalStatusFalseAndQuotationAskPricing_QuotationAskCode(
                      x.getSparePartInputCode(),
                      quotationAskPricingProposal.getReplyTenantId(),
                      x.getSegment(),
                      quotationAskPricingProposal.getQuotationAskCode());
          if (askedSparePartPricingProposalEntityOptional.isEmpty()) {
            String error =
                x.getSparePartInputCode()
                    + " - "
                    + quotationAskPricingProposal.getReplyTenantId()
                    + " - "
                    + x.getSegment()
                    + " - "
                    + quotationAskPricingProposal.getQuotationAskCode();
            throw new ResourceNotFoundException(
                "AskedSparePartPricingRequestEntity Not found by code , tenantId, segment, quotationAskCode ",
                error);
          }
          AskedSparePartPricingRequestEntity askedSparePartPricingRequestEntity =
              askedSparePartPricingProposalEntityOptional.get();
          askedSparePartPricingRequestEntity.setProposalStatus(true);
          askedSparePartPricingRequestEntities.add(askedSparePartPricingRequestEntity);
        });
    jpaAskedSparePartPricingRequestRepository.saveAll(askedSparePartPricingRequestEntities);
  }

  private void storeHistory(
      QuotationAskPricingProposalEntity clone, QuotationAskPricingProposalEntity newData) {
    if (clone == null) return;
    List<ObjectDiffer.FieldChange> changes =
        new ArrayList<>(
            ObjectDiffer.compareWithIdMatch(
                clone.getSparePartPriceLineItems(),
                newData.getSparePartPriceLineItems(),
                e -> e.getSparePartInputCode() + "-" + e.getSegment().name(),
                List.of("materialPrice", "servicingPrice"),
                Consts.PRICING_SPARE_PART_LINE_ITEM));
    if (!changes.isEmpty()) {
      QuotationAskHistoryEntity history =
          new QuotationAskHistoryEntity(
              clone.getQuotationAskCode(),
              clone.getReplyTenantId(),
              OwnerType.VENDOR,
              UpdatedType.PRICING,
              changes.stream().map(quotationAskHistoryEntityMapper::toJpaEntity).toList());
      QuotationAskEntity quotationAskEntity =
          jpaAskRepository.findByCode(clone.getQuotationAskCode()).orElseThrow();
      QuotationBidEntity quotationBidEntity =
          jpaBidRepository
              .findByQuotationAskCodeAndTenantId(
                  clone.getQuotationAskCode(), clone.getReplyTenantId())
              .orElseThrow();
      Map<String, String> mapPartName = new HashMap<>();
      mapPartName.putAll(quotationAskEntity.getSparePartsMapName());
      mapPartName.putAll(quotationBidEntity.getSparePartsMapName());
      mapPartName.putAll(clone.getSparePartsMapName(mapPartName));
      history.enrichNote(mapPartName);
      jpaAskHistoryRepository.save(history);
    }
  }

  private void updateQuotationBid(
      String quotationAskCode,
      Long tenantId,
      List<AskedSparePartPricingProposalEntity> askedSparePartPricingProposalEntities) {
    List<AskedSparePartPricingProposalEntity> askedSparePartPricingEntities =
        askedSparePartPricingProposalEntities.stream()
            .filter(x -> StringUtils.isBlank(x.getRefCode()))
            .toList();

    // phụ tùng chính
    List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities = new ArrayList<>();
    askedSparePartPricingEntities.forEach(
        x -> {
          SparePartPriceLineItemEntity sparePartPriceLineItemEntity =
              jpaSparePartPriceLineItemRepository
                  .findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
                      x.getSparePartInputCode(), x.getSegment(), quotationAskCode, tenantId)
                  .orElseThrow(
                      () ->
                          new ResourceNotFoundException(
                              "SparePartPriceLineItemEntity not found by code, tenant",
                              x.getSparePartInputCode() + "-" + x.getSegment()));
          sparePartPriceLineItemEntity.setReceiveDetailStatus(true);
          sparePartPriceLineItemEntity.setMaterialPrice(x.getMaterialPrice());
          sparePartPriceLineItemEntity.setServicingPrice(x.getServicingPrice());
          sparePartPriceLineItemEntities.add(sparePartPriceLineItemEntity);
        });

    // phụ tùng đính kèm
    List<AskedSparePartPricingProposalEntity> addAskedSparePartPricingEntities =
        askedSparePartPricingProposalEntities.stream()
            .filter(x -> StringUtils.isNotBlank(x.getRefCode()))
            .toList();
    List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntities = new ArrayList<>();
    addAskedSparePartPricingEntities.forEach(
        x -> {
          AddedSparePartPriceLineItemEntity addedSparePartPriceLineItemEntity =
              jpaAddedSparePartPriceLineItemRepository
                  .findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
                      x.getSparePartInputCode(), x.getSegment(), quotationAskCode, tenantId)
                  .orElseThrow(
                      () ->
                          new ResourceNotFoundException(
                              "SparePartPriceLineItemEntity not found by code, tenant",
                              x.getSparePartInputCode() + "-" + x.getSegment()));
          addedSparePartPriceLineItemEntity.setReceiveDetailStatus(true);
          addedSparePartPriceLineItemEntity.setMaterialPrice(x.getMaterialPrice());
          addedSparePartPriceLineItemEntity.setServicingPrice(x.getServicingPrice());
          addedSparePartPriceLineItemEntities.add(addedSparePartPriceLineItemEntity);
        });

    jpaSparePartPriceLineItemRepository.saveAll(sparePartPriceLineItemEntities);
    jpaAddedSparePartPriceLineItemRepository.saveAll(addedSparePartPriceLineItemEntities);
  }
}
