package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.repository.JpaAddedSparePartPriceLineItemRepository;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import com.actechx.gf.domain.repository.AddedSparePartPriceLineItemRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class AddedSparePartPriceLineItemRepositoryImpl
    implements AddedSparePartPriceLineItemRepository {
  private final JpaAddedSparePartPriceLineItemRepository repository;

  @Override
  public List<AddedSparePartPriceLineItemEntity>
      getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
          List<Long> ids, List<QuotationBidStatus> statuses) {
    return repository.findByIdInAndDetailStatusFalseAndQuotationBid_StatusIn(ids, statuses);
  }

  @Override
  public Optional<AddedSparePartPriceLineItemEntity> getByIdAndDetailStatusFalse(Long id) {
    return repository.findByIdAndDetailStatusFalse(id);
  }

  @Override
  public void saveAllRepo(
      List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntities) {
    repository.saveAll(addedSparePartPriceLineItemEntities);
  }

  @Override
  public Optional<AddedSparePartPriceLineItemEntity> getAddSparePartPriceLineItemProposal(
      String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId) {
    return repository
        .findBySparePartInputCodeAndSegmentAndQuotationBid_QuotationAskCodeAndQuotationBid_TenantIdAndDetailStatusTrue(
            sparePartInputCode, segment, quotationAskCode, tenantId);
  }
}
