package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.controller.form.response.PurchaseOrderResponseDto;
import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.persistence.purchase.order.POQuotationRefEntity;
import com.actechx.gf.adapter.persistence.purchase.order.POSupplierEntity;
import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.domain.model.purchaseorder.POProduct;
import com.actechx.gf.domain.model.purchaseorder.POQuotationRef;
import com.actechx.gf.domain.model.purchaseorder.POSupplier;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PurchaseOrderEntityMapper {
  @Mapping(target = "version", ignore = true)
  PurchaseOrderEntity toEntity(PurchaseOrder purchaseOrder);

  @Mapping(target = "poProducts", ignore = true)
  @Mapping(target = "poQuotationRefs", ignore = true)
  @Mapping(target = "poSupplier", ignore = true)
  @Mapping(target = "domainEvents", ignore = true)
  PurchaseOrder toModel(PurchaseOrderEntity entity);

  POProductEntity toEntity(POProduct poProduct);

  POProduct toModel(POProductEntity entity);

  POQuotationRefEntity toEntity(POQuotationRef poQuotationRef);

  POQuotationRef toModel(POQuotationRefEntity entity);

  POSupplierEntity toEntity(POSupplier model);

  POSupplier toModel(POSupplierEntity entity);

  @Mapping(target = "poProducts", ignore = true)
  PurchaseOrderResponseDto toResponseDto(PurchaseOrderEntity purchaseOrder);
}
