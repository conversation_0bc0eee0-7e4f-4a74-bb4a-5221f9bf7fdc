package com.actechx.gf.adapter.persistence.quotation.ask;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.OwnerType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(exclude = "quotationAsk", callSuper = true)
@Entity
@Table(name = "asked_attachments")
@ToString(exclude = "quotationAsk")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class AskedAttachmentEntity extends AuditableEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "quotation_ask_id", nullable = false)
  @JsonIgnore
  private QuotationAskEntity quotationAsk;

  @Column(name = "attachment_url", columnDefinition = "TEXT", nullable = false)
  private String attachmentUrl;

  @Enumerated(EnumType.STRING)
  @Column(name = "owner", nullable = false, length = 20)
  private OwnerType owner;

  @Column(name = "note", columnDefinition = "TEXT")
  private String note;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  private boolean deleted;

  protected void updateInfo(String attachmentUrl, OwnerType owner, String note) {
    this.attachmentUrl = attachmentUrl;
    this.owner = owner;
    this.note = note;
  }
}
