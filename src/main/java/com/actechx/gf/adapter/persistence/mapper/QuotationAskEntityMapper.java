package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.persistence.quotation.ask.*;
import com.actechx.gf.domain.model.quotationask.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface QuotationAskEntityMapper {

  @Mapping(target = "version", ignore = true)
  @Mapping(target = "spareParts", ignore = true)
  QuotationAskEntity toJpaEntity(QuotationAsk domain);

  @Mapping(target = "domainEvents", ignore = true)
  QuotationAsk toDomain(QuotationAskEntity jpaEntity);

  @Mapping(target = "quotationAsk", ignore = true)
  AskedVehicleEntity toJpaEntity(AskedVehicle domain);

  @Mapping(target = "id", source = "id")
  AskedVehicle toDomain(AskedVehicleEntity jpaEntity);

  @Mapping(target = "quotationAsk", ignore = true)
  AskedAttachmentEntity toJpaEntity(AskedAttachment domain);

  @Mapping(target = "id", source = "id")
  AskedAttachment toDomain(AskedAttachmentEntity jpaEntity);

  @Mapping(target = "quotationAsk", ignore = true)
  AskedSparePartEntity toJpaEntity(AskedSparePart domain);

  @Mapping(target = "id", source = "id")
  AskedSparePart toDomain(AskedSparePartEntity jpaEntity);

  @Mapping(target = "version", ignore = true)
  @Mapping(target = "spareParts", ignore = true)
  @Mapping(target = "attachments", ignore = true)
  @Mapping(target = "askedVehicle", ignore = true)
  @Mapping(target = "sparePartsMapName", ignore = true)
  QuotationAskEntity toJpaEntity(QuotationAsk domain, @MappingTarget QuotationAskEntity jpaEntity);
}
