package com.actechx.gf.adapter.persistence.purchase.order;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "po_quotation_ref")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class POQuotationRefEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "po_id", nullable = false)
  private Long poId;

  @Column(name = "quotation_ask_code", nullable = false)
  private String quotationAskCode;
}
