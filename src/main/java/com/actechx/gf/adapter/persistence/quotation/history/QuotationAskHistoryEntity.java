package com.actechx.gf.adapter.persistence.quotation.history;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.OwnerType;
import com.actechx.gf.domain.model.enums.UpdatedType;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.*;

@Entity
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "quotation_ask_histories")
@EqualsAndHashCode(callSuper = false)
public class QuotationAskHistoryEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(length = 50, nullable = false)
  private String quotationAskCode;

  private Long updatedByTenantId;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private OwnerType updatedRole;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private UpdatedType updatedType;

  @OneToMany(
      mappedBy = "quotationAskHistory",
      cascade = CascadeType.PERSIST,
      fetch = FetchType.LAZY)
  private List<QuotationAskHistoryItemEntity> items = new ArrayList<>();

  public QuotationAskHistoryEntity(
      String quotationAskCode,
      Long updatedByTenantId,
      OwnerType updatedRole,
      UpdatedType updatedType,
      List<QuotationAskHistoryItemEntity> items) {
    this.quotationAskCode = quotationAskCode;
    this.updatedByTenantId = updatedByTenantId;
    this.updatedRole = updatedRole;
    this.updatedType = updatedType;
    items.forEach(item -> item.setQuotationAskHistory(this));
    this.items = items;
  }

  public void enrichNote(Map<String, String> mapPartName) {
    for (QuotationAskHistoryItemEntity item : this.items) {
      if (StringUtils.isNotBlank(item.getFieldName())) {
        int lastDotIndex = item.getFieldName().lastIndexOf('.');
        String key =
            lastDotIndex != -1
                ? item.getFieldName().substring(0, lastDotIndex)
                : item.getFieldName();
        String note = mapPartName.getOrDefault(key, "");
        item.setNote(note);
      }
    }
  }
}
