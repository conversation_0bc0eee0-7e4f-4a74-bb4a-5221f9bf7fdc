package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.mapper.QuotationAskEntityMapper;
import com.actechx.gf.adapter.persistence.mapper.QuotationAskHistoryEntityMapper;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedAttachmentEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedVehicleEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import com.actechx.gf.adapter.repository.JpaQuotationAskHistoryRepository;
import com.actechx.gf.adapter.repository.JpaQuotationAskRepository;
import com.actechx.gf.app.utils.Consts;
import com.actechx.gf.app.utils.ObjectDiffer;
import com.actechx.gf.domain.model.enums.OwnerType;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.enums.UpdatedType;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.hibernate.Hibernate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class QuotationAskRepositoryImpl implements QuotationAskRepository {

  private final JpaQuotationAskRepository jpaRepository;
  private final JpaQuotationAskHistoryRepository jpaHistoryRepository;
  private final QuotationAskEntityMapper mapper;
  private final QuotationAskHistoryEntityMapper quotationAskHistoryEntityMapper;
  private final EntityManager entityManager;
  private final DBUtils dbUtils;

  @Override
  public QuotationAsk save(QuotationAsk quotationAsk, String tenantType) {
    Long tenantId = quotationAsk.getTenantId();
    QuotationAskEntity jpaEntity = mapper.toJpaEntity(quotationAsk);
    jpaEntity.setIsInvoiceRequired(false);
    // Set relationships
    if (jpaEntity.getAskedVehicle() != null) {
      jpaEntity.getAskedVehicle().setQuotationAsk(jpaEntity);
    }
    if (jpaEntity.getAttachments() != null) {
      jpaEntity
          .getAttachments()
          .forEach(
              attachment -> {
                attachment.setQuotationAsk(jpaEntity);
                attachment.setOwner(OwnerType.GARAGE);
              });
    }
    addSparePart(quotationAsk, jpaEntity, tenantId, tenantType);

    QuotationAskEntity savedEntity = jpaRepository.save(jpaEntity);
    return mapper.toDomain(savedEntity);
  }

  @Override
  public Optional<QuotationAsk> findByIdAndTenantId(Long id, Long tenantId) {
    return jpaRepository.findByIdAndTenantId(id, tenantId).map(mapper::toDomain);
  }

  @Override
  public Page<QuotationAskEntity> findAll(
      Specification<QuotationAskEntity> spec, Pageable pageable) {
    return jpaRepository.findAll(spec, pageable);
  }

  @Override
  public Optional<QuotationAsk> findByCode(String code) {
    return jpaRepository.findByCode(code).map(mapper::toDomain);
  }

  @Override
  public Optional<QuotationAsk> findByCodeAndTenantId(String code, Long tenantId) {
    return jpaRepository.findByCodeAndTenantId(code, tenantId).map(mapper::toDomain);
  }

  @Override
  public List<QuotationAsk> findUnprocessedBatch(int batchSize) {
    String jpql =
        """
            SELECT qa FROM QuotationAskEntity qa
            WHERE qa.isProcessed = false
            AND qa.status = com.actechx.gf.domain.model.enums.QuotationStatus.OPEN
            ORDER BY qa.createdAt ASC
            """;

    List<QuotationAskEntity> entities =
        entityManager
            .createQuery(jpql, QuotationAskEntity.class)
            .setMaxResults(batchSize)
            .getResultList();

    return entities.stream().map(mapper::toDomain).collect(Collectors.toList());
  }

  @Override
  public void lockForProcessing(List<Long> ids) {
    if (ids.isEmpty()) return;

    String jpql =
        """
            SELECT qa FROM QuotationAskEntity qa
            WHERE qa.id IN :ids
            AND qa.isProcessed = false
            """;

    entityManager
        .createQuery(jpql, QuotationAskEntity.class)
        .setParameter("ids", ids)
        .setLockMode(LockModeType.PESSIMISTIC_WRITE)
        .getResultList();
  }

  @Override
  public QuotationAsk store(QuotationAsk quotationAsk, boolean ignoreValueType) {
    var quotationAskEntityOptional = jpaRepository.findByCode(quotationAsk.getCode());
    QuotationAskEntity quotationAskEntity;

    if (quotationAskEntityOptional.isEmpty()) {
      return null;
    } else {
      quotationAskEntity = quotationAskEntityOptional.get();
      QuotationAskEntity clone = quotationAskHistoryEntityMapper.clone(quotationAskEntity);
      quotationAskEntity = mapper.toJpaEntity(quotationAsk, quotationAskEntity);
      if (!ignoreValueType) {
        updateLineItems(quotationAskEntity, quotationAsk);
        storeHistory(clone, quotationAskEntity);
      }

      var savedEntity = jpaRepository.save(quotationAskEntity);
      return mapper.toDomain(savedEntity);
    }
  }

  @Override
  public void quotationAskUpdateStatus(String code) {
    jpaRepository.updateQuotationStatus(code, QuotationStatus.PRICING);
  }

  private void addSparePart(
      QuotationAsk quotationAsk, QuotationAskEntity jpaEntity, Long tenantId, String tenantType) {
    List<AskedSparePartEntity> partEntities = new ArrayList<>();
    quotationAsk
        .getSpareParts()
        .forEach(
            x -> {
              Long id = dbUtils.getNextSequence("asked_spare_part_id");
              x.setPartNameInput(x.getPartNameInput().trim());
              x.setPartNameUnit(x.getPartNameUnit().trim());
              AskedSparePartEntity askedSparePartEntity = mapper.toJpaEntity(x);
              askedSparePartEntity.setId(id);
              askedSparePartEntity.setTenantId(tenantId);
              String sparePartCode = String.format("%s_%d_%d", tenantType, tenantId, id);
              askedSparePartEntity.setCode(sparePartCode);
              askedSparePartEntity.setQuotationAsk(jpaEntity);
              partEntities.add(askedSparePartEntity);
            });
    jpaEntity.setSpareParts(partEntities);
  }

  private void updateLineItems(QuotationAskEntity quotationAskEntity, QuotationAsk quotationAsk) {
    var vehicleEntity = mapper.toJpaEntity(quotationAsk.getAskedVehicle());
    var sparePartEntities =
        quotationAsk.getSpareParts().stream()
            .map(
                x -> {
                  var sparePartEntity = mapper.toJpaEntity(x);
                  if (sparePartEntity.getId() == null) {
                    sparePartEntity.setId(dbUtils.getNextSequence("asked_spare_part_id"));
                  }
                  return sparePartEntity;
                })
            .toList();
    var attachmentEntities =
        quotationAsk.getAttachments().stream().map(mapper::toJpaEntity).toList();

    quotationAskEntity.updateVehicle(vehicleEntity);
    quotationAskEntity.updateSparePart(sparePartEntities);
    quotationAskEntity.updateAttachment(attachmentEntities);
  }

  private void storeHistory(QuotationAskEntity clone, QuotationAskEntity newData) {
    List<ObjectDiffer.FieldChange> changes = new ArrayList<>();
    AskedVehicleEntity realVehicle =
        (AskedVehicleEntity) Hibernate.unproxy(newData.getAskedVehicle());
    changes.addAll(
        ObjectDiffer.compareObjects(
            clone.getAskedVehicle(),
            realVehicle,
            Consts.ASKED_VEHICLE,
            List.of("carBrand", "carModel", "yearOfManufacture", "carType", "trimsLevel", "vin")));
    changes.addAll(
        ObjectDiffer.compareWithIdMatch(
            clone.getAttachments(),
            newData.getAttachments(),
            AskedAttachmentEntity::getId,
            List.of("id"),
            Consts.ATTACHMENT));
    changes.addAll(
        ObjectDiffer.compareWithIdMatch(
            clone.getSpareParts(),
            newData.getSpareParts(),
            AskedSparePartEntity::getCode,
            List.of("partNameInput", "partNameUnit"),
            Consts.ASKED_SPARE_PART));

    if (!changes.isEmpty()) {
      QuotationAskHistoryEntity history =
          new QuotationAskHistoryEntity(
              newData.getCode(),
              newData.getTenantId(),
              OwnerType.CSKH,
              UpdatedType.ASKING,
              changes.stream().map(quotationAskHistoryEntityMapper::toJpaEntity).toList());
      Map<String, String> mapPartName = new HashMap<>();
      mapPartName.putAll(clone.getSparePartsMapName());
      mapPartName.putAll(newData.getSparePartsMapName());
      history.enrichNote(mapPartName);
      jpaHistoryRepository.save(history);
    }
  }
}
