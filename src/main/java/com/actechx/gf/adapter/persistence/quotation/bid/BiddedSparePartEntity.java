package com.actechx.gf.adapter.persistence.quotation.bid;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "bidded_spare_parts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = "quotationBid")
@EqualsAndHashCode(exclude = "quotationBid", callSuper = false)
public class BiddedSparePartEntity extends AuditableEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "code", unique = true)
  private String code;

  @Column(name = "ref_code", nullable = false)
  private String refCode;

  @Column(name = "tenant_id")
  private Long tenantId;

  @Column(name = "part_name_input", nullable = false)
  private String partNameInput;

  @Column(name = "part_name_unit", length = 50)
  private String partNameUnit;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  private boolean deleted;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "quotation_bid_id", nullable = false)
  @JsonIgnore
  private QuotationBidEntity quotationBid;

  protected void updateGeneral(
      String code, String refCode, Long tenantId, String partNameInput, String partNameUnit) {
    this.code = code;
    this.refCode = refCode;
    this.tenantId = tenantId;
    this.partNameInput = partNameInput;
    this.partNameUnit = partNameUnit;
  }
}
