package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.controller.form.request.SearchQuotationAskHistoryRequestDto;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import com.actechx.gf.adapter.repository.JpaQuotationAskHistoryRepository;
import com.actechx.gf.domain.repository.QuotationAskHistoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class QuotationAskHistoryRepositoryImpl implements QuotationAskHistoryRepository {
  private final JpaQuotationAskHistoryRepository jpaRepository;

  @Override
  public Page<QuotationAskHistoryEntity> getHistories(
      String code, SearchQuotationAskHistoryRequestDto request) {
    Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize());
    if (request.getTenantId() != null) {
      return jpaRepository.findByQuotationAskCodeAndUpdatedByTenantIdOrderByCreatedAtDesc(
          code, request.getTenantId(), pageable);
    }
    return jpaRepository.findByQuotationAskCodeOrderByCreatedAtDesc(code, pageable);
  }
}
