package com.actechx.gf.adapter.persistence.purchase.order;

import com.actechx.common.spring.jpa.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "po_transition_history")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class POTransitionHistoryEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "po_id", nullable = false)
  private Long poId;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "previous_stages", columnDefinition = "jsonb")
  private String previousStages;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "transition_data", columnDefinition = "jsonb")
  private String transitionData;

  public POTransitionHistoryEntity(Long poId, String transitionData, String previousStages) {
    this.poId = poId;
    this.transitionData = transitionData;
    this.previousStages = previousStages;
  }
}
