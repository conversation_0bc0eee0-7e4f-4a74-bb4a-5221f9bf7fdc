package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.persistence.quotation.ask.AskedAttachmentEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedVehicleEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryItemEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingProposalEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import com.actechx.gf.app.utils.ObjectDiffer;
import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuotationAskHistoryEntityMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "note", ignore = true)
  @Mapping(target = "quotationAskHistory", ignore = true)
  QuotationAskHistoryItemEntity toJpaEntity(ObjectDiffer.FieldChange fieldChange);

  // DeepClone QuotationAsk
  @Mapping(
      target = "askedVehicle",
      expression = "java(cloneAskedVehicle(source.getAskedVehicle()))")
  @Mapping(
      target = "attachments",
      expression = "java(cloneAskdedAttachment(source.getAttachments()))")
  @Mapping(target = "spareParts", expression = "java(cloneAskedSpareParts(source.getSpareParts()))")
  QuotationAskEntity clone(QuotationAskEntity source);

  default AskedVehicleEntity cloneAskedVehicle(AskedVehicleEntity object) {
    return this.clone(object);
  }

  default List<AskedAttachmentEntity> cloneAskdedAttachment(List<AskedAttachmentEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  default List<AskedSparePartEntity> cloneAskedSpareParts(List<AskedSparePartEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  @Mapping(target = "quotationAsk", ignore = true)
  AskedVehicleEntity clone(AskedVehicleEntity source);

  @Mapping(target = "quotationAsk", ignore = true)
  AskedAttachmentEntity clone(AskedAttachmentEntity source);

  @Mapping(target = "quotationAsk", ignore = true)
  AskedSparePartEntity clone(AskedSparePartEntity source);

  // DeepClone QuotationBid
  @Mapping(
      target = "sparePartPriceLineItems",
      expression = "java(cloneSpareParts(source.getSparePartPriceLineItems()))")
  @Mapping(
      target = "biddedSpareParts",
      expression = "java(cloneBiddedSpareParts(source.getBiddedSpareParts()))")
  @Mapping(
      target = "addedSparePartPriceLineItems",
      expression = "java(cloneAddedSpareParts(source.getAddedSparePartPriceLineItems()))")
  QuotationBidEntity clone(QuotationBidEntity source);

  default List<SparePartPriceLineItemEntity> cloneSpareParts(
      List<SparePartPriceLineItemEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  default List<BiddedSparePartEntity> cloneBiddedSpareParts(List<BiddedSparePartEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  default List<AddedSparePartPriceLineItemEntity> cloneAddedSpareParts(
      List<AddedSparePartPriceLineItemEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  @Mapping(target = "quotationBid", ignore = true)
  SparePartPriceLineItemEntity clone(SparePartPriceLineItemEntity source);

  @Mapping(target = "quotationBid", ignore = true)
  BiddedSparePartEntity clone(BiddedSparePartEntity source);

  @Mapping(target = "quotationBid", ignore = true)
  AddedSparePartPriceLineItemEntity clone(AddedSparePartPriceLineItemEntity source);

  // DeepClone QuotationAskPricingProposal
  @Mapping(
      target = "sparePartPriceLineItems",
      expression = "java(cloneSparePartPriceLineItems(source.getSparePartPriceLineItems()))")
  QuotationAskPricingProposalEntity clone(QuotationAskPricingProposalEntity source);

  default List<AskedSparePartPricingProposalEntity> cloneSparePartPriceLineItems(
      List<AskedSparePartPricingProposalEntity> list) {
    if (list == null) return Collections.emptyList();
    return list.stream().map(this::clone).toList();
  }

  @Mapping(target = "quotationAskPricingProposal", ignore = true)
  AskedSparePartPricingProposalEntity clone(AskedSparePartPricingProposalEntity source);
}
