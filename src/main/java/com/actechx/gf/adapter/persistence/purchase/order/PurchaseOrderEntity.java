package com.actechx.gf.adapter.persistence.purchase.order;

import com.actechx.common.exception.InvalidDataException;
import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.*;
import jakarta.persistence.*;
import java.time.Instant;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "purchase_orders")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseOrderEntity extends AuditableEntity {

  @Id
  @Column(name = "id")
  private Long id;

  @Column(name = "code", unique = true, nullable = false)
  private String code;

  @Enumerated(EnumType.STRING)
  @Column(name = "source", nullable = false)
  private PurchaseSource source;

  @Column(name = "transport_order_id")
  private Long transportOrderId;

  @Column(name = "purchaser_id", nullable = false)
  private Long purchaserId;

  @Column(name = "supplier_id", nullable = false)
  private Long supplierId;

  @Column(name = "supplier_name")
  private String supplierName;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_method")
  private PaymentMethod paymentMethod;

  @Column(name = "is_best_price")
  private Boolean isBestPrice;

  @Column(name = "transport_route_id")
  private Long transportRouteId;

  @Column(name = "quotation_ask_code", nullable = false, columnDefinition = "text")
  private String quotationAskCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "stage", length = 20, nullable = false)
  private POStage stage;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", length = 50, nullable = false)
  private POStatusEnum status;

  @Column(name = "note")
  private String note;

  @Column(name = "pr_id", nullable = false)
  private Long prId;

  @Column(name = "sale_order_code")
  private String saleOrderCode;

  @Version private Long version;

  public void updateInfo(
      Long transportOrderId,
      Long transportRouteId,
      PaymentMethod paymentMethod,
      Boolean isBestPrice,
      POStage stage,
      POStatusEnum status,
      String note,
      String saleOrderCode) {
    this.transportOrderId = transportOrderId;
    this.transportRouteId = transportRouteId;
    this.paymentMethod = paymentMethod;
    this.isBestPrice = isBestPrice;
    this.stage = stage;
    this.status = status;
    this.note = note;
    this.saleOrderCode = saleOrderCode;
    this.setUpdatedAt(Instant.now());
  }

  public void confirm() {
    if (this.stage.equals(POStage.WAIT_TO_CONFIRM)
        && (this.status.equals(POStatusEnum.WAIT_GARAGE_TO_CONFIRM)
            || this.status.equals(POStatusEnum.CONFIRMED))) {
      this.stage = POStage.OPEN;
      this.status = POStatusEnum.CONFIRMED;
      this.setUpdatedAt(Instant.now());
    }
  }

  public void updateStageByPrStatus(PurchaseRequestStatus targetStatus) {
    switch (targetStatus) {
      case PurchaseRequestStatus.ORDER_CREATED -> {
        this.stage = POStage.OPEN;
        this.setUpdatedAt(Instant.now());
      }
      case PurchaseRequestStatus.CANCELLED -> {
        this.stage = POStage.CANCELLED;
        this.setUpdatedAt(Instant.now());
      }
      default -> {
        // Empty
      }
    }
  }

  public void confirmReceived() {
    if (this.stage.equals(POStage.DELIVERING)) {
      this.stage = POStage.CLOSED;
      this.setUpdatedAt(Instant.now());
    } else {
      throw new InvalidDataException("stage", "is not delivering");
    }
  }

  public void cancel() {
    this.stage = POStage.CANCELLED;
    this.status = POStatusEnum.CANCELLED;
    this.setUpdatedAt(Instant.now());
  }
}
