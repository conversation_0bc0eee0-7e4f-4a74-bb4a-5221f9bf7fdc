package com.actechx.gf.adapter.persistence;

import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingResponseDto;
import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingRequestEntity;
import com.actechx.gf.adapter.repository.JpaQuotationAskPricingRequestRepository;
import com.actechx.gf.app.mapper.QuotationAskPricingMapper;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingRequest;
import com.actechx.gf.domain.repository.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
@RequiredArgsConstructor
public class QuotationAskPricingRequestRepositoryImpl
    implements QuotationAskPricingRequestRepository {
  private final QuotationAskPricingMapper pricingMapper;
  private final JpaQuotationAskPricingRequestRepository repository;
  private final SparePartPriceLineItemRepository sparePartPriceLineItemRepository;
  private final AddedSparePartPriceLineItemRepository addedSparePartPriceLineItemRepository;
  private final QuotationAskRepository quotationAskRepository;

  @Override
  public QuotationAskPricingResponseDto saveData(
      QuotationAskPricingRequest quotationAskPricingRequest) {
    log.info(
        "QuotationAskPricingRequestEntity number: {}",
        quotationAskPricingRequest.getAskedSpareParts().size());
    QuotationAskPricingRequestEntity quotationAskPricingRequestEntity =
        pricingMapper.toEntity(quotationAskPricingRequest);
    quotationAskPricingRequestEntity.addItems(
        quotationAskPricingRequestEntity.getAskedSpareParts());
    QuotationAskPricingRequestEntity entitySave = repository.save(quotationAskPricingRequestEntity);
    this.updateDataQuotationBid(quotationAskPricingRequest.getAskedSpareParts());
    QuotationAskPricingResponseDto responseDto = pricingMapper.toDto(entitySave);
    responseDto.setCreatedAt(
        DateTimeUtils.convertInstantToLocalDateTime(entitySave.getCreatedAt()));
    quotationAskRepository.quotationAskUpdateStatus(
        quotationAskPricingRequest.getQuotationAskCode());
    return responseDto;
  }

  private void updateDataQuotationBid(
      List<QuotationAskPricingRequest.AskedSparePartPricing> askedSpareParts) {
    List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities = new ArrayList<>();
    List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntities = new ArrayList<>();
    askedSpareParts.forEach(
        x -> {
          Long sparePartLineItemId = x.getSparePartLineItemId();
          Long addSparePartLineItemId = x.getAddSparePartLineItemId();
          if (Objects.nonNull(sparePartLineItemId)) {
            SparePartPriceLineItemEntity sparePartPriceLineItemEntity =
                sparePartPriceLineItemRepository
                    .getByIdAndDetailStatusFalse(sparePartLineItemId)
                    .orElseThrow(
                        () ->
                            new ResourceNotFoundException(
                                "SparePartPriceLineItem get by id", sparePartLineItemId));
            sparePartPriceLineItemEntity.setQuantity(x.getQuantity());
            sparePartPriceLineItemEntity.setDetailStatus(true);
            sparePartPriceLineItemEntities.add(sparePartPriceLineItemEntity);
          } else if (Objects.nonNull(addSparePartLineItemId)) {
            AddedSparePartPriceLineItemEntity addedSparePartPriceLineItemEntity =
                addedSparePartPriceLineItemRepository
                    .getByIdAndDetailStatusFalse(addSparePartLineItemId)
                    .orElseThrow(
                        () ->
                            new ResourceNotFoundException(
                                "BiddedSparePart get by id", addSparePartLineItemId));
            addedSparePartPriceLineItemEntity.setQuantity(x.getQuantity());
            addedSparePartPriceLineItemEntity.setDetailStatus(true);
            addedSparePartPriceLineItemEntities.add(addedSparePartPriceLineItemEntity);
          }
        });
    sparePartPriceLineItemRepository.saveAllRepo(sparePartPriceLineItemEntities);
    addedSparePartPriceLineItemRepository.saveAllRepo(addedSparePartPriceLineItemEntities);
  }
}
