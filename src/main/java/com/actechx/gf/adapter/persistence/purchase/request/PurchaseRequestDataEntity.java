package com.actechx.gf.adapter.persistence.purchase.request;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.PurchaseRequestDataStatus;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.*;

@Entity
@Table(name = "purchase_request_data")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseRequestDataEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private String requestedProductName;

  @Column(nullable = false)
  private String quotationAskCode;

  @Column(nullable = false)
  private Long productId;

  @Column(nullable = false)
  private Long purchaseRequestId;

  // tenantId gara
  @Column(nullable = false)
  private Long purchaserId;

  // tenantId vendor
  @Column(nullable = false)
  private Long supplierId;

  @Column(nullable = false)
  private Integer requestedQuantity;

  private Integer actualSalesQuantity;

  private BigDecimal detailedPrice;

  private BigDecimal materialPrice;

  private BigDecimal servicingPrice;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private PurchaseRequestDataStatus salesStatus;

  public void updateInfo(Integer actualSalesQuantity) {
    this.actualSalesQuantity = actualSalesQuantity;
    if (requestedQuantity == null
        || actualSalesQuantity == null
        || requestedQuantity <= 0
        || actualSalesQuantity <= 0) {
      this.salesStatus = PurchaseRequestDataStatus.SOLD_OUT;
    }
    if (Objects.equals(requestedQuantity, actualSalesQuantity)) {
      this.salesStatus = PurchaseRequestDataStatus.CONFIRMED;
    }
    if (requestedQuantity > actualSalesQuantity) {
      this.salesStatus = PurchaseRequestDataStatus.STOCK_CHANGED;
    }
  }
}
