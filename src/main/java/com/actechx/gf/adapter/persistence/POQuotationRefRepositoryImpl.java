package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.purchase.order.POQuotationRefEntity;
import com.actechx.gf.adapter.repository.JpaPOQuotationRefRepository;
import com.actechx.gf.domain.repository.POQuotationRefRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class POQuotationRefRepositoryImpl implements POQuotationRefRepository {
  private final JpaPOQuotationRefRepository jpaPOQuotationRefRepository;

  @Override
  public List<POQuotationRefEntity> findByPOId(Long poId) {
    return jpaPOQuotationRefRepository.findByPoId(poId);
  }

  @Override
  public void store(POQuotationRefEntity entity) {
    jpaPOQuotationRefRepository.save(entity);
  }
}
