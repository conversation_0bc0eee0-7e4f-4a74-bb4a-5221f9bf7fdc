package com.actechx.gf.adapter.persistence.purchase.order;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.POSupplyStatus;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "po_supplier")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class POSupplierEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "po_id", nullable = false)
  private Long poId;

  @Column(name = "supplier_id", nullable = false)
  private Long supplierId;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", length = 50, nullable = false)
  private POSupplyStatus status;

  public void updateStatus(POSupplyStatus status) {
    this.status = status;
  }
}
