package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequest;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequestData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PurchaseRequestEntityMapper {

  @Mapping(target = "note", ignore = true)
  PurchaseRequestEntity toJpaEntity(PurchaseRequest domain);

  @Mapping(target = "domainEvents", ignore = true)
  @Mapping(target = "purchaseRequestDataList", ignore = true)
  PurchaseRequest toDomain(PurchaseRequestEntity jpaEntity);

  PurchaseRequestDataEntity toJpaEntity(PurchaseRequestData domain);

  PurchaseRequestData toDomain(PurchaseRequestDataEntity jpaEntity);
}
