package com.actechx.gf.adapter.persistence.quotation.pricing;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.Segment;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "asked_spare_parts_pricing_request")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AskedSparePartPricingRequestEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false, length = 50)
  private String code;

  @Column(nullable = false)
  private String partNameInput;

  @Column(nullable = false, length = 50)
  private String partNameUnit;

  @Column(nullable = false)
  private Integer quantity;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private Segment segment;

  private String refCode;

  @Column(nullable = false)
  private Long tenantId;

  // Đã có báo giá chi tiết từ vendor hay chưa
  @Column(nullable = false)
  private boolean proposalStatus;

  @ManyToOne
  @JoinColumn(name = "quotation_ask_pricing_id", nullable = false)
  private QuotationAskPricingRequestEntity quotationAskPricing;
}
