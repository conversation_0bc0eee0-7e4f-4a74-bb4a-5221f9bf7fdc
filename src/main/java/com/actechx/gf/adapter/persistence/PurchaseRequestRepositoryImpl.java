package com.actechx.gf.adapter.persistence;

import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.persistence.mapper.PurchaseRequestEntityMapper;
import com.actechx.gf.adapter.persistence.purchase.request.PRTransitionHistoryEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.adapter.repository.JpaPRTransitionHistoryRepository;
import com.actechx.gf.adapter.repository.JpaPurchaseRequestDataEntityRepository;
import com.actechx.gf.adapter.repository.JpaPurchaseRequestEntityRepository;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequest;
import com.actechx.gf.domain.repository.purchase.PurchaseRequestRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Repository
@RequiredArgsConstructor
public class PurchaseRequestRepositoryImpl implements PurchaseRequestRepository {

  private final JpaPurchaseRequestEntityRepository jpaPurchaseRequestEntityRepository;
  private final JpaPurchaseRequestDataEntityRepository purchaseRequestDataEntityRepository;
  private final JpaPRTransitionHistoryRepository prTransitionHistoryRepository;
  private final PurchaseRequestEntityMapper mapper;
  private final EntityManagerFactory entityManagerFactory;

  @Override
  @Transactional
  public PurchaseRequest save(PurchaseRequest purchaseRequestAgg) {
    PurchaseRequestEntity entity = mapper.toJpaEntity(purchaseRequestAgg);
    savePRTransitionHistory(entity);
    PurchaseRequestEntity savedEntity = jpaPurchaseRequestEntityRepository.save(entity);

    if (!CollectionUtils.isEmpty(purchaseRequestAgg.getPurchaseRequestDataList())) {
      purchaseRequestDataEntityRepository.deleteByPurchaseRequestId(savedEntity.getId());

      var dataEntities =
          purchaseRequestAgg.getPurchaseRequestDataList().stream()
              .map(
                  data -> {
                    PurchaseRequestDataEntity dataEntity = mapper.toJpaEntity(data);
                    dataEntity.setPurchaseRequestId(savedEntity.getId());
                    return dataEntity;
                  })
              .toList();

      purchaseRequestDataEntityRepository.saveAll(dataEntities);
    }

    return findById(savedEntity.getId()).orElseThrow();
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<PurchaseRequest> findById(Long id) {
    return jpaPurchaseRequestEntityRepository
        .findById(id)
        .map(
            entity -> {
              PurchaseRequest agg = mapper.toDomain(entity);
              var dataEntities = purchaseRequestDataEntityRepository.findByPurchaseRequestId(id);
              var dataList =
                  dataEntities.stream().map(mapper::toDomain).collect(Collectors.toList());
              agg.setPurchaseRequestDataList(dataList);
              return agg;
            });
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<PurchaseRequest> findByCode(String code) {
    return jpaPurchaseRequestEntityRepository.findAll().stream()
        .filter(entity -> code.equals(entity.getCode()))
        .findFirst()
        .map(
            entity -> {
              PurchaseRequest agg = mapper.toDomain(entity);
              var dataEntities =
                  purchaseRequestDataEntityRepository.findByPurchaseRequestId(entity.getId());
              var dataList =
                  dataEntities.stream().map(mapper::toDomain).collect(Collectors.toList());
              agg.setPurchaseRequestDataList(dataList);
              return agg;
            });
  }

  @Override
  public Optional<PurchaseRequestEntity> findEntityByIdAndTenantId(Long id, Long purchaserId) {
    return jpaPurchaseRequestEntityRepository.findByIdAndPurchaserId(id, purchaserId);
  }

  @Override
  public Optional<PurchaseRequestEntity> findEntityByCode(String code) {
    return jpaPurchaseRequestEntityRepository.findByCode(code);
  }

  @Override
  public Page<PurchaseRequestEntity> findAll(
      Specification<PurchaseRequestEntity> spec, PageRequest pageable) {
    return jpaPurchaseRequestEntityRepository.findAll(spec, pageable);
  }

  @Override
  public PurchaseRequestEntity store(PurchaseRequestEntity purchaseRequest) {
    savePRTransitionHistory(purchaseRequest);
    return jpaPurchaseRequestEntityRepository.save(purchaseRequest);
  }

  private void savePRTransitionHistory(PurchaseRequestEntity purchaseRequestEntity) {
    PurchaseRequestEntity oldEntity = null;

    if (purchaseRequestEntity.getId() != null) {
      try (EntityManager em2 = entityManagerFactory.createEntityManager()) {
        oldEntity = em2.find(PurchaseRequestEntity.class, purchaseRequestEntity.getId());
      }
    }

    // Chỉ lưu history nếu có thay đổi status
    boolean isNew = oldEntity == null; // Trường hợp tạo mới
    boolean stageChanged =
        !isNew
            && oldEntity.getStatus() != null
            && purchaseRequestEntity.getStatus() != null
            && !oldEntity.getStatus().equals(purchaseRequestEntity.getStatus());

    if (isNew || stageChanged) {
      var lastTransitionHistory =
          prTransitionHistoryRepository
              .findTopByPrIdOrderByCreatedAtDesc(purchaseRequestEntity.getId())
              .orElse(null);
      List<String> previousStages = new ArrayList<>();
      if (lastTransitionHistory != null) {
        previousStages = JsonUtils.toList(lastTransitionHistory.getPreviousStages(), String.class);
      }
      // Append stage mới
      previousStages.add(purchaseRequestEntity.getStatus().name());

      // Tạo bản ghi history mới
      PRTransitionHistoryEntity history =
          new PRTransitionHistoryEntity(
              purchaseRequestEntity.getId(),
              JsonUtils.toJson(purchaseRequestEntity),
              JsonUtils.toJson(previousStages));

      prTransitionHistoryRepository.save(history);
    }
  }
}
