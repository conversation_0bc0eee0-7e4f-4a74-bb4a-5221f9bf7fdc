package com.actechx.gf.adapter.persistence.quotation.bid;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.Segment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.*;

@Entity
@Table(name = "spare_part_price_line_items")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = "quotationBid")
@EqualsAndHashCode(exclude = "quotationBid", callSuper = false)
public class SparePartPriceLineItemEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "spare_part_input_code", length = 50)
  private String sparePartInputCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "segment", nullable = false, length = 20)
  private Segment segment;

  @Column(name = "price")
  private BigDecimal price;

  @Column(name = "currency", length = 10)
  private String currency;

  @Column(name = "note")
  private String note;

  @Column(name = "unit")
  private String unit;

  @Column(name = "picked_to_po")
  private boolean pickedToPO;

  // đã báo giá chi tiết hay chưa
  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  @Builder.Default
  private Boolean detailStatus = false;

  // vendor đã phản hồi báo giá chi tiết hay chưa
  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  @Builder.Default
  private Boolean receiveDetailStatus = false;

  private Integer quantity;

  private BigDecimal materialPrice;

  private BigDecimal servicingPrice;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  private boolean deleted;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "quotation_bid_id", nullable = false)
  @JsonIgnore
  private QuotationBidEntity quotationBid;

  protected void updateGeneral(BigDecimal price, String currency, String note, boolean pickedToPO) {
    this.price = price;
    this.currency = currency;
    this.note = note;
    this.pickedToPO = pickedToPO;
  }
}
