package com.actechx.gf.adapter.persistence.purchase.cart;

import com.actechx.common.spring.jpa.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "cart")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CartEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private Long productId;

  @Column(nullable = false)
  private Long quotationAskId;

  // tenantId gara
  @Column(nullable = false)
  private Long purchaserId;

  // tenantId vendor
  @Column(nullable = false)
  private Long supplierId;

  @Column(nullable = false)
  private Integer quantity;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  @Builder.Default
  private Boolean isPicked = false;

  private String refPrCode;

  @Column(columnDefinition = "BOOLEAN DEFAULT false", nullable = false)
  @Builder.Default
  private Boolean deleted = false;
}
