package com.actechx.gf.adapter.persistence;

import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.gf.adapter.controller.form.response.BidCount;
import com.actechx.gf.adapter.persistence.mapper.QuotationAskHistoryEntityMapper;
import com.actechx.gf.adapter.persistence.mapper.QuotationBidEntityMapper;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import com.actechx.gf.adapter.repository.JpaQuotationAskHistoryRepository;
import com.actechx.gf.adapter.repository.JpaQuotationAskRepository;
import com.actechx.gf.adapter.repository.JpaQuotationBidRepository;
import com.actechx.gf.app.utils.Consts;
import com.actechx.gf.app.utils.ObjectDiffer;
import com.actechx.gf.domain.model.enums.OwnerType;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.UpdatedType;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import com.actechx.gf.domain.repository.QuotationBidRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class QuotationBidRepositoryImpl implements QuotationBidRepository {
  private final JpaQuotationAskRepository jpaAskRepository;
  private final JpaQuotationBidRepository jpaRepository;
  private final JpaQuotationAskHistoryRepository jpaAskHistoryRepository;
  private final QuotationBidEntityMapper mapper;
  private final QuotationAskHistoryEntityMapper quotationAskHistoryEntityMapper;

  @Override
  public QuotationBid store(QuotationBid quotationBid) {
    var quotationBidEntityOptional =
        jpaRepository.findByQuotationAskCodeAndTenantId(
            quotationBid.getQuotationAskCode(), quotationBid.getTenantId());
    QuotationBidEntity quotationBidEntity;
    QuotationBidEntity clone = null;
    if (quotationBidEntityOptional.isEmpty()) {
      quotationBidEntity = mapper.toJpaEntity(quotationBid);
    } else {
      quotationBidEntity = quotationBidEntityOptional.get();
      clone = quotationAskHistoryEntityMapper.clone(quotationBidEntity);
      quotationBidEntity.updateGeneralInfo(
          quotationBid.getBidType(),
          quotationBid.getStatus(),
          quotationBid.getNote(),
          quotationBid.getUpdatedBy());
    }
    updateLineItems(quotationBidEntity, quotationBid);
    var savedEntity = jpaRepository.save(quotationBidEntity);
    storeHistory(clone, quotationBidEntity);
    return mapper.toDomain(savedEntity);
  }

  @Override
  public List<QuotationBidEntity> findByQuotationAskCode(String quotationAskCode) {
    return jpaRepository.findByQuotationAskCode(quotationAskCode);
  }

  @Override
  public Optional<QuotationBid> findByQuotationAskCodeAndTenantId(
      String quotationAskCode, Long tenantId) {
    return jpaRepository
        .findByQuotationAskCodeAndTenantId(quotationAskCode, tenantId)
        .map(mapper::toDomain);
  }

  @Override
  public Map<String, Integer> getBidCountByQuotationAskCode(List<String> quotationAskCodes) {
    return jpaRepository.getBidCountByQuotationAskCode(quotationAskCodes).stream()
        .collect(Collectors.toMap(BidCount::getQuotationAskCode, BidCount::getCount, (a, b) -> b));
  }

  @Override
  public Map<String, Integer> getBidCountByQuotationAskCodeAndStatus(
      List<String> quotationAskCodes, QuotationBidStatus status) {
    return jpaRepository.getBidCountByQuotationAskCodeAndStatus(quotationAskCodes, status).stream()
        .collect(Collectors.toMap(BidCount::getQuotationAskCode, BidCount::getCount, (a, b) -> b));
  }

  @Override
  public int updateQuotationBidStatusRepo(
      Long tenantId, String quotationAskCode, QuotationBidStatus status) {
    return jpaRepository.updateQuotationBidStatus(tenantId, quotationAskCode, status);
  }

  @Override
  public void updateStatus(QuotationBid quotationBid) {
    var quotationBidEntity =
        jpaRepository
            .findByQuotationAskCodeAndTenantId(
                quotationBid.getQuotationAskCode(), quotationBid.getTenantId())
            .orElseThrow(() -> new ResourceNotFoundException("QuotationBid", quotationBid.getId()));
    quotationBidEntity.setStatus(QuotationBidStatus.PRICED);
    jpaRepository.save(quotationBidEntity);
  }

  private void updateLineItems(QuotationBidEntity quotationBidEntity, QuotationBid quotationBid) {
    var sparePartPriceLineItems =
        quotationBid.getSparePartPriceLineItems().stream().map(mapper::toJpaEntity).toList();
    var addedsparePartPriceLineItems =
        quotationBid.getAddedSparePartPriceLineItems().stream().map(mapper::toJpaEntity).toList();
    var biddedSpareParts =
        quotationBid.getBiddedSpareParts().stream().map(mapper::toJpaEntity).toList();

    quotationBidEntity.updateSparePartPriceLineItems(sparePartPriceLineItems);
    quotationBidEntity.updateAddedSparePartPriceLineItems(addedsparePartPriceLineItems);
    quotationBidEntity.updateBiddedSpareParts(biddedSpareParts);
  }

  private void storeHistory(QuotationBidEntity clone, QuotationBidEntity newData) {
    if (clone != null) {
      List<ObjectDiffer.FieldChange> changes = new ArrayList<>();
      changes.addAll(
          ObjectDiffer.compareWithIdMatch(
              clone.getBiddedSpareParts(),
              newData.getBiddedSpareParts(),
              BiddedSparePartEntity::getCode,
              List.of("partNameInput", "partNameUnit"),
              Consts.BIDDED_SPARE_PART));
      changes.addAll(
          ObjectDiffer.compareWithIdMatch(
              clone.getAddedSparePartPriceLineItems(),
              newData.getAddedSparePartPriceLineItems(),
              e -> e.getSparePartInputCode() + "-" + e.getSegment(),
              List.of("price"),
              Consts.ADDED_SPARE_PART_PRICE_LINE_ITEM));
      changes.addAll(
          ObjectDiffer.compareWithIdMatch(
              clone.getSparePartPriceLineItems(),
              newData.getSparePartPriceLineItems(),
              e -> e.getSparePartInputCode() + "-" + e.getSegment(),
              List.of("price"),
              Consts.SPARE_PART_PRICE_LINE_ITEM));

      if (!changes.isEmpty()) {
        QuotationAskHistoryEntity history =
            new QuotationAskHistoryEntity(
                newData.getQuotationAskCode(),
                newData.getTenantId(),
                OwnerType.VENDOR,
                UpdatedType.BIDDING,
                changes.stream().map(quotationAskHistoryEntityMapper::toJpaEntity).toList());
        QuotationAskEntity quotationAskEntity =
            jpaAskRepository.findByCode(newData.getQuotationAskCode()).orElseThrow();
        Map<String, String> mapPartName = new HashMap<>();
        mapPartName.putAll(quotationAskEntity.getSparePartsMapName());
        mapPartName.putAll(clone.getSparePartsMapName());
        mapPartName.putAll(newData.getSparePartsMapName());
        history.enrichNote(mapPartName);
        jpaAskHistoryRepository.save(history);
      }
    }
  }
}
