package com.actechx.gf.adapter.persistence.mapper;

import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.quotationbid.AddedSparePartPriceLineItem;
import com.actechx.gf.domain.model.quotationbid.BiddedSparePart;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import com.actechx.gf.domain.model.quotationbid.SparePartPriceLineItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuotationBidEntityMapper {

  @Mapping(target = "version", ignore = true)
  @Mapping(target = "sparePartPriceLineItems", ignore = true)
  @Mapping(target = "addedSparePartPriceLineItems", ignore = true)
  @Mapping(target = "biddedSpareParts", ignore = true)
  @Mapping(target = "sparePartsMapName", ignore = true)
  QuotationBidEntity toJpaEntity(QuotationBid domain);

  @Mapping(target = "domainEvents", ignore = true)
  QuotationBid toDomain(QuotationBidEntity jpaEntity);

  @Mapping(target = "id", source = "id")
  AddedSparePartPriceLineItem toDomain(AddedSparePartPriceLineItemEntity jpaEntity);

  @Mapping(target = "quotationBid", ignore = true)
  @Mapping(target = "detailStatus", ignore = true)
  @Mapping(target = "quantity", ignore = true)
  @Mapping(target = "receiveDetailStatus", ignore = true)
  @Mapping(target = "materialPrice", ignore = true)
  @Mapping(target = "servicingPrice", ignore = true)
  AddedSparePartPriceLineItemEntity toJpaEntity(AddedSparePartPriceLineItem domain);

  @Mapping(target = "id", source = "id")
  BiddedSparePart toDomain(BiddedSparePartEntity jpaEntity);

  @Mapping(target = "quotationBid", ignore = true)
  BiddedSparePartEntity toJpaEntity(BiddedSparePart domain);

  @Mapping(target = "id", source = "id")
  SparePartPriceLineItem toDomain(SparePartPriceLineItemEntity jpaEntity);

  @Mapping(target = "quotationBid", ignore = true)
  @Mapping(target = "detailStatus", ignore = true)
  @Mapping(target = "quantity", ignore = true)
  @Mapping(target = "receiveDetailStatus", ignore = true)
  @Mapping(target = "materialPrice", ignore = true)
  @Mapping(target = "servicingPrice", ignore = true)
  SparePartPriceLineItemEntity toJpaEntity(SparePartPriceLineItem domain);
}
