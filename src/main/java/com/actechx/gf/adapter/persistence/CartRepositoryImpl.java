package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.purchase.cart.CartEntity;
import com.actechx.gf.adapter.repository.JpaCartRepository;
import com.actechx.gf.app.mapper.purchase.CartMapper;
import com.actechx.gf.domain.model.purchaserequest.Cart;
import com.actechx.gf.domain.model.purchaserequest.CartAdd;
import com.actechx.gf.domain.repository.purchase.CartRepository;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class CartRepositoryImpl implements CartRepository {
  private final CartMapper cartMapper;
  private final JpaCartRepository repository;

  @Override
  public List<Cart> getAllByPurchaserId(Long purchaserId) {
    List<CartEntity> cartEntities = repository.findByPurchaserIdAndDeletedFalse(purchaserId);
    return cartMapper.toCarts(cartEntities);
  }

  @Override
  public List<Cart> getAllByIds(Set<Long> ids, Long purchaserId) {
    List<CartEntity> cartEntities =
        repository.findByIdInAndPurchaserIdAndDeletedFalse(ids, purchaserId);
    return cartMapper.toCarts(cartEntities);
  }

  @Override
  public int addCartRepo(List<CartAdd> cartAdds) {
    AtomicInteger count = new AtomicInteger();
    cartAdds.forEach(
        cartAdd -> {
          Optional<CartEntity> cartEntityOptional =
              repository.findByProductIdAndQuotationAskIdAndPurchaserIdAndSupplierIdAndDeletedFalse(
                  cartAdd.getProductId(),
                  cartAdd.getQuotationAskId(),
                  cartAdd.getPurchaserId(),
                  cartAdd.getSupplierId());
          if (cartEntityOptional.isEmpty()) {
            CartEntity cartEntity = cartMapper.toEntity(cartAdd);
            repository.save(cartEntity);
            count.getAndIncrement();
          }
        });
    return count.get();
  }

  @Override
  public Optional<CartEntity> getByIdAndPurchaserId(Long id, Long purchaserId) {
    return repository.findByIdAndPurchaserIdAndDeletedFalse(id, purchaserId);
  }

  @Override
  public void update(CartEntity cartEntity) {
    repository.save(cartEntity);
  }

  @Override
  public void delete(CartEntity cartEntity) {
    repository.save(cartEntity);
  }

  @Override
  public void addRefPrCodeAndDelete(List<Long> ids, String prCode) {
    List<CartEntity> cartEntities = repository.findByIdIn(ids);
    cartEntities.forEach(
        x -> {
          x.setRefPrCode(prCode);
          x.setDeleted(true);
        });
    repository.saveAll(cartEntities);
  }
}
