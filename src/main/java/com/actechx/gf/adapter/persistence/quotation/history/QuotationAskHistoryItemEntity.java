package com.actechx.gf.adapter.persistence.quotation.history;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "quotation_ask_history_items")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = "quotationAskHistory")
@EqualsAndHashCode(exclude = "quotationAskHistory", callSuper = false)
public class QuotationAskHistoryItemEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String fieldName;

  @Column(columnDefinition = "TEXT")
  private String oldData;

  @Column(columnDefinition = "TEXT")
  private String newData;

  @Column(columnDefinition = "TEXT")
  private String note;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "quotation_ask_history_id", nullable = false)
  @JsonIgnore
  private QuotationAskHistoryEntity quotationAskHistory;
}
