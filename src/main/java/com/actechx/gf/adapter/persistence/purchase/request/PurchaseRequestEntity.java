package com.actechx.gf.adapter.persistence.purchase.request;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "purchase_request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseRequestEntity extends AuditableEntity {
  @Id private Long id;

  @Column(unique = true, length = 50, nullable = false)
  private String code;

  // tenantId gara
  @Column(nullable = false)
  private Long purchaserId;

  @Enumerated(EnumType.STRING)
  private PurchaseRequestStatus status;

  @Column(columnDefinition = "TEXT")
  private String note;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private List<String> statusTransitionData;
}
