package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.repository.JpaBiddedSparePartRepository;
import com.actechx.gf.domain.repository.BiddedSparePartRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class BiddedSparePartRepositoryImpl implements BiddedSparePartRepository {
  private final JpaBiddedSparePartRepository repository;

  @Override
  public Optional<BiddedSparePartEntity> getByCode(String code) {
    return repository.findByCode(code);
  }

  @Override
  public Optional<BiddedSparePartEntity> getBidedSparePartByName(
      String name, Long tenantId, String quotationAskCode) {
    return repository.findByPartNameInputAndTenantIdAndQuotationBid_QuotationAskCode(
        name, tenantId, quotationAskCode);
  }
}
