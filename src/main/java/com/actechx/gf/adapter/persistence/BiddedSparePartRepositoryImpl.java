package com.actechx.gf.adapter.persistence;

import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.repository.JpaBiddedSparePartRepository;
import com.actechx.gf.domain.repository.BiddedSparePartRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class BiddedSparePartRepositoryImpl implements BiddedSparePartRepository {
  private final JpaBiddedSparePartRepository repository;

  @Override
  public Optional<BiddedSparePartEntity> getByCode(String code) {
    return repository.findByCodeAndDeletedFalse(code);
  }

  @Override
  public Optional<BiddedSparePartEntity> getBidedSparePartByName(
      String name, String unit, Long tenantId, String quotationAskCode) {
    return repository
        .findByPartNameInputAndPartNameUnitAndTenantIdAndQuotationBid_QuotationAskCodeAndDeletedFalse(
            name, unit, tenantId, quotationAskCode);
  }
}
