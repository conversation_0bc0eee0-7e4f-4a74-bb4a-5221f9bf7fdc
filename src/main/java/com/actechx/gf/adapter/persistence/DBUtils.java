package com.actechx.gf.adapter.persistence;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.math.BigInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DBUtils {

  private final EntityManager entityManager;

  @Value("${spring.jpa.properties.hibernate.default_schema}")
  private String defaultSchema;

  private final JdbcTemplate jdbcTemplate;

  public Long getNextSequence(String sequenceName) {
    try {
      String sql = String.format("SELECT \"%s\".get_next_number(?, ?)", defaultSchema);
      return jdbcTemplate.queryForObject(sql, Long.class, defaultSchema, sequenceName);
    } catch (Exception e) {
      throw new RuntimeException("Failed to generate sequence for: " + sequenceName, e);
    }
  }

  /**
   * <PERSON><PERSON>i stored procedure get_next_id để sinh sequence tiếp theo <PERSON> bảo tính concurrency thông qua
   * database lock
   *
   * @param sequenceName tên sequence cần sinh
   * @return giá trị sequence tiếp theo
   */
  public Long getNextSequence2(String sequenceName) {
    try {
      log.debug("Getting next sequence for: {}", sequenceName);

      String sql = String.format("SELECT \"%s\".get_next_number(?)", defaultSchema);

      Query query = entityManager.createNativeQuery(sql);
      query.setParameter(1, sequenceName);

      BigInteger result = (BigInteger) query.getSingleResult();

      log.debug("Generated sequence {} for {}", result.longValue(), sequenceName);
      return result.longValue();

    } catch (Exception e) {
      log.error("Failed to get next sequence for: {}", sequenceName, e);
      throw new RuntimeException("Failed to generate sequence for: " + sequenceName, e);
    }
  }
}
