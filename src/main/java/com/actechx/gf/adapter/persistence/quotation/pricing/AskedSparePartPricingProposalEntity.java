package com.actechx.gf.adapter.persistence.quotation.pricing;

import com.actechx.common.spring.jpa.AuditableEntity;
import com.actechx.gf.domain.model.enums.Segment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Entity
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "asked_spare_parts_pricing_proposal")
public class AskedSparePartPricingProposalEntity extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(length = 50, nullable = false)
  private String sparePartInputCode;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private Segment segment;

  @Column(nullable = false)
  private BigDecimal materialPrice;

  @Column(nullable = false)
  private BigDecimal servicingPrice;

  @Column(nullable = false, length = 10)
  private String currency;

  @Column(nullable = false)
  private Integer quantity;

  @Column(nullable = false, length = 50)
  private String unit;

  private String refCode;

  @Column(columnDefinition = "TEXT")
  private String note;

  // trạng thái bên vendor cập nhật lại giá, hiện tại ko cho phép cập nhật
  @Column(nullable = false)
  private boolean updatedStatus;

  @ManyToOne
  @JoinColumn(name = "quotation_ask_pricing_proposal_id", nullable = false)
  @JsonIgnore
  private QuotationAskPricingProposalEntity quotationAskPricingProposal;
}
