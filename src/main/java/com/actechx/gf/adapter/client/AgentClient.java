package com.actechx.gf.adapter.client;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.client.request.CancelPurchaseOrderDto;
import com.actechx.gf.adapter.client.request.PurchaseRequestAgentRequest;
import com.actechx.gf.adapter.client.request.QuotationAskAgentRequest;
import com.actechx.gf.adapter.client.request.QuotationAskPricingClientRequest;
import com.actechx.gf.adapter.controller.form.request.UpdatePurchaseOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.ShipmentOrderClientRequest;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import org.springframework.web.service.annotation.PutExchange;

@HttpExchange("/protected/v1")
public interface AgentClient {

  @PostExchange("/quotation-asks")
  ApiResponse<String> createQuotationAskAgent(@RequestBody QuotationAskAgentRequest request);

  @PostExchange("/pricing")
  ApiResponse<String> createPricingRequest(
      @RequestBody List<QuotationAskPricingClientRequest> request);

  @PostExchange("/purchases")
  ApiResponse<String> createPurchaseRequest(@RequestBody PurchaseRequestAgentRequest request);

  @PutExchange("/purchases/cancel")
  ApiResponse<String> cancelPurchaseRequest(@RequestBody CancelPurchaseOrderDto request);

  @PostExchange("/purchases/confirm")
  ApiResponse<String> confirmPurchaseRequest(
      @RequestBody List<UpdatePurchaseOrderRequestDto> request);

  @PostExchange("/purchases/confirm-received")
  ApiResponse<String> confirmReceivedPurchaseRequest(
      @RequestBody ShipmentOrderClientRequest request);
}
