package com.actechx.gf.adapter.client;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.client.response.CmsCatalogContentResponse;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/protected/catalog/v1")
public interface CmsClient {

  @PostExchange("/get-parent")
  ApiResponse<CmsCatalogContentResponse> getCatalogContentClient(@RequestParam("code") String code);
}
