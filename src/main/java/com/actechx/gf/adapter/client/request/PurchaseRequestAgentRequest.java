package com.actechx.gf.adapter.client.request;

import com.actechx.gf.domain.model.enums.*;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestAgentRequest {
  private String code; // PurchaseRequestCode
  private Long purchaserId; // tenant garage Id
  private PurchaseRequestStatus status;
  private String purchaseRequestDataList; // List PurchaseRequestData
  private String purchaseOrderList; // List PurchaseOrder
  private String purchaserInformation; // Thông tin garage PurchaserInformation
  private String purchaserCode; // tenant garage code
  private PurchaseSource purchaseSource;
  private String saleSource; // null

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class PurchaseRequestDataDto {
    private Long id;
    private String requestedProductName;
    private String quotationAskCode;
    private Long productId;
    private Long purchaseRequestId;
    private Long purchaserId;
    private Long supplierId;
    private Integer requestedQuantity;
    private Integer actualSalesQuantity;
    private BigDecimal detailedPrice;
    private PurchaseRequestDataStatus salesStatus;
    private BigDecimal unitPrice;
    private String unit;
    private Segment segment;
    private BigDecimal materialPrice;
    private BigDecimal servicingPrice;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class PurchaseOrderDto {
    private String code;
    private PurchaseSource source;
    private Long transportOrderId;
    private Long purchaserId;
    private Long supplierId;
    private String supplierName;
    private PaymentMethod paymentMethod;
    private Boolean isBestPrice;
    private Long transportRouteId;
    private String quotationAskCode;
    private POStage stage;
    private POStatusEnum status;
    private List<POProductDto> poProducts;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class POProductDto {
    private Long poId;
    private String requestedProductName;
    private Long productId;
    private Long quantity;
    private BigDecimal unitPrice;
    private String unit;
    private Segment segment;
    private Long supplierId;
    private String quotationAskCode;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class PurchaserInformation {
    private Long purchaserId;
    private String purchaserName;
    private String purchaserAddress;
    private String purchaserOpsArea;
    private String purchaserRegionArea;
    private String purchaserPhoneNumber;
  }
}
