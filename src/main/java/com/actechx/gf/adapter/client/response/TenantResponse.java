package com.actechx.gf.adapter.client.response;

import com.actechx.common.enums.TenantType;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class TenantResponse {
  private Long id;
  private String tenantName;
  private TenantType tenantType;
  private String code;
  private String logo;
  private String subdomain;
  private String tenantEmail;
  private String tenantPhoneNumber;
  private String legalRepresentative;
  private String chiefAccountant;
  private String currentStage;
  private String currentStatus;
  private String subscriptionPlan;

  private TenantLocationResponse location;
  private List<String> subscribedProducts;
  private List<String> segment;

  public String getOpsArea() {
    return Optional.ofNullable(location).map(TenantLocationResponse::getAreaId).orElse("");
  }

  public String getAddress() {
    return Optional.ofNullable(location).map(e -> e.getWard() + ", " + e.getProvince()).orElse("");
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class TenantLocationResponse {
    private Long id;
    private String province;
    private String ward;
    private String areaId;
  }
}
