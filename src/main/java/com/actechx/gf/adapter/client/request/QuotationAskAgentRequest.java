package com.actechx.gf.adapter.client.request;

import com.actechx.gf.domain.model.enums.CarType;
import com.actechx.gf.domain.model.enums.OwnerType;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class QuotationAskAgentRequest {
  private Long id;

  private Long tenantId;

  private String tenantName;

  private String code;

  private String tenantOpsRegion;
  private String tenantPhoneNumber;
  private String tenantOpsArea;
  private String tenantAddress;
  private String askNote;
  private Boolean isInvoiceRequired;
  private LocalDateTime createdAt;
  private String createdBy;

  private AskedVehicleDto askedVehicle;

  private List<AttachmentDto> attachments;

  private List<SparePartDto> spareParts;

  @Data
  public static class AskedVehicleDto {
    private Long id;
    private String carBrand;
    private String carModel;
    private String yearOfManufacture;
    private CarType carType;
    private String trimsLevel;
    private String vin;
  }

  @Data
  public static class AttachmentDto {
    private Long id;
    private OwnerType owner;
    private String attachmentUrl;
    private String note;
  }

  @Data
  public static class SparePartDto {
    private Long id;
    private String partNameInput;
    private String partNameUnit;
    private String code;
    private String refCode;
    private Long tenantId;
  }
}
