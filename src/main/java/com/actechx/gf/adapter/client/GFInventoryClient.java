package com.actechx.gf.adapter.client;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.client.request.*;
import com.actechx.gf.adapter.client.response.CmsCatalogContentResponse;
import com.actechx.gf.domain.model.purchaserequest.ProductInventoryModel;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.*;

@HttpExchange("/protected/v1/product")
public interface GFInventoryClient {

  @PostExchange("/product-lines")
  ApiResponse<CmsCatalogContentResponse> createProductInventoryClient(
      @RequestBody CreateProductDto request);

  @GetExchange("/productId")
  ApiResponse<Long> getProductIdClient(
      @RequestParam("name") String name,
      @RequestParam("tenantId") Long tenantId,
      @RequestParam("productType") String productTypeEnum,
      @RequestParam("segment") String segment);

  @GetExchange("/{productId}")
  ApiResponse<ProductInventoryModel> getProductInfoClient(@PathVariable Long productId);
}
