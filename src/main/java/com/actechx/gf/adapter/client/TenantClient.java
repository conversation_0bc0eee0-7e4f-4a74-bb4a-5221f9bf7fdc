package com.actechx.gf.adapter.client;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.client.response.TenantResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

@HttpExchange("/api/v1/tenants/{tenantId}")
public interface TenantClient {

  @GetExchange
  ApiResponse<TenantResponse> getTenantInfo(@PathVariable("tenantId") Long tenantId);
}
