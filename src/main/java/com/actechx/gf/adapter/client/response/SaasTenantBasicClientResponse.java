package com.actechx.gf.adapter.client.response;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SaasTenantBasicClientResponse {

  private Long id;
  private String code;
  private String name;
  private String logoUrl;
  private String representativeName;
  private String chiefAccountantName;
  private String companyPhoneNumber;
  private String companyEmailAddress;
  private String companyHoAddress;
  private List<SaasTenantTransporterRegistryResponse> transporterRegistry;
  private List<SaasOperationAreaResponse> operationAreas;

  @Getter
  @Setter
  public static class SaasTenantTransporterRegistryResponse {
    private Long id;
    private Long tenantId;
    private String transporterName;
    private String routeName;
    private String routeContactPhoneNumber;
    private String routeStartedAt;
  }

  @Getter
  @Setter
  public static class SaasOperationAreaResponse {
    private Long id;
    private Long tenantId;
    private String operationRegionCode;
    private String operationAreaCode;
  }
}
