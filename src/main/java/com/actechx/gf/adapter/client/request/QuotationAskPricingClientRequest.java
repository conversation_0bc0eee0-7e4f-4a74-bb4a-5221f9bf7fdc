package com.actechx.gf.adapter.client.request;

import com.actechx.gf.domain.model.enums.Segment;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuotationAskPricingClientRequest {

  private String quotationAskCode;

  private Long originTenantId;

  private Long targetTenantId;

  private LocalDateTime createdAt;

  private String createdBy;

  private List<AskedSparePartPricingClientRequest> askedSpareParts;

  @Data
  @Builder
  public static class AskedSparePartPricingClientRequest {

    private String code;

    private String partNameInput;

    private String partNameUnit;

    private Integer quantity;

    private Segment segment;

    private String refCode;

    private Long tenantId;
  }
}
