package com.actechx.gf.adapter.client;

import com.actechx.common.dto.ApiResponse;
import com.actechx.gf.adapter.client.response.SaasTenantBasicClientResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

@HttpExchange("/protected/v1")
public interface InternalTenantClient {

  @GetExchange("/saas-tenant/{tenantId}")
  ApiResponse<SaasTenantBasicClientResponse> getTenantBasic(
      @PathVariable("tenantId") Long tenantId);
}
