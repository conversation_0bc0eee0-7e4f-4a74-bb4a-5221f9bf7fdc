package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import com.actechx.gf.domain.model.quotationpricing.PricingNumberSuccess;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposal;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposalResponseDomain;
import java.util.Optional;

public interface QuotationAskPricingProposalRepository {
  Optional<QuotationAskPricingProposalEntity> getByQuotationAskCodeAndReplyTenantId(
      String quotationAskCode, Long replyTenantId);

  PricingNumberSuccess createData(QuotationAskPricingProposal proposal);

  PricingNumberSuccess updateData(
      QuotationAskPricingProposalEntity proposalEntity, QuotationAskPricingProposal proposal);

  QuotationAskPricingProposalResponseDomain getQuotationAskPricingProposal(
      Long sparePartPriceLineItemId);
}
