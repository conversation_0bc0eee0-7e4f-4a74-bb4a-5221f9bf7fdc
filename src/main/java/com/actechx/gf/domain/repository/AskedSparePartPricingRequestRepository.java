package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingRequestEntity;
import java.util.List;

public interface AskedSparePartPricingRequestRepository {
  List<AskedSparePartPricingRequestEntity> findByTenantIdAndCodesAndQuotationAskCode(
      Long tenantId, List<String> codes, String quotationAskCode);

  int saveDataAll(List<AskedSparePartPricingRequestEntity> entities);
}
