package com.actechx.gf.domain.repository.purchase;

import com.actechx.gf.adapter.persistence.purchase.cart.CartEntity;
import com.actechx.gf.domain.model.purchaserequest.Cart;
import com.actechx.gf.domain.model.purchaserequest.CartAdd;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface CartRepository {
  List<Cart> getAllByPurchaserId(Long purchaserId);

  List<Cart> getAllByIds(Set<Long> ids, Long purchaserId);

  int addCartRepo(List<CartAdd> cartAdds);

  Optional<CartEntity> getByIdAndPurchaserId(Long id, Long purchaserId);

  void update(CartEntity cartEntity);

  void delete(CartEntity cartEntity);

  void addRefPrCodeAndDelete(List<Long> ids, String prCode);
}
