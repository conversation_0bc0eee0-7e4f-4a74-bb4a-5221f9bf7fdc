package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.controller.form.request.SearchSparePartRequestDto;
import com.actechx.gf.adapter.controller.form.response.SearchSparePartResponseDto;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface AskedSparePartRepository {

  Page<SearchSparePartResponseDto> findDistinctPartNameUnit(SearchSparePartRequestDto request);

  List<AskedSparePartEntity> getByCodeList(List<String> codes);

  Optional<AskedSparePartEntity> getByCode(String code);

  List<Long> findExistingIdsByIdsRepo(List<Long> ids);

  Optional<AskedSparePartEntity> findByIdAndTenantIdAndQuotationAskIdRepo(
      Long id, Long tenantId, Long quotationId);
}
