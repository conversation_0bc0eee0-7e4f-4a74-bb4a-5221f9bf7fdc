package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface QuotationBidRepository {
  QuotationBid store(QuotationBid quotationBid);

  List<QuotationBidEntity> findByQuotationAskCode(String quotationAskCode);

  Optional<QuotationBid> findByQuotationAskCodeAndTenantId(String quotationAskCode, Long tenantId);

  Map<String, Integer> getBidCountByQuotationAskCode(List<String> quotationAskCodes);

  Map<String, Integer> getBidCountByQuotationAskCodeAndStatus(
      List<String> quotationAskCodes, QuotationBidStatus status);

  int updateQuotationBidStatusRepo(
      Long tenantId, String quotationAskCode, QuotationBidStatus status);

  void updateStatus(QuotationBid quotationBid);
}
