package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.controller.form.request.SearchQuotationAskHistoryRequestDto;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import org.springframework.data.domain.Page;

public interface QuotationAskHistoryRepository {
  Page<QuotationAskHistoryEntity> getHistories(
      String code, SearchQuotationAskHistoryRequestDto request);
}
