package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import java.util.Optional;

public interface AddedSparePartPriceLineItemRepository {
  List<AddedSparePartPriceLineItemEntity> getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
      List<Long> ids, List<QuotationBidStatus> statuses);

  Optional<AddedSparePartPriceLineItemEntity> getByIdAndDetailStatusFalse(Long id);

  void saveAllRepo(List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntities);

  Optional<AddedSparePartPriceLineItemEntity> getAddSparePartPriceLineItemProposal(
      String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId);
}
