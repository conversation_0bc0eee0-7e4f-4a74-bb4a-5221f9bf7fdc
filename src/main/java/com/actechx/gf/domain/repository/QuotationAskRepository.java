package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

public interface QuotationAskRepository {
  QuotationAsk save(QuotationAsk quotationAsk, String tenantType);

  Optional<QuotationAsk> findByIdAndTenantId(Long id, Long tenantId);

  Page<QuotationAskEntity> findAll(Specification<QuotationAskEntity> spec, Pageable pageable);

  Optional<QuotationAsk> findByCode(String code);

  Optional<QuotationAsk> findByCodeAndTenantId(String code, Long tenantId);

  List<QuotationAsk> findUnprocessedBatch(int batchSize);

  void lockForProcessing(List<Long> ids);

  QuotationAsk store(QuotationAsk quotationAsk, boolean ignoreValueType);

  void quotationAskUpdateStatus(String code);

  /**
   * Đếm số lượng yêu cầu báo giá theo tenantId và khoảng thời gian tạo
   */
  Long countByTenantIdAndCreatedAtBetween(Long tenantId, Instant startTime, Instant endTime);

  /**
   * Đếm số lượng yêu cầu báo giá theo tenantId, trạng thái và khoảng thời gian tạo
   */
  Long countByTenantIdAndStatusAndCreatedAtBetween(Long tenantId, QuotationStatus status, Instant startTime, Instant endTime);
}
