package com.actechx.gf.domain.repository.purchase;

import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequest;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

public interface PurchaseRequestRepository {
  PurchaseRequest save(PurchaseRequest purchaseRequestAgg);

  Optional<PurchaseRequest> findById(Long id);

  Optional<PurchaseRequest> findByCode(String code);

  Optional<PurchaseRequestEntity> findEntityByIdAndTenantId(Long id, Long tenantId);

  Optional<PurchaseRequestEntity> findEntityByCode(String code);

  Page<PurchaseRequestEntity> findAll(
      Specification<PurchaseRequestEntity> spec, PageRequest pageable);

  PurchaseRequestEntity store(PurchaseRequestEntity purchaseRequest);
}
