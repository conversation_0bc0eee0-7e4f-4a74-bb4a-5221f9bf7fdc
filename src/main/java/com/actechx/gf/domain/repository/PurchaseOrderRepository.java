package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

public interface PurchaseOrderRepository {
  PurchaseOrder create(PurchaseOrder purchaseOrder);

  PurchaseOrderEntity save(PurchaseOrderEntity purchaseOrderEntity);

  Optional<PurchaseOrderEntity> findByPurchaseRequestCodeAndSupplierId(
      String purchaseRequestCode, Long supplierId);

  List<PurchaseOrderEntity> findByPrIdAndTenantId(Long prId, Long tenantId);

  Page<PurchaseOrderEntity> searchData(
      Specification<PurchaseOrderEntity> orderEntitySpecification, PageRequest pageable);

  Optional<PurchaseOrderEntity> findByIdAndTenantId(Long id, Long tenantId);

  Optional<PurchaseOrderEntity> findByCodeAndTenantId(String code, Long tenantId);

  List<PurchaseOrderEntity> findByPurchaseOrderCodeOrSaleOrderCode(
      List<String> purchaseOrderCode, List<String> saleOrderCode);

  List<PurchaseOrderEntity> findByPurchaseRequestCode(String purchaseRequestCode);

  /**
   * Đếm số lượng đơn hàng theo purchaserId và trạng thái
   */
  Long countByPurchaserIdAndStatus(Long purchaserId, POStatusEnum status);

  /**
   * Đếm số lượng đơn hàng theo purchaserId, trạng thái và khoảng thời gian cập nhật
   */
  Long countByPurchaserIdAndStatusAndUpdatedAtBetween(Long purchaserId, POStatusEnum status, Instant startTime, Instant endTime);
}
