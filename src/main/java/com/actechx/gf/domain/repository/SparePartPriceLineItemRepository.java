package com.actechx.gf.domain.repository;

import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import java.util.Optional;

public interface SparePartPriceLineItemRepository {

  List<SparePartPriceLineItemEntity> getListByCode(String code);

  List<SparePartPriceLineItemEntity> getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
      List<Long> ids, List<QuotationBidStatus> statuses);

  Optional<SparePartPriceLineItemEntity> getById(Long id);

  Optional<SparePartPriceLineItemEntity> getByIdAndDetailStatusFalse(Long id);

  Optional<SparePartPriceLineItemEntity> getByIdAndDetailStatusTrue(Long id);

  void saveAllRepo(List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities);

  List<Long> findBySparePartInputCodeAndQuotationCodeRepo(
      String sparePartInputCode, String quotationAskCode);

  Optional<SparePartPriceLineItemEntity> getSparePartPriceLineItemProposal(
      String sparePartInputCode, Segment segment, String quotationAskCode, Long tenantId);
}
