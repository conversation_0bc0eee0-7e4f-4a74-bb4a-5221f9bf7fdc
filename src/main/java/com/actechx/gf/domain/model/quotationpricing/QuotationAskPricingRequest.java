package com.actechx.gf.domain.model.quotationpricing;

import com.actechx.gf.domain.model.enums.Segment;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuotationAskPricingRequest {

  private String quotationAskCode;

  private Long originTenantId;

  private List<AskedSparePartPricing> askedSpareParts;

  @Getter
  @Setter
  public static class AskedSparePartPricing {
    private Long sparePartLineItemId;

    private Long addSparePartLineItemId;

    private String code;

    private String partNameInput;

    private String partNameUnit;

    private Integer quantity;

    private Segment segment;

    private String refCode;

    private Long tenantId;
  }
}
