package com.actechx.gf.domain.model.quotationask;

import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddSparePartLineItemModel {
  private Long id;
  private Long tenantId;
  private String partNameInput;
  private String partNameUnit;
  private String refCode;

  private String sparePartInputCode;
  private Segment segment;
  private BigDecimal price;
  private String currency;
  private String note;
  private String unit;
  private Boolean detailStatus;
  private Boolean receiveDetailStatus;
  private Integer quantity;
  private BigDecimal materialPrice;
  private BigDecimal servicingPrice;
  private Instant updatedAt;
}
