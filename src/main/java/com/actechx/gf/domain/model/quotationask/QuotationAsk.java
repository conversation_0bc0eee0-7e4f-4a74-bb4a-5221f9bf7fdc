package com.actechx.gf.domain.model.quotationask;

import com.actechx.common.domain.AggregateRoot;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QuotationAsk extends AggregateRoot<Long> {

  @NotBlank private String code;

  @NotNull private Long tenantId;

  @NotBlank private String tenantName;

  private String tenantPhoneNumber;
  private String tenantOpsArea;
  private String tenantAddress;
  private String tenantOpsRegion;
  private String askNote;
  private Boolean isInvoiceRequired;

  private AskedVehicle askedVehicle;
  private List<AskedAttachment> attachments;
  private List<AskedSparePart> spareParts;

  private QuotationStatus status;

  private Boolean isProcessed = false;

  private LocalDateTime processedAt = null;

  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;

  // Factory method to create new QuotationAsk
  public QuotationAsk(
      Long id,
      String code,
      Long tenantId,
      String tenantName,
      String tenantPhoneNumber,
      String tenantOpsArea,
      String tenantAddress,
      String tenantOpsRegion,
      String askNote,
      AskedVehicle askedVehicle,
      List<AskedAttachment> attachments,
      List<AskedSparePart> spareParts) {

    if (id == null) {
      throw new IllegalArgumentException("ID cannot be null");
    }
    this.id = id;
    this.code = code;
    this.tenantId = tenantId;
    this.tenantName = tenantName;
    this.tenantPhoneNumber = tenantPhoneNumber;
    this.tenantOpsArea = tenantOpsArea;
    this.tenantAddress = tenantAddress;
    this.tenantOpsRegion = tenantOpsRegion;
    this.askNote = askNote;
    this.askedVehicle = askedVehicle;
    this.attachments = attachments;
    this.spareParts = spareParts;
    this.status = QuotationStatus.OPEN;
    this.isProcessed = false;
    this.processedAt = null;

    // Add domain event
    //    quotationAsk.addDomainEvent(new QuotationAskCreatedEvent(
    //        UUID.randomUUID().toString(),
    //        LocalDateTime.now(),
    //        quotationAsk.getId()
    //    ));
  }

  // Business method to mark as processed
  public void markAsProcessed() {
    this.isProcessed = true;
    this.processedAt = LocalDateTime.now();
    this.setUpdatedAt(Instant.now());
  }

  // Business method to update status
  public void updateStatus(QuotationStatus newStatus, String updatedBy) {
    this.status = newStatus;
    this.setUpdatedBy(updatedBy);
    this.setUpdatedAt(Instant.now());
  }

  public void deleteAttachment(AskedAttachment askedAttachment) {
    if (askedAttachment != null) {
      this.attachments.removeIf(x -> x.getId().equals(askedAttachment.getId()));
    }
  }

  public void deleteSpartPart(AskedSparePart askedSparePart) {
    if (askedSparePart != null) {
      this.spareParts.removeIf(x -> x.getId().equals(askedSparePart.getId()));
    }
  }

  public void addAttachment(AskedAttachment askedAttachment) {
    this.attachments.add(askedAttachment);
  }

  public void addSpartPart(AskedSparePart askedSparePart) {
    this.spareParts.add(askedSparePart);
  }

  public void updateAttachment(AskedAttachment askedAttachment) {
    for (AskedAttachment updateAttachment : this.attachments) {
      if (updateAttachment.getAttachmentUrl().equals(askedAttachment.getAttachmentUrl())) {
        updateAttachment.updateInfo(
            askedAttachment.getAttachmentUrl(),
            askedAttachment.getOwner(),
            askedAttachment.getNote());
      }
    }
  }

  public void updateSpartPart(AskedSparePart askedSparePart) {
    for (AskedSparePart updateSparePart : this.spareParts) {
      if (updateSparePart.getCode().equals(askedSparePart.getCode())) {
        updateSparePart.updateInfo(
            askedSparePart.getPartNameInput(),
            askedSparePart.getPartNameUnit(),
            askedSparePart.getCode(),
            askedSparePart.getRefCode(),
            askedSparePart.getTenantId());
      }
    }
  }

  public void updateVehicle(AskedVehicle askedVehicle) {
    this.askedVehicle = askedVehicle;
  }
}
