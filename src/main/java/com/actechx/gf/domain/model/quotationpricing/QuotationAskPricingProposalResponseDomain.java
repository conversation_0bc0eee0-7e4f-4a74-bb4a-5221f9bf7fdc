package com.actechx.gf.domain.model.quotationpricing;

import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuotationAskPricingProposalResponseDomain {
  private Long id;

  private String quotationAskCode;

  private Long replyTenantId;

  private List<AskedSparePartPricingProposalResponse> sparePartPriceLineItems;

  @Getter
  @Setter
  public static class AskedSparePartPricingProposalResponse {

    private Long id;

    private String sparePartInputCode;

    private Segment segment;

    private BigDecimal materialPrice;

    private BigDecimal servicingPrice;

    private String currency;

    private Integer quantity;

    // them vao
    private String partNameInput;

    private String unit;

    private String refCode;

    private String note;

    private boolean updatedStatus;
  }
}
