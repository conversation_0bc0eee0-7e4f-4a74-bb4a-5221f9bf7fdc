package com.actechx.gf.domain.model.purchaseorder;

import com.actechx.common.domain.AggregateRoot;
import com.actechx.gf.domain.model.enums.*;
import java.time.Instant;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrder extends AggregateRoot<Long> {
  private Long prId;

  private String code;

  private String saleOrderCode;

  private PurchaseSource source;

  private Long transportOrderId;

  private Long purchaserId;

  private Long supplierId;

  private String supplierName;

  private PaymentMethod paymentMethod;

  private Boolean isBestPrice;

  private Long transportRouteId;

  private String quotationAskCode; // Set<String> quotationAskCode, join(",")

  private POStage stage;

  private POStatusEnum status;

  // Lý do hủy của vendor
  private String note;

  private Instant createdAt;

  private Instant updatedAt;

  private String createdBy;

  private String updatedBy;

  private List<POProduct> poProducts;

  private List<POQuotationRef> poQuotationRefs;

  private POSupplier poSupplier;

  public static PurchaseOrder getPurchaseOrderAgg(
      Long id,
      Long prId,
      String code,
      String saleOrderCode,
      PurchaseSource source,
      Long transportOrderId,
      Long purchaserId,
      Long supplierId,
      String supplierName,
      PaymentMethod paymentMethod,
      Boolean isBestPrice,
      Long transportRouteId,
      String quotationAskCode,
      POStage stage,
      POStatusEnum status,
      String note,
      List<POProduct> poProducts,
      List<POQuotationRef> poQuotationRefs,
      POSupplier poSupplier) {
    PurchaseOrder purchaseOrder = new PurchaseOrder();
    purchaseOrder.id = id;
    purchaseOrder.prId = prId;
    purchaseOrder.source = source;
    purchaseOrder.transportOrderId = transportOrderId;
    purchaseOrder.purchaserId = purchaserId;
    purchaseOrder.supplierId = supplierId;
    purchaseOrder.supplierName = supplierName;
    purchaseOrder.paymentMethod = paymentMethod;
    purchaseOrder.isBestPrice = isBestPrice;
    purchaseOrder.transportRouteId = transportRouteId;
    purchaseOrder.quotationAskCode = quotationAskCode;
    purchaseOrder.stage = stage;
    purchaseOrder.status = status;
    purchaseOrder.code = code;
    purchaseOrder.saleOrderCode = saleOrderCode;
    purchaseOrder.note = note;
    purchaseOrder.poProducts = poProducts;
    purchaseOrder.poQuotationRefs = poQuotationRefs;
    purchaseOrder.poSupplier = poSupplier;
    return purchaseOrder;
  }

  public void updateInfo(
      Long transportOrderId,
      PaymentMethod paymentMethod,
      Boolean isBestPrice,
      POStage stage,
      POStatusEnum status,
      String note,
      String saleOrderCode,
      List<POProduct> poProducts,
      String updatedBy) {
    this.transportOrderId = transportOrderId;
    this.paymentMethod = paymentMethod;
    this.isBestPrice = isBestPrice;
    this.stage = stage;
    this.status = status;
    this.note = note;
    this.saleOrderCode = saleOrderCode;
    this.updatedAt = Instant.now();
    this.updatedBy = updatedBy;
    this.poProducts = poProducts;
    this.poSupplier.updateStatus(status);
  }
}
