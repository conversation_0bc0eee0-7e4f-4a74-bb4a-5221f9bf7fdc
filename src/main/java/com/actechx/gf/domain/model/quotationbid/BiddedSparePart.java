package com.actechx.gf.domain.model.quotationbid;

import com.actechx.common.domain.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BiddedSparePart extends Entity<Long> {
  private String partNameInput;
  private String partNameUnit;
  private String code;
  private String refCode;
  private Long tenantId;
  private boolean deleted;
}
