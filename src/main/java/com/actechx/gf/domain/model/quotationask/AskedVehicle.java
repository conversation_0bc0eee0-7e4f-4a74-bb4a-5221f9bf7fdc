package com.actechx.gf.domain.model.quotationask;

import com.actechx.common.domain.Entity;
import com.actechx.gf.domain.model.enums.CarType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AskedVehicle extends Entity<Long> {
  private String carBrand;
  private String carModel;
  private String yearOfManufacture;
  private CarType carType;
  private String trimsLevel;
  private String vin;
}
