package com.actechx.gf.domain.model.quotationask;

import com.actechx.common.domain.Entity;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AskedSparePart extends Entity<Long> {

  @NotBlank private String partNameInput;
  private String partNameUnit;
  private String code;
  private String refCode;
  private Long tenantId;
  private boolean deleted;

  protected void updateInfo(
      String partNameInput, String partNameUnit, String code, String refCode, Long tenantId) {
    this.partNameInput = partNameInput;
    this.partNameUnit = partNameUnit;
    this.code = code;
    this.refCode = refCode;
    this.tenantId = tenantId;
  }
}
