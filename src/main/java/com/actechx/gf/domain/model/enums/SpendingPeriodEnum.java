package com.actechx.gf.domain.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SpendingPeriodEnum {
  
  /**
   * Tuần này: <PERSON><PERSON><PERSON> thị 7 cột tương ứng với 7 ngày
   */
  THIS_WEEK("this_week", "Tuần này", 7),
  
  /**
   * Tháng này: Hi<PERSON><PERSON> thị 4 cột tương ứng 4 tuần
   */
  THIS_MONTH("this_month", "Tháng này", 4),
  
  /**
   * 6 tháng gần nhất: <PERSON><PERSON><PERSON> thị 6 cột tương ứng 6 tháng gần nhất
   * Hiển thị dữ liệu tháng hiện tại và trọn vẹn 5 tháng trước đó
   */
  LAST_6_MONTHS("last_6_months", "6 tháng gần nhất", 6),
  
  /**
   * 1 năm gần nhất: <PERSON><PERSON><PERSON> thị 4 cột tương ứng với 4 quý
   * <PERSON><PERSON><PERSON> thị dữ liệu quý hiện tại và trọn vẹn 3 quý trước đó
   */
  LAST_YEAR("last_year", "1 năm gần nhất", 4);
  
  private final String value;
  private final String displayName;
  private final int dataPoints;
  
  /**
   * Tìm enum theo value
   * @param value Giá trị cần tìm
   * @return SpendingPeriodEnum tương ứng
   * @throws IllegalArgumentException nếu không tìm thấy
   */
  public static SpendingPeriodEnum fromValue(String value) {
    for (SpendingPeriodEnum period : values()) {
      if (period.getValue().equals(value)) {
        return period;
      }
    }
    throw new IllegalArgumentException("Invalid spending period: " + value);
  }
}
