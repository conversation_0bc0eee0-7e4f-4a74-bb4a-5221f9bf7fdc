package com.actechx.gf.domain.model.purchaseorder;

import com.actechx.common.domain.Entity;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.POSupplyStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class POSupplier extends Entity<Long> {

  private Long poId;

  private Long supplierId;

  private POSupplyStatus status;

  protected void updateStatus(POStatusEnum targetStatus) {
    if (POStatusEnum.WAIT_GARAGE_TO_CONFIRM.equals(targetStatus)) {
      this.status = POSupplyStatus.STOCK_CHANGED;
    } else if (POStatusEnum.CONFIRMED.equals(targetStatus)) {
      this.status = POSupplyStatus.SALES_ORDER_CONFIRM;
    } else if (POStatusEnum.CANCELLED.equals(targetStatus)) {
      this.status = POSupplyStatus.DECLINED;
    }
  }
}
