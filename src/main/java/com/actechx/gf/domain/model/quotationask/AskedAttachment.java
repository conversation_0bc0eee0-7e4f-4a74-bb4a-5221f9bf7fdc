package com.actechx.gf.domain.model.quotationask;

import com.actechx.common.domain.Entity;
import com.actechx.gf.domain.model.enums.OwnerType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AskedAttachment extends Entity<Long> {

  @NotBlank private String attachmentUrl;
  @NotNull private OwnerType owner;
  private boolean deleted;
  private String note;

  protected void updateInfo(String attachmentUrl, OwnerType owner, String note) {
    this.attachmentUrl = attachmentUrl;
    this.owner = owner;
    this.note = note;
  }
}
