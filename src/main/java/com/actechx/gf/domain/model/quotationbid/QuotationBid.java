package com.actechx.gf.domain.model.quotationbid;

import com.actechx.common.domain.AggregateRoot;
import com.actechx.common.enums.TenantType;
import com.actechx.gf.domain.model.enums.BidType;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QuotationBid extends AggregateRoot<Long> {

  private Long tenantId;

  private TenantType tenantType;

  private String quotationAskCode;

  private BidType bidType;

  private QuotationBidStatus status;

  private String note;

  private List<SparePartPriceLineItem> sparePartPriceLineItems = new ArrayList<>();

  private List<AddedSparePartPriceLineItem> addedSparePartPriceLineItems = new ArrayList<>();

  private List<BiddedSparePart> biddedSpareParts = new ArrayList<>();

  private Instant createdAt;

  private Instant updatedAt;

  private String createdBy;

  private String updatedBy;

  public QuotationBid(
      Long id,
      Long tenantId,
      TenantType tenantType,
      String quotationAskCode,
      BidType bidType,
      QuotationBidStatus status,
      String note,
      List<SparePartPriceLineItem> sparePartPriceLineItems,
      List<AddedSparePartPriceLineItem> addedSparePartPriceLineItems,
      List<BiddedSparePart> biddedSpareParts,
      String createdBy) {
    if (id == null) {
      throw new IllegalArgumentException("ID cannot be null");
    }
    this.id = id;
    this.tenantId = tenantId;
    this.tenantType = tenantType;
    this.quotationAskCode = quotationAskCode;
    this.bidType = bidType;
    this.status = status;
    this.note = note;
    this.sparePartPriceLineItems = sparePartPriceLineItems;
    this.addedSparePartPriceLineItems = addedSparePartPriceLineItems;
    this.biddedSpareParts = biddedSpareParts;
    this.createdBy = createdBy;
  }

  public void update(
      TenantType tenantType,
      String quotationAskCode,
      BidType bidType,
      QuotationBidStatus status,
      String note,
      List<SparePartPriceLineItem> sparePartPriceLineItems,
      List<AddedSparePartPriceLineItem> addedSparePartPriceLineItems,
      List<BiddedSparePart> biddedSpareParts,
      String updatedBy) {
    this.tenantType = tenantType;
    this.quotationAskCode = quotationAskCode;
    this.bidType = bidType;
    this.status = status;
    this.note = note;
    this.sparePartPriceLineItems = sparePartPriceLineItems;
    this.addedSparePartPriceLineItems = addedSparePartPriceLineItems;
    this.biddedSpareParts = biddedSpareParts;
    this.updatedBy = updatedBy;
  }
}
