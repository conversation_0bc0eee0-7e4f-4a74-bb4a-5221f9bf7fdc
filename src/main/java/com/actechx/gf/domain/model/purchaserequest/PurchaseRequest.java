package com.actechx.gf.domain.model.purchaserequest;

import com.actechx.common.domain.AggregateRoot;
import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PurchaseRequest extends AggregateRoot<Long> {

  @NotBlank private String code;

  @NotNull private Long purchaserId;

  @NotNull private PurchaseRequestStatus status;

  private List<String> statusTransitionData;

  private List<PurchaseRequestData> purchaseRequestDataList;

  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;

  public PurchaseRequest(Long id, String code, Long purchaserId) {

    if (id == null) {
      throw new IllegalArgumentException("ID cannot be null");
    }
    if (code == null || code.trim().isEmpty()) {
      throw new IllegalArgumentException("Code cannot be null or empty");
    }
    if (purchaserId == null) {
      throw new IllegalArgumentException("Purchaser ID cannot be null");
    }

    this.id = id;
    this.code = code;
    this.purchaserId = purchaserId;
    this.status = PurchaseRequestStatus.OPEN;
    this.statusTransitionData = new ArrayList<>();
    this.purchaseRequestDataList = new ArrayList<>();
  }

  public void updateStatus(PurchaseRequestStatus newStatus, String updatedBy) {
    if (newStatus == null) {
      throw new IllegalArgumentException("Status cannot be null");
    }

    PurchaseRequestStatus oldStatus = this.status;
    this.status = newStatus;
    this.updatedBy = updatedBy;
    this.updatedAt = Instant.now();

    String transition =
        String.format("%s -> %s by %s at %s", oldStatus, newStatus, updatedBy, Instant.now());
    this.statusTransitionData.add(transition);
  }

  public void addPurchaseRequestData(PurchaseRequestData purchaseRequestData) {
    if (purchaseRequestData == null) {
      throw new IllegalArgumentException("Purchase request data cannot be null");
    }

    purchaseRequestData.setPurchaseRequestId(this.id);
    this.purchaseRequestDataList.add(purchaseRequestData);
    this.updatedAt = Instant.now();
  }

  public void removePurchaseRequestData(Long purchaseRequestDataId) {
    if (purchaseRequestDataId == null) {
      throw new IllegalArgumentException("Purchase request data ID cannot be null");
    }

    this.purchaseRequestDataList.removeIf(data -> data.getId().equals(purchaseRequestDataId));
    this.updatedAt = Instant.now();
  }

  public void updatePurchaseRequestData(PurchaseRequestData updatedData) {
    if (updatedData == null || updatedData.getId() == null) {
      throw new IllegalArgumentException("Updated data and its ID cannot be null");
    }

    for (int i = 0; i < this.purchaseRequestDataList.size(); i++) {
      if (this.purchaseRequestDataList.get(i).getId().equals(updatedData.getId())) {
        this.purchaseRequestDataList.set(i, updatedData);
        this.updatedAt = Instant.now();
        break;
      }
    }
  }

  public boolean canCreateOrder() {
    return this.status == PurchaseRequestStatus.OPEN && !this.purchaseRequestDataList.isEmpty();
  }

  public void markAsOrderCreated(String updatedBy) {
    if (!canCreateOrder()) {
      throw new IllegalStateException("Cannot create order for purchase request in current state");
    }

    updateStatus(PurchaseRequestStatus.ORDER_CREATED, updatedBy);
  }

  public void markAsInsufficientQuantity(String updatedBy) {
    updateStatus(PurchaseRequestStatus.INSUFFICIENT_QUANTITY, updatedBy);
  }

  public void markAsInsufficientProduct(String updatedBy) {
    updateStatus(PurchaseRequestStatus.INSUFFICIENT_PRODUCT, updatedBy);
  }

  public void cancel(String updatedBy) {
    updateStatus(PurchaseRequestStatus.CANCELLED, updatedBy);
  }

  public int getTotalRequestedQuantity() {
    return this.purchaseRequestDataList.stream()
        .mapToInt(PurchaseRequestData::getRequestedQuantity)
        .sum();
  }

  public int getTotalConfirmedQuantity() {
    return this.purchaseRequestDataList.stream()
        .filter(data -> data.getActualSalesQuantity() != null)
        .mapToInt(PurchaseRequestData::getActualSalesQuantity)
        .sum();
  }
}
