package com.actechx.gf.domain.model.quotationbid;

import com.actechx.common.domain.Entity;
import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AddedSparePartPriceLineItem extends Entity<Long> {

  private String sparePartInputCode;

  private Segment segment;

  private BigDecimal price;

  private String currency;

  private String note;

  private String unit;

  private boolean pickedToPO;

  private boolean deleted;
}
