package com.actechx.gf.domain.model.purchaserequest;

import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SparePartPriceItemModel {
  private Long id;

  private String code;

  private String partNameInput;

  private String partNameUnit;

  private Integer quantity;

  private Segment segment;

  private String refCode;

  private Long tenantId;

  private BigDecimal materialPrice;

  private BigDecimal servicingPrice;

  private BigDecimal priceTotal;

  private String note;
}
