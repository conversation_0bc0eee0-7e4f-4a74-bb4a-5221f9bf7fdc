package com.actechx.gf.domain.model.purchaseorder;

import com.actechx.common.domain.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class POTransitionHistory extends Entity<Long> {

  private Long poId;

  private String previousStages;

  private String transitionData;
}
