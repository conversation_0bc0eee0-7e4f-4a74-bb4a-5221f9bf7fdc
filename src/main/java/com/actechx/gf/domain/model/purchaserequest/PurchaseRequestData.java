package com.actechx.gf.domain.model.purchaserequest;

import com.actechx.gf.domain.model.enums.PurchaseRequestDataStatus;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestData {
  private Long id;

  @NotNull private String requestedProductName;

  @NotNull private String quotationAskCode;

  @NotNull private Long productId;

  @NotNull private Long purchaseRequestId;

  @NotNull private Long purchaserId;

  @NotNull private Long supplierId;

  @NotNull private Integer requestedQuantity;

  private Integer actualSalesQuantity;

  @NotNull private BigDecimal detailedPrice;

  private BigDecimal materialPrice;

  private BigDecimal servicingPrice;

  @NotNull private PurchaseRequestDataStatus salesStatus;

  private Instant createdAt;
  private Instant updatedAt;
  private String createdBy;
  private String updatedBy;

  public PurchaseRequestData(
      String requestedProductName,
      String quotationAskCode,
      Long productId,
      Long purchaseRequestId,
      Long purchaserId,
      Long supplierId,
      Integer requestedQuantity,
      BigDecimal detailedPrice,
      BigDecimal materialPrice,
      BigDecimal servicingPrice) {
    this.requestedProductName = requestedProductName;
    this.quotationAskCode = quotationAskCode;
    this.productId = productId;
    this.purchaseRequestId = purchaseRequestId;
    this.purchaserId = purchaserId;
    this.supplierId = supplierId;
    this.requestedQuantity = requestedQuantity;
    this.detailedPrice = detailedPrice;
    this.materialPrice = materialPrice;
    this.servicingPrice = servicingPrice;
    this.salesStatus = PurchaseRequestDataStatus.OPEN;
  }

  public void updateSalesQuantity(Integer actualSalesQuantity) {
    this.actualSalesQuantity = actualSalesQuantity;
    this.updatedAt = Instant.now();
  }

  public void updateDetailedPrice(BigDecimal detailedPrice) {
    this.detailedPrice = detailedPrice;
    this.updatedAt = Instant.now();
  }

  public void updateSalesStatus(PurchaseRequestDataStatus newStatus, String updatedBy) {
    this.salesStatus = newStatus;
    this.updatedBy = updatedBy;
    this.updatedAt = Instant.now();
  }

  public void confirmSale(Integer actualSalesQuantity, BigDecimal detailedPrice, String updatedBy) {
    this.actualSalesQuantity = actualSalesQuantity;
    this.detailedPrice = detailedPrice;
    this.salesStatus = PurchaseRequestDataStatus.CONFIRMED;
    this.updatedBy = updatedBy;
    this.updatedAt = Instant.now();
  }
}
