package com.actechx.gf.domain.model.purchaseorder;

import com.actechx.common.domain.Entity;
import com.actechx.gf.domain.model.enums.Segment;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class POProduct extends Entity<Long> {

  private Long poId;

  private Long productId;

  private String requestedProductName;

  private Long quantity;

  private BigDecimal unitPrice;

  private String unit;

  private Segment segment;

  private Long supplierId;

  private String quotationAskCode;
}
