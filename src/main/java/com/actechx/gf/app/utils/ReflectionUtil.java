package com.actechx.gf.app.utils;

import java.lang.reflect.Field;

public class ReflectionUtil {
  public static Object getFieldValue(Object obj, String fieldName)
      throws NoSuchFieldException, IllegalAccessException {
    Field field = getField(obj.getClass(), fieldName);
    field.setAccessible(true);
    return field.get(obj);
  }

  private static Field getField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
    while (clazz != null) {
      try {
        return clazz.getDeclaredField(fieldName);
      } catch (NoSuchFieldException e) {
        clazz = clazz.getSuperclass();
      }
    }
    throw new NoSuchFieldException("Field not found: " + fieldName);
  }
}
