package com.actechx.gf.app.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MoneyUtils {
  
  private static final BigDecimal ONE_MILLION = new BigDecimal("1000000");
  private static final BigDecimal ONE_BILLION = new BigDecimal("1000000000");
  
  /**
   * Làm tròn số tiền theo quy tắc:
   * - < 1 triệu: hiển thị đủ số tiền (2000đ, 50.000đ, 500.000đ)
   * - Giữa 1 triệu và 1 tỷ: làm tròn triệu (2 triệu, 20 triệu, 200 triệu)
   * - Trên 1 tỷ: làm tròn tỷ (2 tỷ, 20 tỷ, 200 tỷ)
   * - Làm tròn 2 chữ số (nếu có lẻ)
   * 
   * @param amount Số tiền cần làm tròn
   * @return Số tiền đã được làm tròn
   */
  public static BigDecimal formatMoney(BigDecimal amount) {
    if (amount == null) {
      return BigDecimal.ZERO;
    }
    
    // Làm tròn về số nguyên trước
    BigDecimal roundedAmount = amount.setScale(0, RoundingMode.HALF_UP);
    
    if (roundedAmount.compareTo(ONE_MILLION) < 0) {
      // < 1 triệu: hiển thị đủ số tiền
      return roundedAmount;
    } else if (roundedAmount.compareTo(ONE_BILLION) < 0) {
      // Giữa 1 triệu và 1 tỷ: làm tròn triệu
      return roundToMillion(roundedAmount);
    } else {
      // Trên 1 tỷ: làm tròn tỷ
      return roundToBillion(roundedAmount);
    }
  }
  
  /**
   * Làm tròn về triệu với 2 chữ số có nghĩa
   * Ví dụ: 1,234,567 -> 1,200,000 (1.2 triệu)
   *        12,345,678 -> 12,000,000 (12 triệu)
   *        123,456,789 -> 120,000,000 (120 triệu)
   */
  private static BigDecimal roundToMillion(BigDecimal amount) {
    // Chia cho 1 triệu để có số triệu
    BigDecimal millions = amount.divide(ONE_MILLION, 2, RoundingMode.HALF_UP);
    
    // Làm tròn về 2 chữ số có nghĩa
    BigDecimal roundedMillions = roundToTwoSignificantDigits(millions);
    
    // Nhân lại với 1 triệu
    return roundedMillions.multiply(ONE_MILLION).setScale(0, RoundingMode.HALF_UP);
  }
  
  /**
   * Làm tròn về tỷ với 2 chữ số có nghĩa
   * Ví dụ: 1,234,567,890 -> 1,200,000,000 (1.2 tỷ)
   *        12,345,678,900 -> 12,000,000,000 (12 tỷ)
   *        123,456,789,000 -> 120,000,000,000 (120 tỷ)
   */
  private static BigDecimal roundToBillion(BigDecimal amount) {
    // Chia cho 1 tỷ để có số tỷ
    BigDecimal billions = amount.divide(ONE_BILLION, 2, RoundingMode.HALF_UP);
    
    // Làm tròn về 2 chữ số có nghĩa
    BigDecimal roundedBillions = roundToTwoSignificantDigits(billions);
    
    // Nhân lại với 1 tỷ
    return roundedBillions.multiply(ONE_BILLION).setScale(0, RoundingMode.HALF_UP);
  }
  
  /**
   * Làm tròn về 2 chữ số có nghĩa
   * Ví dụ: 1.234 -> 1.2
   *        12.34 -> 12
   *        123.4 -> 120
   */
  private static BigDecimal roundToTwoSignificantDigits(BigDecimal number) {
    if (number.compareTo(BigDecimal.ZERO) == 0) {
      return BigDecimal.ZERO;
    }
    
    // Tìm số chữ số trước dấu phẩy
    int integerDigits = number.setScale(0, RoundingMode.DOWN).toString().length();
    
    if (integerDigits >= 2) {
      // Nếu có từ 2 chữ số trở lên, làm tròn về số nguyên và chỉ giữ 2 chữ số đầu
      int scale = -(integerDigits - 2);
      return number.setScale(scale, RoundingMode.HALF_UP);
    } else {
      // Nếu chỉ có 1 chữ số, giữ 1 chữ số thập phân
      return number.setScale(1, RoundingMode.HALF_UP);
    }
  }
  
  /**
   * Format số tiền thành chuỗi hiển thị với đơn vị
   * 
   * @param amount Số tiền gốc
   * @return Chuỗi hiển thị với đơn vị (VD: "1.2 triệu", "15 tỷ")
   */
  public static String formatMoneyWithUnit(BigDecimal amount) {
    if (amount == null) {
      return "0đ";
    }
    
    BigDecimal roundedAmount = amount.setScale(0, RoundingMode.HALF_UP);
    
    if (roundedAmount.compareTo(ONE_MILLION) < 0) {
      // < 1 triệu: hiển thị với đơn vị đồng
      return String.format("%,dđ", roundedAmount.longValue());
    } else if (roundedAmount.compareTo(ONE_BILLION) < 0) {
      // Giữa 1 triệu và 1 tỷ: hiển thị với đơn vị triệu
      BigDecimal millions = roundedAmount.divide(ONE_MILLION, 1, RoundingMode.HALF_UP);
      if (millions.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
        return String.format("%d triệu", millions.intValue());
      } else {
        return String.format("%.1f triệu", millions.doubleValue());
      }
    } else {
      // Trên 1 tỷ: hiển thị với đơn vị tỷ
      BigDecimal billions = roundedAmount.divide(ONE_BILLION, 1, RoundingMode.HALF_UP);
      if (billions.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
        return String.format("%d tỷ", billions.intValue());
      } else {
        return String.format("%.1f tỷ", billions.doubleValue());
      }
    }
  }
}
