package com.actechx.gf.app.utils;

import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.domain.model.quotationask.AddSparePartLineItemModel;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class QuotationUtil {
  public static List<AddSparePartLineItemModel>
      getAddSparePartPriceLineItemsReceiveDetailStatusTrue(
          SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    return getAddSparePartPriceLineItems(sparePartPriceLineItemEntity).stream()
        .filter(AddSparePartLineItemModel::getReceiveDetailStatus)
        .toList();
  }

  public static List<AddSparePartLineItemModel> getAddSparePartPriceLineItems(
      SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    List<AddSparePartLineItemModel> list = new ArrayList<>();
    QuotationBidEntity quotationBidEntity = sparePartPriceLineItemEntity.getQuotationBid();
    Long tenantId = quotationBidEntity.getTenantId();
    List<BiddedSparePartEntity> biddedSparePartEntities =
        quotationBidEntity.getBiddedSpareParts().stream()
            .filter(
                x -> sparePartPriceLineItemEntity.getSparePartInputCode().equals(x.getRefCode()))
            .toList();
    biddedSparePartEntities.forEach(
        x -> {
          List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntitys =
              quotationBidEntity.getAddedSparePartPriceLineItems().stream()
                  .filter(a -> a.getSparePartInputCode().equals(x.getCode()))
                  .toList();
          addedSparePartPriceLineItemEntitys.forEach(
              y -> {
                AddSparePartLineItemModel model = new AddSparePartLineItemModel();
                model.setId(y.getId());
                model.setTenantId(tenantId);
                model.setPartNameInput(x.getPartNameInput());
                model.setPartNameUnit(x.getPartNameUnit());
                model.setRefCode(x.getRefCode());
                model.setSparePartInputCode(y.getSparePartInputCode());
                model.setSegment(y.getSegment());
                model.setPrice(y.getPrice());
                model.setCurrency(y.getCurrency());
                model.setNote(y.getNote());
                model.setUnit(y.getUnit());
                model.setDetailStatus(y.getDetailStatus());
                model.setReceiveDetailStatus(y.getReceiveDetailStatus());
                model.setQuantity(y.getQuantity());
                model.setMaterialPrice(y.getMaterialPrice());
                model.setServicingPrice(y.getServicingPrice());
                model.setUpdatedAt(y.getUpdatedAt());
                list.add(model);
              });
        });
    return list;
  }
}
