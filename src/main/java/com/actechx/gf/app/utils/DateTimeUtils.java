package com.actechx.gf.app.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateTimeUtils {

  private static final ZoneId VIETNAM_ZONE = ZoneId.of("Asia/Ho_Chi_Minh");

  public static LocalDateTime convertInstantToLocalDateTime(Instant instant) {
    return LocalDateTime.ofInstant(instant, VIETNAM_ZONE);
  }

  /**
   * Lấy thời điểm bắt đầu tuần hiện tại (thứ 2 00:00:00)
   * @return Instant của thời điểm bắt đầu tuần
   */
  public static Instant getStartOfCurrentWeek() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfWeek = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        .truncatedTo(ChronoUnit.DAYS);
    return startOfWeek.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc tuần hiện tại (chủ nhật 23:59:59.999)
   * @return Instant của thời điểm kết thúc tuần
   */
  public static Instant getEndOfCurrentWeek() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfWeek = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfWeek.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm bắt đầu tháng hiện tại (ngày 1 00:00:00)
   * @return Instant của thời điểm bắt đầu tháng
   */
  public static Instant getStartOfCurrentMonth() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfMonth = now.with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS);
    return startOfMonth.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc tháng hiện tại (ngày cuối 23:59:59.999)
   * @return Instant của thời điểm kết thúc tháng
   */
  public static Instant getEndOfCurrentMonth() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfMonth.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm bắt đầu năm hiện tại (1/1 00:00:00)
   * @return Instant của thời điểm bắt đầu năm
   */
  public static Instant getStartOfCurrentYear() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfYear = now.with(TemporalAdjusters.firstDayOfYear())
        .truncatedTo(ChronoUnit.DAYS);
    return startOfYear.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc năm hiện tại (31/12 23:59:59.999)
   * @return Instant của thời điểm kết thúc năm
   */
  public static Instant getEndOfCurrentYear() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfYear = now.with(TemporalAdjusters.lastDayOfYear())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfYear.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy danh sách 7 ngày trong tuần hiện tại (từ thứ 2 đến chủ nhật)
   * @return List<Instant[]> với mỗi phần tử là [startOfDay, endOfDay]
   */
  public static List<Instant[]> getDaysOfCurrentWeek() {
    List<Instant[]> days = new ArrayList<>();
    LocalDateTime startOfWeek = LocalDateTime.now(VIETNAM_ZONE)
        .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        .truncatedTo(ChronoUnit.DAYS);

    for (int i = 0; i < 7; i++) {
      LocalDateTime dayStart = startOfWeek.plusDays(i);
      LocalDateTime dayEnd = dayStart.plusDays(1).minusNanos(1);
      days.add(new Instant[]{
          dayStart.atZone(VIETNAM_ZONE).toInstant(),
          dayEnd.atZone(VIETNAM_ZONE).toInstant()
      });
    }
    return days;
  }

  /**
   * Lấy danh sách 4 tuần trong tháng hiện tại
   * @return List<Instant[]> với mỗi phần tử là [startOfWeek, endOfWeek]
   */
  public static List<Instant[]> getWeeksOfCurrentMonth() {
    List<Instant[]> weeks = new ArrayList<>();
    LocalDateTime startOfMonth = LocalDateTime.now(VIETNAM_ZONE)
        .with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS);
    LocalDateTime endOfMonth = LocalDateTime.now(VIETNAM_ZONE)
        .with(TemporalAdjusters.lastDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);

    // Tìm thứ 2 đầu tiên của tháng (có thể ở tháng trước)
    LocalDateTime firstMonday = startOfMonth.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

    LocalDateTime weekStart = firstMonday;
    int weekCount = 0;

    while (weekStart.isBefore(endOfMonth.toLocalDate().atStartOfDay()) && weekCount < 4) {
      LocalDateTime weekEnd = weekStart.plusDays(6).truncatedTo(ChronoUnit.DAYS).plusDays(1).minusNanos(1);

      // Điều chỉnh để không vượt quá tháng hiện tại
      if (weekEnd.isAfter(endOfMonth)) {
        weekEnd = endOfMonth;
      }

      weeks.add(new Instant[]{
          weekStart.atZone(VIETNAM_ZONE).toInstant(),
          weekEnd.atZone(VIETNAM_ZONE).toInstant()
      });

      weekStart = weekStart.plusDays(7);
      weekCount++;
    }

    return weeks;
  }

  /**
   * Lấy danh sách 6 tháng gần nhất (tháng hiện tại + 5 tháng trước)
   * Tháng hiện tại tính từ ngày 1 đến ngày hiện tại
   * @return List<Instant[]> với mỗi phần tử là [startOfMonth, endOfMonth]
   */
  public static List<Instant[]> getLast6Months() {
    List<Instant[]> months = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);

    for (int i = 5; i >= 0; i--) {
      LocalDateTime monthStart = now.minusMonths(i)
          .with(TemporalAdjusters.firstDayOfMonth())
          .truncatedTo(ChronoUnit.DAYS);

      LocalDateTime monthEnd;
      if (i == 0) {
        // Tháng hiện tại: từ ngày 1 đến ngày hiện tại
        monthEnd = now.truncatedTo(ChronoUnit.DAYS).plusDays(1).minusNanos(1);
      } else {
        // Tháng trước: trọn vẹn cả tháng
        monthEnd = now.minusMonths(i)
            .with(TemporalAdjusters.lastDayOfMonth())
            .truncatedTo(ChronoUnit.DAYS)
            .plusDays(1)
            .minusNanos(1);
      }

      months.add(new Instant[]{
          monthStart.atZone(VIETNAM_ZONE).toInstant(),
          monthEnd.atZone(VIETNAM_ZONE).toInstant()
      });
    }

    return months;
  }

  /**
   * Lấy danh sách 4 quý gần nhất (quý hiện tại + 3 quý trước)
   * Quý hiện tại tính từ ngày đầu quý đến ngày hiện tại
   * @return List<Instant[]> với mỗi phần tử là [startOfQuarter, endOfQuarter]
   */
  public static List<Instant[]> getLast4Quarters() {
    List<Instant[]> quarters = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);

    for (int i = 3; i >= 0; i--) {
      LocalDateTime quarterStart = getStartOfQuarter(now.minusMonths(i * 3));
      LocalDateTime quarterEnd;

      if (i == 0) {
        // Quý hiện tại: từ đầu quý đến ngày hiện tại
        quarterEnd = now.truncatedTo(ChronoUnit.DAYS).plusDays(1).minusNanos(1);
      } else {
        // Quý trước: trọn vẹn cả quý
        quarterEnd = getEndOfQuarter(now.minusMonths(i * 3));
      }

      quarters.add(new Instant[]{
          quarterStart.atZone(VIETNAM_ZONE).toInstant(),
          quarterEnd.atZone(VIETNAM_ZONE).toInstant()
      });
    }

    return quarters;
  }

  /**
   * Lấy ngày đầu quý của một thời điểm
   */
  private static LocalDateTime getStartOfQuarter(LocalDateTime dateTime) {
    int currentMonth = dateTime.getMonthValue();
    int quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1;

    return dateTime.withMonth(quarterStartMonth)
        .with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS);
  }

  /**
   * Lấy ngày cuối quý của một thời điểm
   */
  private static LocalDateTime getEndOfQuarter(LocalDateTime dateTime) {
    int currentMonth = dateTime.getMonthValue();
    int quarterEndMonth = ((currentMonth - 1) / 3 + 1) * 3;

    return dateTime.withMonth(quarterEndMonth)
        .with(TemporalAdjusters.lastDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
  }
}
