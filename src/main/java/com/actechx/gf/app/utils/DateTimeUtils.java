package com.actechx.gf.app.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateTimeUtils {
  public static LocalDateTime convertInstantToLocalDateTime(Instant instant) {
    ZoneId zoneId = ZoneId.of("Asia/Ho_Chi_Minh");
    return LocalDateTime.ofInstant(instant, zoneId);
  }
}
