package com.actechx.gf.app.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.DayOfWeek;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateTimeUtils {

  private static final ZoneId VIETNAM_ZONE = ZoneId.of("Asia/Ho_Chi_Minh");

  public static LocalDateTime convertInstantToLocalDateTime(Instant instant) {
    return LocalDateTime.ofInstant(instant, VIETNAM_ZONE);
  }

  /**
   * L<PERSON>y thời điểm bắt đầu tuần hiện tại (thứ 2 00:00:00)
   * @return Instant của thời điểm bắt đầu tuần
   */
  public static Instant getStartOfCurrentWeek() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfWeek = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        .truncatedTo(ChronoUnit.DAYS);
    return startOfWeek.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc tuần hiện tại (chủ nhật 23:59:59.999)
   * @return Instant của thời điểm kết thúc tuần
   */
  public static Instant getEndOfCurrentWeek() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfWeek = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfWeek.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm bắt đầu tháng hiện tại (ngày 1 00:00:00)
   * @return Instant của thời điểm bắt đầu tháng
   */
  public static Instant getStartOfCurrentMonth() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfMonth = now.with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS);
    return startOfMonth.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc tháng hiện tại (ngày cuối 23:59:59.999)
   * @return Instant của thời điểm kết thúc tháng
   */
  public static Instant getEndOfCurrentMonth() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfMonth.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm bắt đầu năm hiện tại (1/1 00:00:00)
   * @return Instant của thời điểm bắt đầu năm
   */
  public static Instant getStartOfCurrentYear() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime startOfYear = now.with(TemporalAdjusters.firstDayOfYear())
        .truncatedTo(ChronoUnit.DAYS);
    return startOfYear.atZone(VIETNAM_ZONE).toInstant();
  }

  /**
   * Lấy thời điểm kết thúc năm hiện tại (31/12 23:59:59.999)
   * @return Instant của thời điểm kết thúc năm
   */
  public static Instant getEndOfCurrentYear() {
    LocalDateTime now = LocalDateTime.now(VIETNAM_ZONE);
    LocalDateTime endOfYear = now.with(TemporalAdjusters.lastDayOfYear())
        .truncatedTo(ChronoUnit.DAYS)
        .plusDays(1)
        .minusNanos(1);
    return endOfYear.atZone(VIETNAM_ZONE).toInstant();
  }
}
