package com.actechx.gf.app.utils;

import com.actechx.common.utils.JsonUtils;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

public class ObjectDiffer {

  public static <T> List<FieldChange> compareObjects(
      T oldObj, T newObj, String prefix, List<String> fieldsToCompare) {
    List<FieldChange> changes = new ArrayList<>();

    for (String field : fieldsToCompare) {
      try {
        Object oldValue = ReflectionUtil.getFieldValue(oldObj, field);
        Object newValue = ReflectionUtil.getFieldValue(newObj, field);
        Object normalizeOldValue = normalize(oldValue);
        Object normalizeNewValue = normalize(newValue);
        if (!Objects.equals(normalizeOldValue, normalizeNewValue)) {
          changes.add(
              new FieldChange(
                  prefix + "." + field,
                  normalizeOldValue != null ? normalizeOldValue.toString() : null,
                  normalizeNewValue != null ? normalizeNewValue.toString() : null));
        }
      } catch (Exception e) {
        throw new RuntimeException("Failed to read field: " + field, e);
      }
    }
    return changes;
  }

  // So sánh hai collection có id (List<Part>, List<Attachment>, ...)
  public static <T> List<FieldChange> compareWithIdMatch(
      Collection<T> oldList,
      Collection<T> newList,
      Function<T, Object> idGetter,
      List<String> fieldsToCompare,
      String prefix) {
    List<FieldChange> changes = new ArrayList<>();

    Map<Object, T> oldMap = toUniqueMap(oldList, idGetter);
    Map<Object, T> newMap = toUniqueMap(newList, idGetter);

    Set<Object> allIds = new HashSet<>();
    allIds.addAll(oldMap.keySet());
    allIds.addAll(newMap.keySet());

    for (Object id : allIds) {
      T oldObj = oldMap.get(id);
      T newObj = newMap.get(id);
      String basePath = prefix + "[" + id + "]";

      if (oldObj == null) {
        changes.add(new FieldChange(basePath, null, JsonUtils.toJson(newObj)));
      } else if (newObj == null) {
        changes.add(new FieldChange(basePath, JsonUtils.toJson(oldObj), null));
      } else {
        changes.addAll(compareObjects(oldObj, newObj, basePath, fieldsToCompare));
      }
    }
    return changes;
  }

  private static Object normalize(Object value) {
    if (value instanceof BigDecimal bigDecimal) {
      return bigDecimal.stripTrailingZeros().toPlainString();
    }
    if (value instanceof String str) {
      String trimmed = str.trim();
      return trimmed.isEmpty() ? null : trimmed;
    }
    return value;
  }

  private static <T> Map<Object, T> toUniqueMap(Collection<T> list, Function<T, Object> idGetter) {
    Map<Object, T> result = new HashMap<>();
    for (T item : list) {
      Object key = idGetter.apply(item);
      result.putIfAbsent(key, item);
    }
    return result;
  }

  public record FieldChange(String fieldName, String oldData, String newData) {}
}
