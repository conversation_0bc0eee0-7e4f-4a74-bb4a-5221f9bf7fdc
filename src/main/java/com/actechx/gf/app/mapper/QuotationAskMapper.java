package com.actechx.gf.app.mapper;

import com.actechx.gf.adapter.client.request.QuotationAskAgentRequest;
import com.actechx.gf.adapter.controller.form.request.CreateQuotationAskDto;
import com.actechx.gf.adapter.controller.form.request.QuotationAskUpdateParts;
import com.actechx.gf.adapter.controller.form.response.DetailQuotationAskResponseDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskHistoryResponseDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskResponseDto;
import com.actechx.gf.adapter.controller.form.response.SearchQuotationAskResponseDto;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryEntity;
import com.actechx.gf.adapter.persistence.quotation.history.QuotationAskHistoryItemEntity;
import com.actechx.gf.domain.model.quotationask.*;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuotationAskMapper {

  @Mapping(target = "id", ignore = true)
  AskedVehicle toAskedVehicleDomain(CreateQuotationAskDto.AskedVehicleDto dto);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "owner", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  AskedAttachment toAttachmentDomain(CreateQuotationAskDto.AttachmentDto dto);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "code", ignore = true)
  @Mapping(target = "tenantId", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  AskedSparePart toSparePartDomain(CreateQuotationAskDto.SparePartDto dto);

  @Mapping(target = "askedVehicle", source = "askedVehicle")
  QuotationAskResponseDto toResponseDTO(QuotationAsk quotationAsk);

  @Mapping(target = "sparePartLineItems", ignore = true)
  DetailQuotationAskResponseDto.AskedSparePartDetailResponseDto toSparePartDetail(
      AskedSparePart askedSparePart);

  List<DetailQuotationAskResponseDto.AskedSparePartDetailResponseDto> toSparePartDtoList(
      List<AskedSparePart> askedSpareParts);

  DetailQuotationAskResponseDto toDetailResponseDTO(QuotationAsk quotationAsk);

  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "tenantOpsRegion", ignore = true)
  QuotationAskAgentRequest toAgentRequest(QuotationAsk dto);

  QuotationAskResponseDto.AskedVehicleResponseDto toAskedVehicleResponseDTO(
      AskedVehicle askedVehicle);

  QuotationAskResponseDto.AttachmentResponseDto toAttachmentResponseDTO(AskedAttachment attachment);

  QuotationAskResponseDto.SparePartResponseDto toSparePartResponseDTO(AskedSparePart sparePart);

  @Mapping(target = "quantityQuotationBid", ignore = true)
  @Mapping(target = "quantitySpareParts", ignore = true)
  @Mapping(target = "quantityQuotationProposal", ignore = true)
  SearchQuotationAskResponseDto toResponseDTO(QuotationAskEntity quotationAskEntity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "note", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  AskedAttachment toAskedAttachment(QuotationAskUpdateParts.Attachment askedAttachment);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "refCode", ignore = true)
  @Mapping(target = "tenantId", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  AskedSparePart toSparePart(QuotationAskUpdateParts.SparePart askedSparePart);

  @Mapping(target = "id", ignore = true)
  AskedVehicle toVehicle(QuotationAskUpdateParts.Vehicle askedVehicle);

  CreateQuotationAskDto.SparePartDto toSparePartDto(AskedSparePart askedSparePart);

  QuotationAskHistoryResponseDto toHistoryResponseDTO(QuotationAskHistoryEntity entity);

  @Mapping(target = "tier", ignore = true)
  @Mapping(target = "type", ignore = true)
  QuotationAskHistoryResponseDto.QuotationAskHistoryItemResponseDto toHistoryItemResponseDTO(
      QuotationAskHistoryItemEntity entity);

  List<DetailQuotationAskResponseDto.AddSparePartLineItemDetail> toAddSparePartLineItemDtoList(
      List<AddSparePartLineItemModel> addSparePartLineItemModels);

  @Mapping(target = "quotationAskId", ignore = true)
  AskedSparePartModel toAskedSparePartModel(AskedSparePartEntity askedSparePartEntity);
}
