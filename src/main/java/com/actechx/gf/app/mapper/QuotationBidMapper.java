package com.actechx.gf.app.mapper;

import com.actechx.gf.adapter.controller.form.request.CreateQuotationBidDto;
import com.actechx.gf.adapter.controller.form.response.QuotationBidResponseDto;
import com.actechx.gf.domain.model.quotationbid.AddedSparePartPriceLineItem;
import com.actechx.gf.domain.model.quotationbid.BiddedSparePart;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import com.actechx.gf.domain.model.quotationbid.SparePartPriceLineItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuotationBidMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  SparePartPriceLineItem toDomain(CreateQuotationBidDto.SparePartPriceLineItemsDto dto);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  AddedSparePartPriceLineItem toDomainAdded(CreateQuotationBidDto.SparePartPriceLineItemsDto dto);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  BiddedSparePart toDomain(CreateQuotationBidDto.BiddedSparePartsDto dto);

  @Mapping(target = "addedSparePartPriceLineItems", source = "addedSparePartPriceLineItems")
  @Mapping(target = "updatedInformation", ignore = true)
  QuotationBidResponseDto toResponse(QuotationBid quotationBid);

  QuotationBidResponseDto.SparePartPriceLineItemsDto toResponse(SparePartPriceLineItem domain);

  QuotationBidResponseDto.SparePartPriceLineItemsDto toResponse(AddedSparePartPriceLineItem domain);

  QuotationBidResponseDto.BiddedSparePartsDto toResponse(BiddedSparePart domain);
}
