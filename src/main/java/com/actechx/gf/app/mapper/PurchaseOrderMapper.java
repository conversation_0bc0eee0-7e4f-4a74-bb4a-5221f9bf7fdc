package com.actechx.gf.app.mapper;

import com.actechx.gf.adapter.client.request.PurchaseRequestAgentRequest;
import com.actechx.gf.adapter.controller.form.request.POProductRequestDTO;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.domain.model.purchaseorder.POProduct;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequestData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PurchaseOrderMapper {
  @Mapping(target = "poId", ignore = true)
  @Mapping(target = "id", ignore = true)
  POProduct toModel(POProductRequestDTO dto);

  @Mapping(target = "unitPrice", ignore = true)
  @Mapping(target = "unit", ignore = true)
  @Mapping(target = "segment", ignore = true)
  PurchaseRequestAgentRequest.PurchaseRequestDataDto toRequestData(PurchaseRequestData dto);

  PurchaseRequestAgentRequest.PurchaseOrderDto toRequestDto(PurchaseOrder purchaseOrder);

  PurchaseRequestAgentRequest.POProductDto toRequestDto(POProduct poProduct);

  @Mapping(target = "purchaseOrderCode", source = "code")
  PurchaseOrderDetailResponseDto.CommonInfo toCommonInfo(PurchaseOrder purchaseOrder);

  @Mapping(target = "saleOrderId", ignore = true)
  POProductRequestDTO toRequestDto(POProductEntity entity);

  @Mapping(target = "poProducts", ignore = true)
  @Mapping(target = "poQuotationRefs", ignore = true)
  @Mapping(target = "poSupplier", ignore = true)
  @Mapping(target = "domainEvents", ignore = true)
  PurchaseOrder toPurchaseOrder(PurchaseOrderEntity entity);
}
