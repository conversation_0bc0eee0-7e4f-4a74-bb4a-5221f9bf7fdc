package com.actechx.gf.app.mapper.purchase;

import com.actechx.gf.adapter.persistence.purchase.cart.CartEntity;
import com.actechx.gf.domain.model.purchaserequest.Cart;
import com.actechx.gf.domain.model.purchaserequest.CartAdd;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CartMapper {
  List<Cart> toCarts(List<CartEntity> cartEntities);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "isPicked", ignore = true)
  @Mapping(target = "refPrCode", ignore = true)
  @Mapping(target = "deleted", ignore = true)
  CartEntity toEntity(CartAdd cartAdd);

  Cart toCart(CartEntity cartEntity);
}
