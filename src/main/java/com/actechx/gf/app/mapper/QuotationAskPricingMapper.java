package com.actechx.gf.app.mapper;

import com.actechx.gf.adapter.client.request.QuotationAskPricingClientRequest;
import com.actechx.gf.adapter.controller.form.request.QuotationAskPricingProposalRequest;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingProposalResponseDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingResponseDto;
import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingProposalEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.AskedSparePartPricingRequestEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingRequestEntity;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposal;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposalResponseDomain;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingRequest;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuotationAskPricingMapper {
  @Mapping(target = "createdAt", ignore = true)
  QuotationAskPricingResponseDto toDto(QuotationAskPricingRequestEntity entity);

  @Mapping(target = "id", ignore = true)
  QuotationAskPricingRequestEntity toEntity(QuotationAskPricingRequest quotationAskPricingRequest);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "proposalStatus", ignore = true)
  @Mapping(target = "quotationAskPricing", ignore = true)
  AskedSparePartPricingRequestEntity toEntity(
      QuotationAskPricingRequest.AskedSparePartPricing entity);

  List<QuotationAskPricingClientRequest.AskedSparePartPricingClientRequest> toClientListRequest(
      List<QuotationAskPricingResponseDto.AskedSparePartPricingResponse> dto);

  QuotationAskPricingProposal toClientProposal(QuotationAskPricingProposalRequest proposalRequest);

  @Mapping(target = "id", ignore = true)
  QuotationAskPricingProposalEntity toPricingProposalEntity(QuotationAskPricingProposal proposal);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "updatedStatus", ignore = true)
  @Mapping(target = "quotationAskPricingProposal", ignore = true)
  AskedSparePartPricingProposalEntity toPricingProposalEntity(
      QuotationAskPricingProposal.AskedSparePartPricingProposal askedSparePartPricingProposal);

  @Mapping(target = "partNameInput", ignore = true)
  QuotationAskPricingProposalResponseDomain.AskedSparePartPricingProposalResponse
      toProposalResponsesDomain(
          AskedSparePartPricingProposalEntity askedSparePartPricingProposalEntity);

  QuotationAskPricingProposalResponseDto toProposalResponsesDto(
      QuotationAskPricingProposalResponseDomain responseDomain);
}
