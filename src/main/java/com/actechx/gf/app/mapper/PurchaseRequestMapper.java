package com.actechx.gf.app.mapper;

import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.domain.model.purchaserequest.PurchaseRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PurchaseRequestMapper {

  @Mapping(target = "purchaseRequestCode", source = "code")
  @Mapping(target = "purchaseRequestStatus", source = "status")
  @Mapping(target = "stage", ignore = true)
  @Mapping(target = "purchaseOrderCode", ignore = true)
  @Mapping(target = "prId", ignore = true)
  PurchaseOrderDetailResponseDto.CommonInfo toCommonInfo(PurchaseRequest request);
}
