package com.actechx.gf.app.service.purchase;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.BaseSearchRequest;
import com.actechx.common.exception.InvalidDataException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.client.AgentClient;
import com.actechx.gf.adapter.client.TenantClient;
import com.actechx.gf.adapter.client.response.TenantResponse;
import com.actechx.gf.adapter.controller.form.request.CreatePurchaseOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.POProductRequestDTO;
import com.actechx.gf.adapter.controller.form.request.UpdatePurchaseOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.ConfirmPurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.DeliveringPurchaseOrderRequest;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaserOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.ShipmentOrderClientRequest;
import com.actechx.gf.adapter.controller.form.response.PurchaseOrderResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.SearchPurchaseOrderResponseDto;
import com.actechx.gf.adapter.persistence.DBUtils;
import com.actechx.gf.adapter.persistence.mapper.PurchaseOrderEntityMapper;
import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.persistence.purchase.order.POSupplierEntity;
import com.actechx.gf.adapter.persistence.purchase.order.PurchaseOrderEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.adapter.repository.JpaPurchaseRequestDataEntityRepository;
import com.actechx.gf.adapter.repository.specification.PurchaseOrderSpecifications;
import com.actechx.gf.app.mapper.PurchaseOrderMapper;
import com.actechx.gf.app.service.CommonService;
import com.actechx.gf.domain.model.enums.*;
import com.actechx.gf.domain.model.purchaseorder.POProduct;
import com.actechx.gf.domain.model.purchaseorder.POQuotationRef;
import com.actechx.gf.domain.model.purchaseorder.POSupplier;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import com.actechx.gf.domain.repository.*;
import com.actechx.gf.domain.repository.purchase.PurchaseRequestRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseOrderApplicationService {
  private final DBUtils dbUtils;
  private final PurchaseOrderMapper purchaseOrderMapper;
  private final PurchaseOrderEntityMapper purchaseOrderEntityMapper;
  private final PurchaseOrderRepository purchaseOrderRepository;
  private final POProductRepository poProductRepository;
  private final POSupplierRepository poSupplierRepository;
  private final POQuotationRefRepository poQuotationRefRepository;
  private final PurchaseRequestRepository purchaseRequestRepository;
  private final JpaPurchaseRequestDataEntityRepository purchaseRequestDataEntityRepository;
  private final CommonService commonService;
  private final TenantClient tenantClient;
  private final AgentClient agentClient;

  public PurchaseOrder createPurchaseOrderApplication(
      CreatePurchaseOrderRequestDto request, String tenantCode, Long prId) {

    Long poId = dbUtils.getNextSequence("purchase_order_id");
    String code = String.format("ĐMH-%s-%d", tenantCode, poId);
    List<POProduct> poProducts =
        request.getPoProducts().stream()
            .map(
                product ->
                    new POProduct(
                        poId,
                        product.getProductId(),
                        product.getRequestedProductName(),
                        product.getQuantity(),
                        product.getUnitPrice(),
                        product.getUnit(),
                        product.getSegment(),
                        request.getSupplierId(),
                        product.getQuotationAskCode()))
            .toList();
    List<POQuotationRef> poQuotationRefs =
        Arrays.stream(StringUtils.split(request.getQuotationAskCode(), ","))
            .map(qaCode -> new POQuotationRef(poId, qaCode))
            .toList();
    POSupplier poSupplier =
        new POSupplier(poId, request.getSupplierId(), POSupplyStatus.SALES_ORDER_CONFIRM);
    PurchaseOrder purchaseOrder =
        PurchaseOrder.getPurchaseOrderAgg(
            poId,
            prId,
            code,
            null,
            request.getSource(),
            request.getTransportOrderId(),
            request.getPurchaserId(),
            request.getSupplierId(),
            request.getSupplierName(),
            request.getPaymentMethod(),
            request.getIsBestPrice(),
            request.getTransportRouteId(),
            request.getQuotationAskCode(),
            POStage.WAIT_TO_CONFIRM,
            POStatusEnum.WAIT_TO_CONFIRM,
            null,
            poProducts,
            poQuotationRefs,
            poSupplier);

    return purchaseOrderRepository.create(purchaseOrder);
  }

  @Transactional
  public PurchaseOrderResponseDto updatePurchaseOrderApplication(
      UpdatePurchaseOrderRequestDto request) {
    var entity =
        purchaseOrderRepository
            .findByPurchaseRequestCodeAndSupplierId(
                request.getPurchaseRequestCode(), request.getSupplierId())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "PurchaseOrder", "purchaseRequestCode", request.getPurchaseRequestCode()));
    var poId = entity.getId();
    var productEntities = poProductRepository.findByPOId(poId);
    List<Long> productIdExists =
        productEntities.stream().map(POProductEntity::getProductId).toList();
    var poQuotationRefEntities = poQuotationRefRepository.findByPOId(poId);
    var poSupplierEntity =
        poSupplierRepository
            .findByPOIdAndSupplierId(poId, entity.getSupplierId())
            .orElseThrow(() -> new ResourceNotFoundException("POSupplier", "poId", poId));
    var poSupplier = purchaseOrderEntityMapper.toModel(poSupplierEntity);
    List<POProduct> products = preValidateForUpdatePoProducts(request, entity, productIdExists);

    // Handle for domain
    PurchaseOrder purchaseOrder =
        PurchaseOrder.getPurchaseOrderAgg(
            entity.getId(),
            entity.getPrId(),
            entity.getCode(),
            entity.getSaleOrderCode(),
            entity.getSource(),
            entity.getTransportOrderId(),
            entity.getPurchaserId(),
            entity.getSupplierId(),
            entity.getSupplierName(),
            entity.getPaymentMethod(),
            entity.getIsBestPrice(),
            entity.getTransportRouteId(),
            entity.getQuotationAskCode(),
            entity.getStage(),
            entity.getStatus(),
            entity.getNote(),
            productEntities.stream().map(purchaseOrderEntityMapper::toModel).toList(),
            poQuotationRefEntities.stream().map(purchaseOrderEntityMapper::toModel).toList(),
            poSupplier);

    purchaseOrder.updateInfo(
        request.getTransportOrderId(),
        request.getPaymentMethod(),
        request.getIsBestPrice(),
        request.getStage(),
        request.getStatus(),
        request.getNote(),
        request.getCode(),
        products,
        request.getCreatedBy());

    // Handle for entity
    updatePOProductEntities(productEntities, purchaseOrder.getPoProducts());
    updatePOSupplier(poSupplierEntity, purchaseOrder.getPoSupplier());
    var savedPurchaseOrder = updatePurchaseOrderEntities(entity, purchaseOrder);
    updatePurchaseRequest(purchaseOrder.getPrId(), productEntities, purchaseOrder.getPurchaserId());
    return purchaseOrderEntityMapper.toResponseDto(savedPurchaseOrder);
  }

  @Transactional
  public void confirm(Long purchaseRequestId, ConfirmPurchaseRequestDto request) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    var productsNeedConfirm = request.getSparePartsRequest();
    Set<Long> confirmPoIds =
        productsNeedConfirm.stream()
            .filter(ConfirmPurchaseRequestDto.ConfirmSparePartRequestDto::isChecked)
            .map(ConfirmPurchaseRequestDto.ConfirmSparePartRequestDto::getPoId)
            .collect(Collectors.toSet());
    PurchaseRequestEntity purchaseRequest =
        purchaseRequestRepository
            .findEntityByIdAndTenantId(purchaseRequestId, purchaserId)
            .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest", purchaseRequestId));
    List<PurchaseOrderEntity> purchaseOrders =
        purchaseOrderRepository.findByPrIdAndTenantId(purchaseRequestId, purchaserId).stream()
            .filter(
                x ->
                    x.getStage().equals(POStage.WAIT_TO_CONFIRM)
                        && x.getStatus().equals(POStatusEnum.WAIT_GARAGE_TO_CONFIRM)
                        && confirmPoIds.contains(x.getId()))
            .toList();
    if (purchaseOrders.isEmpty()) {
      log.error("NO purchaseOrder need to confirm");
      throw new InvalidDataException("purchaseOrder", "NO purchaseOrder need to confirm");
    }
    List<PurchaseRequestDataEntity> purchaseRequestDataEntities =
        purchaseRequestDataEntityRepository.findByPurchaseRequestId(purchaseRequestId);

    List<PurchaseOrderEntity> savedPurchaseOrder = new ArrayList<>();
    List<POProductEntity> savedProducts = new ArrayList<>();
    for (var purchaseOrder : purchaseOrders) {
      purchaseOrder.confirm();
      var poProducts = poProductRepository.findByPOId(purchaseOrder.getId());
      for (var poProduct : poProducts) {
        boolean checked =
            productsNeedConfirm.stream()
                .filter(
                    p ->
                        p.getPoId().equals(poProduct.getPoId())
                            && p.getProductId().equals(poProduct.getProductId()))
                .findFirst()
                .map(ConfirmPurchaseRequestDto.ConfirmSparePartRequestDto::isChecked)
                .orElse(false);
        poProduct.confirm(checked);
        savedProducts.add(poProductRepository.store(poProduct));

        PurchaseRequestDataEntity purchaseRequestData =
            purchaseRequestDataEntities.stream()
                .filter(
                    data ->
                        data.getProductId().equals(poProduct.getProductId())
                            && data.getSupplierId().equals(poProduct.getSupplierId())
                            && data.getQuotationAskCode().equals(poProduct.getQuotationAskCode()))
                .findFirst()
                .orElse(null);
        if (purchaseRequestData != null) {
          purchaseRequestData.setSalesStatus(
              checked ? PurchaseRequestDataStatus.CONFIRMED : PurchaseRequestDataStatus.SOLD_OUT);
          purchaseRequestDataEntityRepository.save(purchaseRequestData);
        }
      }
      savedPurchaseOrder.add(purchaseOrderRepository.save(purchaseOrder));
    }
    log.info("Confirm purchaseRequest with response: {}", savedPurchaseOrder);
    callToConfirmCOP(purchaseRequest, savedPurchaseOrder, savedProducts);
  }

  @Transactional
  public void updatePurchaserOrderStageService(DeliveringPurchaseOrderRequest request) {
    if ("DELIVERING".equals(request.getStatus())) {
      var purchaseOrders =
          purchaseOrderRepository.findByPurchaseOrderCodeOrSaleOrderCode(
              request.getPurchaseOrderCodes(), request.getSaleOrderCodes());
      for (var purchaseOrder : purchaseOrders) {
        if (POStage.OPEN.equals(purchaseOrder.getStage())) {
          purchaseOrder.setStage(POStage.DELIVERING);
          purchaseOrderRepository.save(purchaseOrder);
        }
      }
    }
  }

  private void updatePurchaseRequest(
      Long purchaseRequestId, List<POProductEntity> productEntities, Long purchaserId) {
    purchaseRequestRepository
        .findEntityByIdAndTenantId(purchaseRequestId, purchaserId)
        .orElseThrow(
            () -> new ResourceNotFoundException("PurchaseRequest", "id", purchaseRequestId));
    var listPurchaseRequestData =
        purchaseRequestDataEntityRepository.findByPurchaseRequestId(purchaseRequestId);
    for (POProductEntity poProductEntity : productEntities) {
      var purchaseRequestData =
          listPurchaseRequestData.stream()
              .filter(
                  p ->
                      p.getProductId().equals(poProductEntity.getProductId())
                          && p.getSupplierId().equals(poProductEntity.getSupplierId()))
              .findFirst()
              .orElseThrow(
                  () ->
                      new ResourceNotFoundException(
                          "PurchaseRequestData", "productId", poProductEntity.getProductId()));
      purchaseRequestData.updateInfo(poProductEntity.getQuantity().intValue());
      purchaseRequestDataEntityRepository.save(purchaseRequestData);
    }
  }

  private List<POProduct> preValidateForUpdatePoProducts(
      UpdatePurchaseOrderRequestDto request,
      PurchaseOrderEntity entity,
      List<Long> productIdExists) {
    if (CollectionUtils.isEmpty(request.getSoProducts())) {
      log.error("updatePurchaseOrderApplication: soProducts is empty");
      throw new InvalidDataException("SOProducts", "SOProducts is empty");
    }
    if (request.getSoProducts().size() != productIdExists.size()) {
      log.error("updatePurchaseOrderApplication: soProducts size != poProductExists.size");
      throw new InvalidDataException("SOProducts", "SOProducts size != poProductExists");
    }
    return request.getSoProducts().stream()
        .map(
            m -> {
              Long productId =
                  commonService.getProductByIdClient(
                      m.getRequestedProductName(), m.getSegment(), entity.getPurchaserId());
              if (!productIdExists.contains(productId)) {
                log.error(
                    "updatePurchaseOrderApplication: product id not found with id:{}, name: {}",
                    m.getProductId(),
                    m.getRequestedProductName());
                throw new InvalidDataException("product", "product id not found");
              }
              var model = purchaseOrderMapper.toModel(m);
              model.setPoId(entity.getId());
              model.setProductId(productId);
              return model;
            })
        .toList();
  }

  private void updatePOProductEntities(List<POProductEntity> entities, List<POProduct> models) {
    for (var entity : entities) {
      var poProduct =
          models.stream().filter(x -> x.getProductId().equals(entity.getProductId())).findFirst();
      if (poProduct.isEmpty()) {
        throw new ResourceNotFoundException("POProduct", entity.getProductId());
      } else {
        entity.updateInfo(
            poProduct.get().getQuantity(),
            poProduct.get().getUnitPrice(),
            poProduct.get().getUnit());
      }
      poProductRepository.store(entity);
    }
  }

  private void updatePOSupplier(POSupplierEntity entity, POSupplier model) {
    if (!entity.getStatus().equals(model.getStatus())) {
      entity.updateStatus(model.getStatus());
      poSupplierRepository.store(entity);
    }
  }

  private PurchaseOrderEntity updatePurchaseOrderEntities(
      PurchaseOrderEntity entity, PurchaseOrder purchaseOrder) {
    entity.updateInfo(
        purchaseOrder.getTransportOrderId(),
        purchaseOrder.getTransportRouteId(),
        purchaseOrder.getPaymentMethod(),
        purchaseOrder.getIsBestPrice(),
        purchaseOrder.getStage(),
        purchaseOrder.getStatus(),
        purchaseOrder.getNote(),
        purchaseOrder.getSaleOrderCode());
    return purchaseOrderRepository.save(entity);
  }

  public PurchaseOrderDetailResponseDto getDetailByIdService(Long id) {
    Long tenantId = 2L;
    PurchaseOrderEntity purchaseOrderEntity =
        purchaseOrderRepository
            .findByIdAndTenantId(id, tenantId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "Purchase Order not found with purchaseId: ", id));
    return this.detailPurchaseOrder(purchaseOrderEntity);
  }

  public PurchaseOrderDetailResponseDto getDetailByCodeService(String code) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    PurchaseOrderEntity purchaseOrderEntity =
        purchaseOrderRepository
            .findByCodeAndTenantId(code, tenantId)
            .orElseThrow(
                () -> new ResourceNotFoundException("Purchase Order not found with code: ", code));
    return this.detailPurchaseOrder(purchaseOrderEntity);
  }

  private PurchaseOrderDetailResponseDto detailPurchaseOrder(
      PurchaseOrderEntity purchaseOrderEntity) {
    PurchaseOrder purchaseOrder = purchaseOrderMapper.toPurchaseOrder(purchaseOrderEntity);

    List<POProduct> poProducts =
        poProductRepository.findByPOId(purchaseOrder.getId()).stream()
            .map(purchaseOrderEntityMapper::toModel)
            .collect(Collectors.toList());

    PurchaseOrderDetailResponseDto purchaseOrderDetailResponseDto =
        new PurchaseOrderDetailResponseDto();

    // Common Info
    purchaseOrderDetailResponseDto.setCommonInfo(processCommonInfo(purchaseOrder));

    // Personal Info
    purchaseOrderDetailResponseDto.setPersonalInfo(processPersonalInfo());

    // SparePartInfo
    purchaseOrderDetailResponseDto.setSparePartInfoConfirmed(processSparePartInfo(poProducts));

    // Payment Info
    // purchaseOrderDetailResponseDto.setPaymentInfo(processPaymentInfo());
    return purchaseOrderDetailResponseDto;
  }

  public Page<SearchPurchaseOrderResponseDto> searchService(
      SearchPurchaserOrderRequestDto request) {
    PageRequest pageable = BaseSearchRequest.buildPageRequest(request);
    Specification<PurchaseOrderEntity> spec = PurchaseOrderSpecifications.withFilters(request);
    Page<PurchaseOrderEntity> pageResult = purchaseOrderRepository.searchData(spec, pageable);
    return pageResult.map(this::toPurchaserOrderDataDto);
  }

  private List<PurchaseOrderDetailResponseDto.SparePartInfo> processSparePartInfo(
      List<POProduct> poProducts) {
    if (poProducts == null || poProducts.isEmpty()) {
      return List.of();
    }
    Set<Long> tenantIds =
        poProducts.stream().map(POProduct::getSupplierId).collect(Collectors.toSet());

    List<PurchaseOrderDetailResponseDto.SparePartInfo> sparePartInfos = new ArrayList<>();
    for (Long tenantId : tenantIds) {
      String tenantName = tenantClient.getTenantInfo(tenantId).getData().getTenantName();
      List<PurchaseOrderDetailResponseDto.SparePartInfoConfirmed> sparePartInfoConfirmeds =
          processSparePartInfoConfirmed(poProducts);

      // Total price each vendor
      BigDecimal totalPrice =
          sparePartInfoConfirmeds.stream()
              .map(PurchaseOrderDetailResponseDto.SparePartInfoConfirmed::getPrice)
              .reduce(BigDecimal.ZERO, BigDecimal::add);

      PurchaseOrderDetailResponseDto.SparePartInfo sparePartInfo =
          PurchaseOrderDetailResponseDto.SparePartInfo.builder()
              .vendorName(tenantName)
              .totalPrice(totalPrice)
              .sparePartInfoConfirmed(sparePartInfoConfirmeds)
              .build();

      sparePartInfos.add(sparePartInfo);

      return sparePartInfos;
    }
    return sparePartInfos;
  }

  private List<PurchaseOrderDetailResponseDto.SparePartInfoConfirmed> processSparePartInfoConfirmed(
      List<POProduct> poProducts) {
    return poProducts.stream()
        .map(
            item -> {
              BigDecimal totalPrice =
                  item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity()));

              return PurchaseOrderDetailResponseDto.SparePartInfoConfirmed.builder()
                  .requestProductName(item.getRequestedProductName())
                  .poId(item.getPoId())
                  .productId(item.getProductId())
                  .supplierId(item.getSupplierId())
                  .quantity(item.getQuantity() == null ? 0 : item.getQuantity())
                  .price(totalPrice)
                  .purchaseRequestStatus(null)
                  .segment(item.getSegment())
                  .unit(item.getUnit())
                  .build();
            })
        .toList();
  }

  private PurchaseOrderDetailResponseDto.PersonalInfo processPersonalInfo() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    TenantResponse tenantResponse = tenantClient.getTenantInfo(tenantId).getData();
    return PurchaseOrderDetailResponseDto.PersonalInfo.builder()
        .name(tenantResponse.getTenantName())
        .address(tenantResponse.getAddress())
        .phoneNumber(tenantResponse.getTenantPhoneNumber())
        .build();
  }

  private PurchaseOrderDetailResponseDto.CommonInfo processCommonInfo(PurchaseOrder purchaseOrder) {
    if (purchaseOrder == null) {
      return null;
    }
    return purchaseOrderMapper.toCommonInfo(purchaseOrder);
  }

  private void callToConfirmCOP(
      PurchaseRequestEntity purchaseRequest,
      List<PurchaseOrderEntity> purchaseOrders,
      List<POProductEntity> poProducts) {
    List<UpdatePurchaseOrderRequestDto> requestConfirm = new ArrayList<>();
    for (var purchaseOrder : purchaseOrders) {
      var soProducts =
          poProducts.stream()
              .filter(p -> p.getPoId().equals(purchaseOrder.getId()))
              .map(purchaseOrderMapper::toRequestDto)
              .toList();
      UpdatePurchaseOrderRequestDto request =
          getUpdatePurchaseOrderRequestDto(purchaseRequest, purchaseOrder, soProducts);
      requestConfirm.add(request);
    }
    log.info("Call Agent confirm purchaseRequest with request: {}", requestConfirm);
    ApiResponse<String> agentClientAgent = agentClient.confirmPurchaseRequest(requestConfirm);
    log.info("Call Agent confirm purchaseRequest success: {}", agentClientAgent.getData());
  }

  private static UpdatePurchaseOrderRequestDto getUpdatePurchaseOrderRequestDto(
      PurchaseRequestEntity purchaseRequest,
      PurchaseOrderEntity purchaseOrder,
      List<POProductRequestDTO> soProducts) {
    UpdatePurchaseOrderRequestDto request = new UpdatePurchaseOrderRequestDto();
    request.setId(purchaseOrder.getId());
    request.setPoId(purchaseOrder.getId());
    request.setCode(purchaseOrder.getCode());
    request.setStage(purchaseOrder.getStage());
    request.setStatus(purchaseOrder.getStatus());
    request.setPurchaseRequestId(purchaseRequest.getId());
    request.setPurchaseRequestCode(purchaseRequest.getCode());
    request.setSupplierId(purchaseOrder.getSupplierId());
    request.setPurchaserId(purchaseOrder.getPurchaserId());
    request.setQuotationAskCode(purchaseOrder.getQuotationAskCode());
    request.setSoProducts(soProducts);
    return request;
  }

  private SearchPurchaseOrderResponseDto toPurchaserOrderDataDto(PurchaseOrderEntity entity) {
    SearchPurchaseOrderResponseDto responseDto = new SearchPurchaseOrderResponseDto();
    responseDto.setId(entity.getId());
    responseDto.setCode(entity.getCode());
    responseDto.setQuotationAskCode(entity.getQuotationAskCode());
    responseDto.setStage(entity.getStage());
    responseDto.setCreatedAt(entity.getCreatedAt());
    List<POProductEntity> productEntities = poProductRepository.findByPOId(entity.getId());
    List<SearchPurchaseOrderResponseDto.SearchPurchaseOrderDataResponseDto> purchaserOrderList =
        new ArrayList<>();
    productEntities.forEach(
        x -> {
          SearchPurchaseOrderResponseDto.SearchPurchaseOrderDataResponseDto
              purchaserRequestDataResponseDto =
                  new SearchPurchaseOrderResponseDto.SearchPurchaseOrderDataResponseDto();
          purchaserRequestDataResponseDto.setId(x.getId());
          purchaserRequestDataResponseDto.setRequestedProductName(x.getRequestedProductName());
          purchaserOrderList.add(purchaserRequestDataResponseDto);
        });
    responseDto.setPurchaserOrderList(purchaserOrderList);
    return responseDto;
  }

  public void confirmGoodsReceivedByIdService(Long purchaseOrderId) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    PurchaseOrderEntity purchaseOrderEntity =
        purchaseOrderRepository
            .findByIdAndTenantId(purchaseOrderId, purchaserId)
            .orElseThrow(
                () -> new ResourceNotFoundException("PurchaseOrder get by id", purchaseOrderId));
    this.confirmGoodsReceived(purchaseOrderEntity);
  }

  public void confirmGoodsReceivedByCodeService(String code) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    PurchaseOrderEntity purchaseOrderEntity =
        purchaseOrderRepository
            .findByCodeAndTenantId(code, purchaserId)
            .orElseThrow(() -> new ResourceNotFoundException("PurchaseOrder get by code", code));
    this.confirmGoodsReceived(purchaseOrderEntity);
  }

  private void confirmGoodsReceived(PurchaseOrderEntity purchaseOrderEntity) {
    purchaseOrderEntity.confirmReceived();
    purchaseOrderRepository.save(purchaseOrderEntity);
    callAgentToConfirmReceived(purchaseOrderEntity.getCode());
  }

  private void callAgentToConfirmReceived(String purchaseOrderCode) {
    ShipmentOrderClientRequest shipmentOrderClientRequest = new ShipmentOrderClientRequest();
    shipmentOrderClientRequest.setType(ShipmentOrderType.DELIVERY);
    shipmentOrderClientRequest.setStatus(POStage.DELIVERED);
    shipmentOrderClientRequest.setCodes(List.of(purchaseOrderCode));
    log.info(
        "Call Agent confirm goods received purchaseOrder with request: {}",
        shipmentOrderClientRequest);
    var agentClientAgent = agentClient.confirmReceivedPurchaseRequest(shipmentOrderClientRequest);
    log.info(
        "Call Agent confirm goods received purchaseOrder with response: {}",
        agentClientAgent.getData());
  }
}
