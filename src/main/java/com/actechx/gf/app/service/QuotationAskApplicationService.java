package com.actechx.gf.app.service;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.BaseSearchRequest;
import com.actechx.common.enums.BusinessMessage;
import com.actechx.common.exception.BusinessException;
import com.actechx.common.exception.DuplicateResourceException;
import com.actechx.common.exception.InvalidDataException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.client.AgentClient;
import com.actechx.gf.adapter.client.CmsClient;
import com.actechx.gf.adapter.client.TenantClient;
import com.actechx.gf.adapter.client.request.QuotationAskAgentRequest;
import com.actechx.gf.adapter.client.response.CmsCatalogContentResponse;
import com.actechx.gf.adapter.client.response.TenantResponse;
import com.actechx.gf.adapter.controller.form.request.*;
import com.actechx.gf.adapter.controller.form.response.*;
import com.actechx.gf.adapter.persistence.DBUtils;
import com.actechx.gf.adapter.persistence.quotation.ask.QuotationAskEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.QuotationBidEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.repository.specification.QuotationAskSpecification;
import com.actechx.gf.app.mapper.QuotationAskMapper;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.app.utils.QuotationUtil;
import com.actechx.gf.domain.model.common.ProductModel;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.enums.Segment;
import com.actechx.gf.domain.model.quotationask.*;
import com.actechx.gf.domain.repository.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class QuotationAskApplicationService {
  private final QuotationAskRepository quotationAskRepository;
  private final QuotationBidRepository quotationBidRepository;
  private final AskedSparePartRepository askedSparePartRepository;
  private final QuotationAskHistoryRepository quotationAskHistoryRepository;
  private final QuotationAskMapper quotationAskMapper;
  private final DBUtils dbUtils;
  private final TenantClient tenantClient;
  private final AgentClient agentClient;
  private final CmsClient cmsClient;
  private final CommonService commonService;

  @Transactional
  public QuotationAskResponseDto createQuotationAskService(CreateQuotationAskDto request) {

    AskedVehicle askedVehicle = quotationAskMapper.toAskedVehicleDomain(request.getAskedVehicle());

    List<AskedAttachment> attachments =
        request.getAttachments().stream().map(quotationAskMapper::toAttachmentDomain).toList();

    List<AskedSparePart> spareParts =
        request.getSpareParts().stream().map(quotationAskMapper::toSparePartDomain).toList();

    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    this.validateDataRequest(request, tenantId);
    ApiResponse<TenantResponse> responseApiResponse = tenantClient.getTenantInfo(tenantId);
    TenantResponse tenantInfo = responseApiResponse.getData();

    String tenantOpsArea = tenantInfo.getOpsArea();
    String tenantOpsRegion = null;
    if (StringUtils.isNotBlank(tenantOpsArea)) {
      ApiResponse<CmsCatalogContentResponse> cmsCatalogContentResponseApiResponse =
          cmsClient.getCatalogContentClient(tenantOpsArea);
      tenantOpsRegion = cmsCatalogContentResponseApiResponse.getData().getCode();
    }
    Long codeSequence = dbUtils.getNextSequence("quotation_ask_code");
    String code = String.format("BG-%s-%d", tenantInfo.getCode(), codeSequence);
    Long id = dbUtils.getNextSequence("quotation_ask_id");
    QuotationAsk quotationAsk =
        new QuotationAsk(
            id,
            code,
            tenantId,
            tenantInfo.getTenantName(),
            tenantInfo.getTenantPhoneNumber(),
            tenantInfo.getOpsArea(),
            tenantInfo.getAddress(),
            tenantOpsRegion,
            request.getAskNote(),
            askedVehicle,
            attachments,
            spareParts);

    // TODO: xử lý sau
    quotationAsk.setStatus(QuotationStatus.ASKING);

    // Save to repository
    QuotationAsk savedQuotationAsk =
        quotationAskRepository.save(quotationAsk, tenantInfo.getTenantType().name());

    // call api agent create ycbg
    QuotationAskAgentRequest agentRequest = quotationAskMapper.toAgentRequest(savedQuotationAsk);
    agentRequest.setId(savedQuotationAsk.getId());
    agentRequest.setCreatedAt(
        DateTimeUtils.convertInstantToLocalDateTime(savedQuotationAsk.getCreatedAt()));
    agentRequest.setTenantOpsRegion(savedQuotationAsk.getTenantOpsRegion());
    ApiResponse<String> agentClientAgent = agentClient.createQuotationAskAgent(agentRequest);
    log.info("Call Agent success: {}", agentClientAgent.getData());

    //     create api product inventory
    this.createProductInventory(quotationAsk.getSpareParts(), tenantId);
    return quotationAskMapper.toResponseDTO(savedQuotationAsk);
  }

  @Transactional
  public QuotationAskResponseDto updateQuotationAsk(UpdateQuotationAskDto request) {
    log.info("PUT update QuotationAsk, UpdateQuotationAskRequest is {}", request);
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByCodeAndTenantId(request.getQuotationAskCode(), request.getOriginTenantId())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationAsk", "code", request.getQuotationAskCode()));
    if (!QuotationStatus.ASKING.equals(quotationAsk.getStatus())) {
      log.error(
          "Update QuotationAsk status is not ASKING. CurrentStatus: {}", quotationAsk.getStatus());
      throw new InvalidDataException("status", quotationAsk.getStatus(), "Must be ASKING");
    }
    QuotationAskUpdateParts quotationAskUpdateParts =
        JsonUtils.toObject(request.getQuotationAskUpdateParts(), QuotationAskUpdateParts.class);

    deleteParts(quotationAsk, quotationAskUpdateParts.getDeleteParts());
    addParts(quotationAsk, quotationAskUpdateParts.getAddParts());
    updateParts(quotationAsk, quotationAskUpdateParts.getUpdateParts());
    quotationAsk.setUpdatedAt(Instant.now());

    validateNoDuplicateParts(
        quotationAsk.getSpareParts().stream().map(quotationAskMapper::toSparePartDto).toList());
    QuotationAsk savedQuotationAsk = quotationAskRepository.store(quotationAsk, false);
    return quotationAskMapper.toResponseDTO(savedQuotationAsk);
  }

  @Transactional(readOnly = true)
  public DetailQuotationAskResponseDto getByIdService(Long id) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByIdAndTenantId(id, tenantId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationAsk get id, tenantId", id + " - " + tenantId));
    return this.getDetailService(quotationAsk);
  }

  @Transactional(readOnly = true)
  public DetailQuotationAskResponseDto getByCodeService(String code) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByCodeAndTenantId(code, tenantId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationAsk get code, tenantId", code + " - " + tenantId));
    return this.getDetailService(quotationAsk);
  }

  @Transactional(readOnly = true)
  public Page<SearchQuotationAskResponseDto> search(SearchQuotationAskRequestDto searchRequest) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    searchRequest.setTenantId(tenantId);

    Specification<QuotationAskEntity> spec = QuotationAskSpecification.withFilters(searchRequest);
    PageRequest pageable = BaseSearchRequest.buildPageRequest(searchRequest);

    Page<QuotationAskEntity> quotationAsks = quotationAskRepository.findAll(spec, pageable);

    // Enrich response
    List<String> quotationAskCodes =
        quotationAsks.stream().map(QuotationAskEntity::getCode).toList();
    Map<String, Integer> quotationBidCountMap =
        quotationBidRepository.getBidCountByQuotationAskCode(quotationAskCodes);
    Map<String, Integer> quotationBidCountStatusMap =
        quotationBidRepository.getBidCountByQuotationAskCodeAndStatus(
            quotationAskCodes, QuotationBidStatus.PRICED);

    return quotationAsks.map(
        ask -> {
          SearchQuotationAskResponseDto responseDto = quotationAskMapper.toResponseDTO(ask);
          responseDto.setQuantityQuotationBid(quotationBidCountMap.getOrDefault(ask.getCode(), 0));
          responseDto.setQuantityQuotationProposal(
              quotationBidCountStatusMap.getOrDefault(ask.getCode(), 0));
          responseDto.setQuantitySpareParts(ask.getSpareParts().size());
          return responseDto;
        });
  }

  @Transactional(readOnly = true)
  public Page<SearchSparePartResponseDto> searchSparePart(SearchSparePartRequestDto searchRequest) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    searchRequest.setTenantId(tenantId);
    return askedSparePartRepository.findDistinctPartNameUnit(searchRequest);
  }

  private void validateDataRequest(CreateQuotationAskDto request, Long tenantId) {
    if (Objects.isNull(tenantId)) {
      throw new BusinessException(BusinessMessage.IAM_037, "Token invalid: tenantId is null");
    }
    int imageNumber = request.getAttachments().size();
    if (imageNumber > 25) {
      // AC-7: số lượng vượt quá 25 ảnh
      log.error("TenantId {}: Image number invalid: {}", tenantId, imageNumber);
      throw new BusinessException(BusinessMessage.IAM_064, "Image number invalid: " + imageNumber);
    }
    isAllDigitsOrAllLetters(request.getAskedVehicle().getVin(), tenantId);
    validateNoDuplicateParts(request.getSpareParts());
  }

  @Transactional
  public void cancelById(Long id) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    var quotationAsk =
        quotationAskRepository
            .findByIdAndTenantId(id, tenantId)
            .orElseThrow(() -> new ResourceNotFoundException("QuotationAsk", id));
    if (!List.of(
            QuotationStatus.OPEN,
            QuotationStatus.ASKING,
            QuotationStatus.BIDDING,
            QuotationStatus.PRICING)
        .contains(quotationAsk.getStatus())) {
      throw new InvalidDataException(
          "Status", quotationAsk.getStatus().toString(), "Status is not valid");
    }
    String updatedBy = SecurityUtils.getCurrentUserId();
    quotationAsk.updateStatus(QuotationStatus.CANCELLED, updatedBy);
    quotationAskRepository.store(quotationAsk, true);

    // TODO: call api agent to cancel quotationAsk
  }

  @Transactional(readOnly = true)
  public Page<QuotationAskHistoryResponseDto> getHistory(
      String quotationAskCode, SearchQuotationAskHistoryRequestDto searchRequest) {
    return quotationAskHistoryRepository
        .getHistories(quotationAskCode, searchRequest)
        .map(
            entity -> {
              var resposne = quotationAskMapper.toHistoryResponseDTO(entity);
              for (var item : resposne.getItems()) {
                if (StringUtils.isNotBlank(item.getFieldName())) {
                  for (var segment : Segment.values()) {
                    if (item.getFieldName().contains(segment.name())) {
                      item.setTier(segment.name());
                      break;
                    }
                  }
                }
                if (StringUtils.isBlank(item.getOldData())) {
                  item.setType("create");
                  item.setNewData(null);
                } else if (StringUtils.isBlank(item.getNewData())) {
                  item.setType("delete");
                  item.setOldData(null);
                } else {
                  item.setType("update");
                }
              }
              return resposne;
            });
  }

  private void validateNoDuplicateParts(List<CreateQuotationAskDto.SparePartDto> partList) {
    Set<String> uniqueKeys = new HashSet<>();
    for (CreateQuotationAskDto.SparePartDto dto : partList) {
      String key = dto.getPartNameInput() + "|" + dto.getPartNameUnit();
      if (!uniqueKeys.add(key)) {
        throw new BusinessException(
            BusinessMessage.IAM_037, "Duplicate partNameInput and partNameUnit found: " + key);
      }
    }
  }

  private void isAllDigitsOrAllLetters(String vin, Long tenantId) {
    if (StringUtils.isNotBlank(vin)) {
      if (vin.matches("\\d+")) {
        log.error("TenantId {}: Vin is invalid - all digits", tenantId);
        throw new BusinessException(BusinessMessage.IAM_038, "Vin is invalid - all digits");
      }
      if (vin.matches("[a-zA-Z]+")) {
        log.error("tenantId {}: Vin is invalid - all letters", tenantId);
        throw new BusinessException(BusinessMessage.IAM_038, "Vin is invalid - all letters");
      }
    }
  }

  private void deleteParts(
      QuotationAsk quotationAsk, QuotationAskUpdateParts.AddOrDeleteParts deleteParts) {
    if (deleteParts == null) return;
    if (!CollectionUtils.isEmpty(deleteParts.getAttachments())) {
      for (var deleteAttachment : deleteParts.getAttachments()) {
        AskedAttachment existAttachment =
            quotationAsk.getAttachments().stream()
                .filter(
                    x -> x.getAttachmentUrl().equalsIgnoreCase(deleteAttachment.getAttachmentUrl()))
                .findFirst()
                .orElse(null);
        quotationAsk.deleteAttachment(existAttachment);
      }
    }

    if (!CollectionUtils.isEmpty(deleteParts.getSpareParts())) {
      for (var deleteSparePart : deleteParts.getSpareParts()) {
        AskedSparePart existSparePart =
            quotationAsk.getSpareParts().stream()
                .filter(x -> x.getCode().equalsIgnoreCase(deleteSparePart.getCode()))
                .findFirst()
                .orElse(null);
        quotationAsk.deleteSpartPart(existSparePart);
      }
    }
  }

  private void addParts(
      QuotationAsk quotationAsk, QuotationAskUpdateParts.AddOrDeleteParts addParts) {
    if (addParts == null) return;
    if (!CollectionUtils.isEmpty(addParts.getAttachments())) {
      for (var addAttachment : addParts.getAttachments()) {
        AskedAttachment askedAttachment = quotationAskMapper.toAskedAttachment(addAttachment);
        quotationAsk.addAttachment(askedAttachment);
      }
    }

    if (!CollectionUtils.isEmpty(addParts.getSpareParts())) {
      for (var addSparePart : addParts.getSpareParts()) {
        boolean existSparePart =
            quotationAsk.getSpareParts().stream()
                .anyMatch(x -> x.getCode().equalsIgnoreCase(addSparePart.getCode()));
        if (existSparePart) {
          throw new DuplicateResourceException("SparePart_add", "code", addSparePart.getCode());
        }
        AskedSparePart askedSparePart = quotationAskMapper.toSparePart(addSparePart);
        if (askedSparePart.getTenantId() == null) {
          askedSparePart.setTenantId(quotationAsk.getTenantId());
        }
        quotationAsk.addSpartPart(askedSparePart);
      }
      createProductInventory(
          addParts.getSpareParts().stream().map(quotationAskMapper::toSparePart).toList(),
          quotationAsk.getTenantId());
    }
  }

  private void updateParts(
      QuotationAsk quotationAsk, QuotationAskUpdateParts.UpdateParts updateParts) {
    if (updateParts == null) return;

    if (!CollectionUtils.isEmpty(updateParts.getSpareParts())) {
      for (var updateSparePart : updateParts.getSpareParts()) {
        quotationAsk.getSpareParts().stream()
            .filter(x -> x.getCode().equalsIgnoreCase(updateSparePart.getCode()))
            .findFirst()
            .orElseThrow(
                () -> new ResourceNotFoundException("SparePart_update", updateSparePart.getCode()));
        AskedSparePart askedSparePart = quotationAskMapper.toSparePart(updateSparePart);
        if (askedSparePart.getTenantId() == null) {
          askedSparePart.setTenantId(quotationAsk.getTenantId());
        }
        quotationAsk.updateSpartPart(askedSparePart);
      }
      createProductInventory(
          updateParts.getSpareParts().stream().map(quotationAskMapper::toSparePart).toList(),
          quotationAsk.getTenantId());
    }

    if (updateParts.getVehicle() != null) {
      AskedVehicle askedVehicle = quotationAskMapper.toVehicle(updateParts.getVehicle());
      quotationAsk.updateVehicle(askedVehicle);
    }
  }

  private List<DetailQuotationAskResponseDto.SparePartLineItemDetail> setQuotationBid(
      String quotationAskCode, QuotationStatus status, String sparePartCode) {
    List<QuotationBidEntity> quotationBidEntities =
        quotationBidRepository.findByQuotationAskCode(quotationAskCode);
    List<DetailQuotationAskResponseDto.SparePartLineItemDetail> sparePartLineItems =
        new ArrayList<>();

    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItemTier1 =
        this.initialSparePartLineItem(Segment.TIER1);
    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItemTier2 =
        this.initialSparePartLineItem(Segment.TIER2);
    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItemTier3 =
        this.initialSparePartLineItem(Segment.TIER3);
    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItemTier4 =
        this.initialSparePartLineItem(Segment.TIER4);
    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItemTier5 =
        this.initialSparePartLineItem(Segment.TIER5);

    quotationBidEntities.forEach(
        quotationBidEntity -> {
          List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities =
              quotationBidEntity.getSparePartPriceLineItems().stream()
                  .filter(a -> sparePartCode.equals(a.getSparePartInputCode()))
                  .toList();

          sparePartPriceLineItemEntities.forEach(
              sparePartPriceLineItemEntity -> {
                switch (sparePartPriceLineItemEntity.getSegment()) {
                  case Segment.TIER1 ->
                      setSparePartLineItemSegmentWithSegment(
                          sparePartLineItemTier1, sparePartPriceLineItemEntity);
                  case Segment.TIER2 ->
                      setSparePartLineItemSegmentWithSegment(
                          sparePartLineItemTier2, sparePartPriceLineItemEntity);
                  case Segment.TIER3 ->
                      setSparePartLineItemSegmentWithSegment(
                          sparePartLineItemTier3, sparePartPriceLineItemEntity);
                  case Segment.TIER4 ->
                      setSparePartLineItemSegmentWithSegment(
                          sparePartLineItemTier4, sparePartPriceLineItemEntity);
                  case Segment.TIER5 ->
                      setSparePartLineItemSegmentWithSegment(
                          sparePartLineItemTier5, sparePartPriceLineItemEntity);
                  default ->
                      throw new ResourceNotFoundException(
                          "Segment Invalid: ", sparePartPriceLineItemEntity.getSegment());
                }
              });
        });
    this.setTotalPriceAndSort(sparePartLineItemTier1, status);
    this.setTotalPriceAndSort(sparePartLineItemTier2, status);
    this.setTotalPriceAndSort(sparePartLineItemTier3, status);
    this.setTotalPriceAndSort(sparePartLineItemTier4, status);
    this.setTotalPriceAndSort(sparePartLineItemTier5, status);
    sparePartLineItems.add(sparePartLineItemTier1);
    sparePartLineItems.add(sparePartLineItemTier2);
    sparePartLineItems.add(sparePartLineItemTier3);
    sparePartLineItems.add(sparePartLineItemTier4);
    sparePartLineItems.add(sparePartLineItemTier5);
    return sparePartLineItems;
  }

  // phụ tùng chính
  private void setSparePartLineItemSegmentWithSegment(
      DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItem,
      SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail sparePartLineItemSegment =
        this.setSparePartPriceLineItem(sparePartPriceLineItemEntity);
    List<AddSparePartLineItemModel> addSparePartLineItemModels =
        QuotationUtil.getAddSparePartPriceLineItems(sparePartPriceLineItemEntity);
    List<DetailQuotationAskResponseDto.AddSparePartLineItemDetail> addSparePartLineItems =
        quotationAskMapper.toAddSparePartLineItemDtoList(addSparePartLineItemModels);
    sparePartLineItemSegment.setAddSparePartLineItems(addSparePartLineItems);
    sparePartLineItem.getSparePartLineItemSegments().add(sparePartLineItemSegment);
  }

  private DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail setSparePartPriceLineItem(
      SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail sparePartLineItemSegment =
        new DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail();
    sparePartLineItemSegment.setId(sparePartPriceLineItemEntity.getId());
    sparePartLineItemSegment.setTenantId(
        sparePartPriceLineItemEntity.getQuotationBid().getTenantId());
    sparePartLineItemSegment.setSparePartInputCode(
        sparePartPriceLineItemEntity.getSparePartInputCode());
    sparePartLineItemSegment.setSegment(sparePartPriceLineItemEntity.getSegment());
    sparePartLineItemSegment.setPrice(sparePartPriceLineItemEntity.getPrice());
    sparePartLineItemSegment.setCurrency(sparePartPriceLineItemEntity.getCurrency());
    sparePartLineItemSegment.setNote(sparePartPriceLineItemEntity.getNote());
    sparePartLineItemSegment.setUnit(sparePartPriceLineItemEntity.getUnit());
    sparePartLineItemSegment.setDetailStatus(sparePartPriceLineItemEntity.getDetailStatus());
    sparePartLineItemSegment.setReceiveDetailStatus(
        sparePartPriceLineItemEntity.getReceiveDetailStatus());
    sparePartLineItemSegment.setQuantity(sparePartPriceLineItemEntity.getQuantity());
    sparePartLineItemSegment.setMaterialPrice(sparePartPriceLineItemEntity.getMaterialPrice());
    sparePartLineItemSegment.setServicingPrice(sparePartPriceLineItemEntity.getServicingPrice());
    sparePartLineItemSegment.setStatus(sparePartPriceLineItemEntity.getQuotationBid().getStatus());
    sparePartLineItemSegment.setUpdatedAt(sparePartPriceLineItemEntity.getUpdatedAt());
    return sparePartLineItemSegment;
  }

  private DetailQuotationAskResponseDto.SparePartLineItemDetail initialSparePartLineItem(
      Segment segment) {
    DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItem =
        new DetailQuotationAskResponseDto.SparePartLineItemDetail();
    List<DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail> sparePartLineItemSegments =
        new ArrayList<>();
    sparePartLineItem.setSegment(segment);
    sparePartLineItem.setSparePartLineItemSegments(sparePartLineItemSegments);
    return sparePartLineItem;
  }

  private void setTotalPriceAndSort(
      DetailQuotationAskResponseDto.SparePartLineItemDetail sparePartLineItem,
      QuotationStatus status) {
    List<DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail> sparePartLineItemSegments =
        sparePartLineItem.getSparePartLineItemSegments().stream()
            .filter(
                DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail
                    ::getReceiveDetailStatus)
            .toList();
    sparePartLineItemSegments.forEach(
        x -> {
          BigDecimal sparePartqty =
              Objects.nonNull(x.getQuantity())
                  ? BigDecimal.valueOf(x.getQuantity())
                  : BigDecimal.ZERO;
          BigDecimal sparePartmaterial =
              Objects.nonNull(x.getMaterialPrice()) ? x.getMaterialPrice() : BigDecimal.ZERO;
          BigDecimal sparePartservicing =
              Objects.nonNull(x.getServicingPrice()) ? x.getServicingPrice() : BigDecimal.ZERO;
          BigDecimal sparePartTotal =
              sparePartqty.multiply(sparePartmaterial.add(sparePartservicing));
          List<DetailQuotationAskResponseDto.AddSparePartLineItemDetail> addSparePartLineItems =
              x.getAddSparePartLineItems().stream()
                  .filter(
                      DetailQuotationAskResponseDto.AddSparePartLineItemDetail
                          ::getReceiveDetailStatus)
                  .toList();
          BigDecimal addSparePartPrice =
              addSparePartLineItems.stream()
                  .map(
                      item -> {
                        BigDecimal addSparePartqty =
                            Objects.nonNull(item.getQuantity())
                                ? BigDecimal.valueOf(item.getQuantity())
                                : BigDecimal.ZERO;
                        BigDecimal addSparePartmaterial =
                            Objects.nonNull(item.getMaterialPrice())
                                ? item.getMaterialPrice()
                                : BigDecimal.ZERO;
                        BigDecimal addSparePartservicing =
                            Objects.nonNull(item.getServicingPrice())
                                ? item.getServicingPrice()
                                : BigDecimal.ZERO;
                        return addSparePartqty.multiply(
                            addSparePartmaterial.add(addSparePartservicing));
                      })
                  .reduce(BigDecimal.ZERO, BigDecimal::add);
          BigDecimal priceTotal = sparePartTotal.add(addSparePartPrice);
          x.setReceiveDetailPriceTotal(priceTotal);
        });
    // sắp xếp theo giá từ nhỏ đến lớn
    if (QuotationStatus.BIDDING.equals(status)) {
      sparePartLineItem
          .getSparePartLineItemSegments()
          .sort(
              Comparator.comparing(
                  DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail::getPrice,
                  Comparator.nullsLast(BigDecimal::compareTo)));
    } else {
      sparePartLineItem
          .getSparePartLineItemSegments()
          .sort(
              Comparator.comparing(
                  DetailQuotationAskResponseDto.SparePartLineItemSegmentDetail
                      ::getReceiveDetailPriceTotal,
                  Comparator.nullsLast(BigDecimal::compareTo)));
    }
  }

  private void createProductInventory(List<AskedSparePart> spareParts, Long tenantId) {
    List<ProductModel> products = new ArrayList<>();
    spareParts.forEach(
        x -> {
          ProductModel productModel = new ProductModel();
          productModel.setName(x.getPartNameInput());
          productModel.setUnit(x.getPartNameUnit());
          products.add(productModel);
        });
    commonService.createProductClient(products, tenantId);
  }

  private DetailQuotationAskResponseDto getDetailService(QuotationAsk quotationAsk) {
    DetailQuotationAskResponseDto responseDto =
        quotationAskMapper.toDetailResponseDTO(quotationAsk);
    responseDto
        .getSpareParts()
        .sort(
            Comparator.comparing(
                DetailQuotationAskResponseDto.AskedSparePartDetailResponseDto::getId));
    if (QuotationStatus.OPEN.equals(quotationAsk.getStatus())
        || QuotationStatus.ASKING.equals(quotationAsk.getStatus())) {
      return responseDto;
    }
    List<DetailQuotationAskResponseDto.AskedSparePartDetailResponseDto> spareParts =
        responseDto.getSpareParts();
    String quotationAskCode = quotationAsk.getCode();
    QuotationStatus status = quotationAsk.getStatus();
    spareParts.forEach(
        x -> {
          List<DetailQuotationAskResponseDto.SparePartLineItemDetail> sparePartLineItems =
              this.setQuotationBid(quotationAskCode, status, x.getCode());
          x.setSparePartLineItems(sparePartLineItems);
        });
    return responseDto;
  }
}
