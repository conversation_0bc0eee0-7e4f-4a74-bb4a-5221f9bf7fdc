package com.actechx.gf.app.service.purchase;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.dto.BaseSearchRequest;
import com.actechx.common.exception.InvalidDataException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.common.utils.JsonUtils;
import com.actechx.gf.adapter.client.AgentClient;
import com.actechx.gf.adapter.client.CmsClient;
import com.actechx.gf.adapter.client.TenantClient;
import com.actechx.gf.adapter.client.request.CancelPurchaseOrderDto;
import com.actechx.gf.adapter.client.request.PurchaseRequestAgentRequest;
import com.actechx.gf.adapter.client.response.CmsCatalogContentResponse;
import com.actechx.gf.adapter.client.response.TenantResponse;
import com.actechx.gf.adapter.controller.form.request.CreatePurchaseOrderRequestDto;
import com.actechx.gf.adapter.controller.form.request.POProductRequestDTO;
import com.actechx.gf.adapter.controller.form.request.purchase.CancelPurchaserRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.CreatePurchaseRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.SearchPurchaseRequestRequestDto;
import com.actechx.gf.adapter.controller.form.request.purchase.UpdatePurchaseRequestStatusRequest;
import com.actechx.gf.adapter.controller.form.response.purchase.PurchaseOrderDetailResponseDto;
import com.actechx.gf.adapter.controller.form.response.purchase.SearchPurchaseRequestResponseDto;
import com.actechx.gf.adapter.persistence.DBUtils;
import com.actechx.gf.adapter.persistence.purchase.order.POProductEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestDataEntity;
import com.actechx.gf.adapter.persistence.purchase.request.PurchaseRequestEntity;
import com.actechx.gf.adapter.repository.JpaPurchaseRequestDataEntityRepository;
import com.actechx.gf.adapter.repository.specification.PurchaseRequestSpecifications;
import com.actechx.gf.app.mapper.PurchaseOrderMapper;
import com.actechx.gf.app.mapper.PurchaseRequestMapper;
import com.actechx.gf.app.service.CommonService;
import com.actechx.gf.domain.model.enums.POStage;
import com.actechx.gf.domain.model.enums.PaymentMethod;
import com.actechx.gf.domain.model.enums.PurchaseRequestStatus;
import com.actechx.gf.domain.model.enums.PurchaseSource;
import com.actechx.gf.domain.model.purchaseorder.POProduct;
import com.actechx.gf.domain.model.purchaseorder.PurchaseOrder;
import com.actechx.gf.domain.model.purchaserequest.*;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.repository.POProductRepository;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import com.actechx.gf.domain.repository.purchase.CartRepository;
import com.actechx.gf.domain.repository.purchase.PurchaseRequestRepository;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseRequestApplicationService {
  private final DBUtils dbUtils;
  private final TenantClient tenantClient;
  private final CartRepository cartRepository;
  private final PurchaseRequestRepository purchaseRequestRepository;
  private final CartApplicationService cartApplicationService;
  private final QuotationAskRepository quotationAskRepository;
  private final PurchaseOrderMapper purchaseOrderMapper;
  private final PurchaseRequestMapper purchaseRequestMapper;
  private final AgentClient agentClient;
  private final CmsClient cmsClient;
  private final PurchaseOrderApplicationService purchaseOrderApplicationService;
  private final CommonService commonService;
  private final JpaPurchaseRequestDataEntityRepository purchaseRequestDataEntityRepository;
  private final POProductRepository pOProductRepository;
  private final PurchaseOrderRepository purchaseOrderRepository;

  @Transactional
  public PurchaseRequest createPurchaseRequest(List<CreatePurchaseRequestDto> request) {
    Set<Long> allIds =
        request.stream()
            .flatMap(
                dto ->
                    dto.getSparePartsRequest().stream()) // chuyển thành stream<SparePartRequestDto>
            .map(CreatePurchaseRequestDto.CreateSparePartRequestDto::getCartId) // lấy id
            .collect(Collectors.toSet());
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    List<Cart> carts = cartRepository.getAllByIds(allIds, purchaserId);
    List<Long> dbIds = carts.stream().map(Cart::getId).toList();
    if (dbIds.isEmpty()) {
      throw new ResourceNotFoundException("Not found cart by ids", allIds);
    }
    List<Long> invalidIds = allIds.stream().filter(id -> !dbIds.contains(id)).toList();
    if (!invalidIds.isEmpty()) {
      throw new ResourceNotFoundException("Invalid cart by ids", invalidIds);
    }
    ApiResponse<TenantResponse> responseApiResponse = tenantClient.getTenantInfo(purchaserId);
    TenantResponse tenantResponse = responseApiResponse.getData();
    Long purchaseRequestId = dbUtils.getNextSequence("purchase_request_id");
    String purchaseRequestCode =
        String.format("XNĐH-%s-%d", tenantResponse.getCode(), purchaseRequestId);
    PurchaseRequest purchaseRequest =
        new PurchaseRequest(purchaseRequestId, purchaseRequestCode, purchaserId);
    List<PurchaseRequestData> purchaseRequestDataList = new ArrayList<>();
    carts.forEach(
        cart -> {
          SparePartPriceItemModel sparePartPriceItemModel =
              cartApplicationService.getInfoSparePartPrice(cart);
          QuotationAsk quotationAsk =
              quotationAskRepository
                  .findByIdAndTenantId(cart.getQuotationAskId(), purchaserId)
                  .orElseThrow(
                      () ->
                          new ResourceNotFoundException(
                              "QuotationAsk get id, tenantId",
                              cart.getQuotationAskId() + " - " + purchaserId));
          PurchaseRequestData purchaseRequestData =
              new PurchaseRequestData(
                  sparePartPriceItemModel.getPartNameInput(),
                  quotationAsk.getCode(),
                  cart.getProductId(),
                  purchaseRequestId,
                  cart.getPurchaserId(),
                  cart.getSupplierId(),
                  cart.getQuantity(),
                  sparePartPriceItemModel.getPriceTotal(),
                  sparePartPriceItemModel.getMaterialPrice(),
                  sparePartPriceItemModel.getServicingPrice());
          purchaseRequestDataList.add(purchaseRequestData);
        });

    // Add all purchase request data to the aggregate
    purchaseRequestDataList.forEach(purchaseRequest::addPurchaseRequestData);

    // Save to database
    PurchaseRequest savedAgg = purchaseRequestRepository.save(purchaseRequest);
    cartApplicationService.addRefPrCodeAndDelete(carts, savedAgg.getCode());
    List<PurchaseOrder> purchaseOrder = createPurchaseOrderFromPR(savedAgg, tenantResponse);
    callToPurchaseRequestAgent(savedAgg, purchaseOrder, tenantResponse);

    log.info(
        "Created purchase request {} for purchaser {} with {} items",
        savedAgg.getCode(),
        purchaserId,
        purchaseRequestDataList.size());
    return savedAgg;
  }

  @Transactional
  public void cancelService(CancelPurchaserRequestDto request) {
    Long purchaserId = SecurityUtils.getCurrentTenantIdAsLong();
    PurchaseRequestEntity purchaseRequestEntity =
        purchaseRequestRepository
            .findEntityByIdAndTenantId(request.getId(), purchaserId)
            .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest", request.getId()));
    var purchaseOrders =
        purchaseOrderRepository.findByPrIdAndTenantId(request.getId(), purchaserId);
    for (var purchaseOrder : purchaseOrders) {
      if (POStage.WAIT_TO_CONFIRM.equals(purchaseOrder.getStage())) {
        purchaseOrder.updateStageByPrStatus(PurchaseRequestStatus.CANCELLED);
        purchaseOrderRepository.save(purchaseOrder);
      } else if (!POStage.CANCELLED.equals(purchaseOrder.getStage())) {
        throw new InvalidDataException("PurchaseRequest - stage", "Cannot cancel purchase request");
      }
    }

    purchaseRequestEntity.setNote(request.getNote());
    purchaseRequestEntity.setStatus(PurchaseRequestStatus.CANCELLED);
    purchaseRequestRepository.store(purchaseRequestEntity);

    CancelPurchaseOrderDto cancelPurchaseOrderDto = new CancelPurchaseOrderDto();
    cancelPurchaseOrderDto.setCode(purchaseRequestEntity.getCode());
    cancelPurchaseOrderDto.setPurchaserId(purchaseRequestEntity.getPurchaserId());
    cancelPurchaseOrderDto.setStatus(purchaseRequestEntity.getStatus());
    cancelPurchaseOrderDto.setNote(purchaseRequestEntity.getNote());
    log.info("Call Agent cancel purchaseRequest with request: {}", request);
    ApiResponse<String> agentClientAgent =
        agentClient.cancelPurchaseRequest(cancelPurchaseOrderDto);
    log.info("Call Agent cancel purchaseRequest with response: {}", agentClientAgent.getData());
  }

  public Page<SearchPurchaseRequestResponseDto> searchService(
      SearchPurchaseRequestRequestDto request) {
    PageRequest pageable = BaseSearchRequest.buildPageRequest(request);
    Specification<PurchaseRequestEntity> spec = PurchaseRequestSpecifications.withFilters(request);
    Page<PurchaseRequestEntity> pageResult = purchaseRequestRepository.findAll(spec, pageable);
    return pageResult.map(this::toPurchaseRequestDataDto);
  }

  private List<PurchaseOrder> createPurchaseOrderFromPR(
      PurchaseRequest savedAgg, TenantResponse tenantInfo) {
    Map<Long, List<PurchaseRequestData>> requestDataMap =
        savedAgg.getPurchaseRequestDataList().stream()
            .collect(Collectors.groupingBy(PurchaseRequestData::getSupplierId));
    List<PurchaseOrder> purchaseOrderResponseDtoList = new ArrayList<>();
    for (var entry : requestDataMap.entrySet()) {
      Long supplierId = entry.getKey();
      List<PurchaseRequestData> items = entry.getValue();
      ApiResponse<TenantResponse> responseApiResponse = tenantClient.getTenantInfo(supplierId);
      TenantResponse supplierInfo = responseApiResponse.getData();
      List<POProductRequestDTO> productRequestDTOList =
          items.stream()
              .map(
                  i -> {
                    ProductInventoryModel productInventoryModel =
                        commonService.getProductInfoClient(i.getProductId());
                    return new POProductRequestDTO(
                        i.getId(),
                        i.getProductId(),
                        i.getRequestedQuantity().longValue(),
                        i.getDetailedPrice(),
                        productInventoryModel.getUnit(),
                        productInventoryModel.getSegment(),
                        supplierId,
                        productInventoryModel.getName(),
                        null,
                        i.getQuotationAskCode());
                  })
              .toList();
      List<String> quotationAskCodes =
          items.stream()
              .map(PurchaseRequestData::getQuotationAskCode)
              .distinct()
              .sorted(Comparator.naturalOrder())
              .toList();

      CreatePurchaseOrderRequestDto createRequest =
          new CreatePurchaseOrderRequestDto(
              PurchaseSource.CART,
              savedAgg.getPurchaserId(),
              supplierId,
              supplierInfo.getTenantName(),
              PaymentMethod.COD,
              false,
              StringUtils.join(quotationAskCodes, ","),
              null,
              null,
              productRequestDTOList);
      purchaseOrderResponseDtoList.add(
          purchaseOrderApplicationService.createPurchaseOrderApplication(
              createRequest, tenantInfo.getCode(), savedAgg.getId()));
    }
    return purchaseOrderResponseDtoList;
  }

  private void callToPurchaseRequestAgent(
      PurchaseRequest purchaseRequest,
      List<PurchaseOrder> purchaseOrders,
      TenantResponse tenantInfo) {

    String tenantOpsRegion = null;
    if (StringUtils.isNotBlank(tenantInfo.getOpsArea())) {
      ApiResponse<CmsCatalogContentResponse> cmsCatalogContentResponseApiResponse =
          cmsClient.getCatalogContentClient(tenantInfo.getOpsArea());
      tenantOpsRegion = cmsCatalogContentResponseApiResponse.getData().getCode();
    }
    PurchaseRequestAgentRequest.PurchaserInformation purchaserInformation =
        new PurchaseRequestAgentRequest.PurchaserInformation(
            tenantInfo.getId(),
            tenantInfo.getTenantName(),
            tenantInfo.getAddress(),
            tenantInfo.getOpsArea(),
            tenantOpsRegion,
            tenantInfo.getTenantPhoneNumber());
    String purchaerInformationJson = JsonUtils.toJson(purchaserInformation);

    Map<Long, POProduct> poProductMap =
        purchaseOrders.stream()
            .flatMap(po -> po.getPoProducts().stream())
            .collect(Collectors.toMap(POProduct::getProductId, Function.identity(), (a, b) -> a));

    List<PurchaseRequestAgentRequest.PurchaseRequestDataDto> purchaseRequestDataList =
        purchaseRequest.getPurchaseRequestDataList().stream()
            .map(
                reqData -> {
                  var data = purchaseOrderMapper.toRequestData(reqData);
                  var poProduct = poProductMap.get(reqData.getProductId());
                  if (poProduct != null) {
                    data.setUnitPrice(poProduct.getUnitPrice());
                    data.setUnit(poProduct.getUnit());
                    data.setSegment(poProduct.getSegment());
                  }
                  return data;
                })
            .toList();
    String purchaseRequestDataListJson = JsonUtils.toJson(purchaseRequestDataList);

    List<PurchaseRequestAgentRequest.PurchaseOrderDto> purchaseOrderList =
        purchaseOrders.stream().map(purchaseOrderMapper::toRequestDto).toList();
    String purchaseOrderListJson = JsonUtils.toJson(purchaseOrderList);

    PurchaseSource source = purchaseOrders.isEmpty() ? null : purchaseOrders.getFirst().getSource();
    PurchaseRequestAgentRequest request =
        new PurchaseRequestAgentRequest(
            purchaseRequest.getCode(),
            purchaseRequest.getPurchaserId(),
            purchaseRequest.getStatus(),
            purchaseRequestDataListJson,
            purchaseOrderListJson,
            purchaerInformationJson,
            tenantInfo.getCode(),
            source,
            null);
    log.info("Call Agent create purchaseRequest with request: {}", request);
    ApiResponse<String> agentClientAgent = agentClient.createPurchaseRequest(request);
    log.info("Call Agent create purchaseRequest success: {}", agentClientAgent.getData());
  }

  @Transactional
  public PurchaseOrderDetailResponseDto getDetailByIdService(Long purchaseId) {
    PurchaseRequest purchaseRequest =
        purchaseRequestRepository
            .findById(purchaseId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "Purchase Request not found with purchaseId: ", purchaseId));
    return this.detailPurchaseRequest(purchaseRequest);
  }

  @Transactional
  public PurchaseOrderDetailResponseDto getDetailByCodeService(String code) {
    PurchaseRequest purchaseRequest =
        purchaseRequestRepository
            .findByCode(code)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException("Purchase Request not found with code: ", code));
    return this.detailPurchaseRequest(purchaseRequest);
  }

  private PurchaseOrderDetailResponseDto detailPurchaseRequest(PurchaseRequest purchaseRequest) {

    PurchaseOrderDetailResponseDto purchaseOrderDetailResponseDto =
        new PurchaseOrderDetailResponseDto();

    // Common Info
    purchaseOrderDetailResponseDto.setCommonInfo(processCommonInfo(purchaseRequest));

    // Personal Info
    purchaseOrderDetailResponseDto.setPersonalInfo(processPersonalInfo());

    // SparePartInfo
    purchaseOrderDetailResponseDto.setSparePartInfoConfirmed(processSparePartInfo(purchaseRequest));

    // Payment Info
    // purchaseOrderDetailResponseDto.setPaymentInfo(processPaymentInfo());
    return purchaseOrderDetailResponseDto;
  }

  private List<PurchaseOrderDetailResponseDto.SparePartInfo> processSparePartInfo(
      PurchaseRequest purchaseRequest) {
    if (purchaseRequest.getPurchaseRequestDataList() == null) {
      return List.of();
    }
    Set<Long> tenantIds =
        purchaseRequest.getPurchaseRequestDataList().stream()
            .map(PurchaseRequestData::getSupplierId)
            .collect(Collectors.toSet());

    List<PurchaseOrderDetailResponseDto.SparePartInfo> sparePartInfos = new ArrayList<>();
    for (Long tenantId : tenantIds) {
      String tenantName = tenantClient.getTenantInfo(tenantId).getData().getTenantName();
      List<PurchaseOrderDetailResponseDto.SparePartInfoConfirmed> sparePartInfoConfirmeds =
          processSparePartInfoConfirmed(
              purchaseRequest.getPurchaseRequestDataList(), purchaseRequest.getId());

      // Total price each vendor
      BigDecimal totalPrice =
          sparePartInfoConfirmeds.stream()
              .map(item -> Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO))
              .reduce(BigDecimal.ZERO, BigDecimal::add);

      PurchaseOrderDetailResponseDto.SparePartInfo sparePartInfo =
          PurchaseOrderDetailResponseDto.SparePartInfo.builder()
              .vendorName(tenantName)
              .totalPrice(totalPrice)
              .sparePartInfoConfirmed(sparePartInfoConfirmeds)
              .build();

      sparePartInfos.add(sparePartInfo);

      return sparePartInfos;
    }
    return sparePartInfos;
  }

  private List<PurchaseOrderDetailResponseDto.SparePartInfoConfirmed> processSparePartInfoConfirmed(
      List<PurchaseRequestData> purchaseRequestDatas, Long prId) {
    List<POProductEntity> poProductEntities = pOProductRepository.findByPrId(prId);
    return purchaseRequestDatas.stream()
        .map(
            item -> {
              POProductEntity poProduct =
                  poProductEntities.stream()
                      .filter(
                          pod ->
                              pod.getProductId().equals(item.getProductId())
                                  && pod.getQuotationAskCode().equals(item.getQuotationAskCode())
                                  && pod.getSupplierId().equals(item.getSupplierId()))
                      .findFirst()
                      .orElse(null);

              if (poProduct != null) {
                BigDecimal totalPrice =
                    item.getDetailedPrice().multiply(BigDecimal.valueOf(poProduct.getQuantity()));
                return PurchaseOrderDetailResponseDto.SparePartInfoConfirmed.builder()
                    .requestProductName(item.getRequestedProductName())
                    .poId(poProduct.getPoId())
                    .productId(poProduct.getProductId())
                    .supplierId(poProduct.getSupplierId())
                    .quantity(
                        Long.valueOf(
                            item.getActualSalesQuantity() == null
                                ? 0
                                : item.getActualSalesQuantity()))
                    .price(totalPrice)
                    .purchaseRequestStatus(item.getSalesStatus())
                    .segment(poProduct.getSegment())
                    .build();
              }
              return PurchaseOrderDetailResponseDto.SparePartInfoConfirmed.builder()
                  .requestProductName(item.getRequestedProductName())
                  .poId(null)
                  .productId(null)
                  .supplierId(null)
                  .quantity(
                      Long.valueOf(
                          item.getActualSalesQuantity() == null
                              ? 0
                              : item.getActualSalesQuantity()))
                  .price(null)
                  .segment(null)
                  .build();
            })
        .toList();
  }

  private PurchaseOrderDetailResponseDto.PersonalInfo processPersonalInfo() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    TenantResponse tenantResponse = tenantClient.getTenantInfo(tenantId).getData();
    return PurchaseOrderDetailResponseDto.PersonalInfo.builder()
        .name(tenantResponse.getTenantName())
        .address(tenantResponse.getAddress())
        .phoneNumber(tenantResponse.getTenantPhoneNumber())
        .build();
  }

  private PurchaseOrderDetailResponseDto.CommonInfo processCommonInfo(
      PurchaseRequest purchaseRequest) {
    if (purchaseRequest == null) {
      return null;
    }
    return purchaseRequestMapper.toCommonInfo(purchaseRequest);
  }

  private SearchPurchaseRequestResponseDto toPurchaseRequestDataDto(PurchaseRequestEntity entity) {
    SearchPurchaseRequestResponseDto responseDto = new SearchPurchaseRequestResponseDto();
    responseDto.setId(entity.getId());
    responseDto.setCode(entity.getCode());
    responseDto.setStatus(entity.getStatus());
    responseDto.setCreatedAt(entity.getCreatedAt());
    List<PurchaseRequestDataEntity> purchaseRequestDataEntities =
        purchaseRequestDataEntityRepository.findByPurchaseRequestId(entity.getId());
    List<SearchPurchaseRequestResponseDto.SearchPurchaseRequestDataResponseDto>
        purchaserRequestDataList = new ArrayList<>();
    purchaseRequestDataEntities.forEach(
        x -> {
          SearchPurchaseRequestResponseDto.SearchPurchaseRequestDataResponseDto
              purchaserRequestDataResponseDto =
                  new SearchPurchaseRequestResponseDto.SearchPurchaseRequestDataResponseDto();
          purchaserRequestDataResponseDto.setId(x.getId());
          purchaserRequestDataResponseDto.setRequestedProductName(x.getRequestedProductName());
          purchaserRequestDataResponseDto.setQuotationAskCode(x.getQuotationAskCode());
          purchaserRequestDataList.add(purchaserRequestDataResponseDto);
        });
    responseDto.setPurchaserRequestDataList(purchaserRequestDataList);
    return responseDto;
  }

  @Transactional
  public void updateStatus(UpdatePurchaseRequestStatusRequest request) {
    var purchaseRequest =
        purchaseRequestRepository
            .findEntityByCode(request.getOriginMessageCode())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "PurchaseRequest", "code", request.getOriginMessageCode()));
    purchaseRequest.setStatus(PurchaseRequestStatus.valueOf(request.getStatus()));
    purchaseRequest.setUpdatedAt(Instant.now());
    purchaseRequestRepository.store(purchaseRequest);
    var purchaseOrders =
        purchaseOrderRepository.findByPrIdAndTenantId(
            purchaseRequest.getId(), purchaseRequest.getPurchaserId());
    for (var purchaseOrder : purchaseOrders) {
      purchaseOrder.updateStageByPrStatus(purchaseRequest.getStatus());
      purchaseOrderRepository.save(purchaseOrder);
    }
  }
}
