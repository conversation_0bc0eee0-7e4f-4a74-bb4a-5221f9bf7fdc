package com.actechx.gf.app.service;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingChartResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingOverviewResponseDto;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.app.utils.MoneyUtils;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.enums.SpendingPeriodEnum;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class StatisticsApplicationService {

  private final QuotationAskRepository quotationAskRepository;
  private final PurchaseOrderRepository purchaseOrderRepository;

  /**
   * Lấy thống kê dashboard cho garage
   * @return DashboardStatsResponseDto chứa các thông tin thống kê
   */
  public DashboardStatsResponseDto getDashboardStats() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting dashboard stats for tenantId: {}", tenantId);

    // Tính thời gian tuần hiện tại
    Instant startOfWeek = DateTimeUtils.getStartOfCurrentWeek();
    Instant endOfWeek = DateTimeUtils.getEndOfCurrentWeek();
    
    log.debug("Week range: {} to {}", startOfWeek, endOfWeek);

    // 1. Số yêu cầu báo giá garage đã tạo trong tuần này
    Long totalQuotationRequestsThisWeek = quotationAskRepository
        .countByTenantIdAndCreatedAtBetween(tenantId, startOfWeek, endOfWeek);

    // 2. Số yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này
    Long quotationRequestsPricingThisWeek = quotationAskRepository
        .countByTenantIdAndStatusAndCreatedAtBetween(tenantId, QuotationStatus.PRICING, startOfWeek, endOfWeek);

    // 3. Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING)
    Long purchaseOrdersInShipping = purchaseOrderRepository
        .countByPurchaserIdAndStatus(tenantId, POStatusEnum.IN_SHIPPING);

    // 4. Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) với thời gian hoàn thành trong tuần này
    Long purchaseOrdersCompletedThisWeek = purchaseOrderRepository
        .countByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfWeek, endOfWeek);

    log.info("Dashboard stats for tenantId {}: totalQuotationRequests={}, quotationRequestsPricing={}, " +
        "purchaseOrdersInShipping={}, purchaseOrdersCompleted={}", 
        tenantId, totalQuotationRequestsThisWeek, quotationRequestsPricingThisWeek, 
        purchaseOrdersInShipping, purchaseOrdersCompletedThisWeek);

    return DashboardStatsResponseDto.builder()
        .totalQuotationRequestsThisWeek(totalQuotationRequestsThisWeek)
        .quotationRequestsPricingThisWeek(quotationRequestsPricingThisWeek)
        .purchaseOrdersInShipping(purchaseOrdersInShipping)
        .purchaseOrdersCompletedThisWeek(purchaseOrdersCompletedThisWeek)
        .build();
  }

  /**
   * Lấy tổng quan số liệu chi tiêu cho garage
   * @return SpendingOverviewResponseDto chứa thông tin chi tiêu theo tuần, tháng, năm
   */
  public SpendingOverviewResponseDto getSpendingOverview() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting spending overview for tenantId: {}", tenantId);

    // Tính thời gian các khoảng
    Instant startOfWeek = DateTimeUtils.getStartOfCurrentWeek();
    Instant endOfWeek = DateTimeUtils.getEndOfCurrentWeek();
    Instant startOfMonth = DateTimeUtils.getStartOfCurrentMonth();
    Instant endOfMonth = DateTimeUtils.getEndOfCurrentMonth();
    Instant startOfYear = DateTimeUtils.getStartOfCurrentYear();
    Instant endOfYear = DateTimeUtils.getEndOfCurrentYear();

    log.debug("Time ranges - Week: {} to {}, Month: {} to {}, Year: {} to {}",
        startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear);

    // Tính tổng chi tiêu cho từng khoảng thời gian (chỉ đơn hàng hoàn thành - CLOSED)
    BigDecimal totalSpentThisWeek = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfWeek, endOfWeek);

    BigDecimal totalSpentThisMonth = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfMonth, endOfMonth);

    BigDecimal totalSpentThisYear = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfYear, endOfYear);

    // Xử lý null values
    totalSpentThisWeek = totalSpentThisWeek != null ? totalSpentThisWeek : BigDecimal.ZERO;
    totalSpentThisMonth = totalSpentThisMonth != null ? totalSpentThisMonth : BigDecimal.ZERO;
    totalSpentThisYear = totalSpentThisYear != null ? totalSpentThisYear : BigDecimal.ZERO;

    // Làm tròn số tiền
    BigDecimal totalSpentThisWeekRounded = MoneyUtils.formatMoney(totalSpentThisWeek);
    BigDecimal totalSpentThisMonthRounded = MoneyUtils.formatMoney(totalSpentThisMonth);
    BigDecimal totalSpentThisYearRounded = MoneyUtils.formatMoney(totalSpentThisYear);

    // Tạo chuỗi hiển thị với đơn vị
    String totalSpentThisWeekDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisWeek);
    String totalSpentThisMonthDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisMonth);
    String totalSpentThisYearDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisYear);

    // Tạo chuỗi thời gian cập nhật (tuần hiện tại)
    String updateTime = formatUpdateTime(startOfWeek, endOfWeek);

    log.info("Spending overview for tenantId {}: week={}, month={}, year={}",
        tenantId, totalSpentThisWeek, totalSpentThisMonth, totalSpentThisYear);

    return SpendingOverviewResponseDto.builder()
        .totalSpentThisWeek(totalSpentThisWeek)
        .totalSpentThisWeekRounded(totalSpentThisWeekRounded)
        .totalSpentThisMonth(totalSpentThisMonth)
        .totalSpentThisMonthRounded(totalSpentThisMonthRounded)
        .totalSpentThisYear(totalSpentThisYear)
        .totalSpentThisYearRounded(totalSpentThisYearRounded)
        .updateTime(updateTime)
        .totalSpentThisWeekDisplay(totalSpentThisWeekDisplay)
        .totalSpentThisMonthDisplay(totalSpentThisMonthDisplay)
        .totalSpentThisYearDisplay(totalSpentThisYearDisplay)
        .build();
  }

  /**
   * Format thời gian cập nhật theo định dạng dd/MM/yyyy - dd/MM/yyyy
   * @param startTime Thời gian bắt đầu
   * @param endTime Thời gian kết thúc
   * @return Chuỗi thời gian đã format
   */
  private String formatUpdateTime(Instant startTime, Instant endTime) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    LocalDateTime startDateTime = DateTimeUtils.convertInstantToLocalDateTime(startTime);
    LocalDateTime endDateTime = DateTimeUtils.convertInstantToLocalDateTime(endTime);

    return String.format("%s - %s",
        startDateTime.format(formatter),
        endDateTime.format(formatter));
  }

  /**
   * Lấy dữ liệu biểu đồ chi tiêu theo khoảng thời gian
   * @param period Loại khoảng thời gian (THIS_WEEK, THIS_MONTH, LAST_6_MONTHS, LAST_YEAR)
   * @return SpendingChartResponseDto chứa dữ liệu biểu đồ
   */
  public SpendingChartResponseDto getSpendingChart(SpendingPeriodEnum period) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting spending chart for tenantId: {} with period: {}", tenantId, period);

    List<String> labels = new ArrayList<>();
    List<BigDecimal> data = new ArrayList<>();
    List<Instant[]> timeRanges = new ArrayList<>();

    // Lấy khoảng thời gian và labels theo period
    switch (period) {
      case THIS_WEEK:
        timeRanges = DateTimeUtils.getDaysOfCurrentWeek();
        labels = List.of("T2", "T3", "T4", "T5", "T6", "T7", "CN");
        break;
      case THIS_MONTH:
        timeRanges = DateTimeUtils.getWeeksOfCurrentMonth();
        for (int i = 1; i <= timeRanges.size(); i++) {
          labels.add("Tuần " + i);
        }
        break;
      case LAST_6_MONTHS:
        timeRanges = DateTimeUtils.getLast6Months();
        labels = generateMonthLabels(timeRanges);
        break;
      case LAST_YEAR:
        timeRanges = DateTimeUtils.getLast4Quarters();
        labels = generateQuarterLabels(timeRanges);
        break;
      default:
        throw new IllegalArgumentException("Unsupported period: " + period);
    }

    // Tính chi tiêu cho từng khoảng thời gian
    for (Instant[] timeRange : timeRanges) {
      BigDecimal amount = purchaseOrderRepository.sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(
          tenantId, POStatusEnum.CLOSED, timeRange[0], timeRange[1]);
      data.add(amount != null ? amount : BigDecimal.ZERO);
    }

    // Tính toán các giá trị thống kê
    BigDecimal totalSpent = data.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal maxValue = data.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    BigDecimal minValue = data.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    BigDecimal averageValue = data.isEmpty() ? BigDecimal.ZERO :
        totalSpent.divide(BigDecimal.valueOf(data.size()), 2, RoundingMode.HALF_UP);

    // Làm tròn dữ liệu
    List<BigDecimal> dataRounded = data.stream()
        .map(MoneyUtils::formatMoney)
        .toList();

    // Tạo chuỗi hiển thị
    List<String> dataDisplay = data.stream()
        .map(MoneyUtils::formatMoneyWithUnit)
        .toList();

    // Tạo time range string
    String timeRange = generateTimeRangeString(period, timeRanges);

    log.info("Spending chart for tenantId {} with period {}: total={}, max={}, min={}, avg={}",
        tenantId, period, totalSpent, maxValue, minValue, averageValue);

    return SpendingChartResponseDto.builder()
        .period(period)
        .periodDisplayName(period.getDisplayName())
        .labels(labels)
        .data(data)
        .dataRounded(dataRounded)
        .dataDisplay(dataDisplay)
        .totalSpent(totalSpent)
        .totalSpentRounded(MoneyUtils.formatMoney(totalSpent))
        .totalSpentDisplay(MoneyUtils.formatMoneyWithUnit(totalSpent))
        .timeRange(timeRange)
        .maxValue(maxValue)
        .minValue(minValue)
        .averageValue(averageValue)
        .build();
  }

  /**
   * Tạo labels cho 6 tháng gần nhất
   * Format: MM/yyyy (VD: "10/2024", "11/2024")
   */
  private List<String> generateMonthLabels(List<Instant[]> timeRanges) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/yyyy");
    return timeRanges.stream()
        .map(range -> DateTimeUtils.convertInstantToLocalDateTime(range[0]).format(formatter))
        .toList();
  }

  /**
   * Tạo labels cho 4 quý gần nhất
   * Format: Q{quý}/{năm} (VD: "Q2/2024", "Q3/2024")
   */
  private List<String> generateQuarterLabels(List<Instant[]> timeRanges) {
    return timeRanges.stream()
        .map(range -> {
          LocalDateTime startTime = DateTimeUtils.convertInstantToLocalDateTime(range[0]);
          int quarter = (startTime.getMonthValue() - 1) / 3 + 1;
          return String.format("Q%d/%d", quarter, startTime.getYear());
        })
        .toList();
  }

  /**
   * Tạo chuỗi mô tả khoảng thời gian
   */
  private String generateTimeRangeString(SpendingPeriodEnum period, List<Instant[]> timeRanges) {
    if (timeRanges.isEmpty()) {
      return "";
    }

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    Instant start = timeRanges.get(0)[0];
    Instant end = timeRanges.get(timeRanges.size() - 1)[1];

    LocalDateTime startDateTime = DateTimeUtils.convertInstantToLocalDateTime(start);
    LocalDateTime endDateTime = DateTimeUtils.convertInstantToLocalDateTime(end);

    return String.format("%s - %s",
        startDateTime.format(formatter),
        endDateTime.format(formatter));
  }
}
