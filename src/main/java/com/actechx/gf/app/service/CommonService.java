package com.actechx.gf.app.service;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.enums.BusinessMessage;
import com.actechx.common.exception.BusinessException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.gf.adapter.client.GFInventoryClient;
import com.actechx.gf.adapter.client.request.*;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.app.mapper.QuotationAskMapper;
import com.actechx.gf.domain.model.common.ProductModel;
import com.actechx.gf.domain.model.enums.ProductTypeEnum;
import com.actechx.gf.domain.model.enums.Segment;
import com.actechx.gf.domain.model.purchaserequest.ProductInventoryModel;
import com.actechx.gf.domain.model.quotationask.AskedSparePartModel;
import com.actechx.gf.domain.repository.AskedSparePartRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommonService {
  private final QuotationAskMapper quotationAskMapper;
  private final AskedSparePartRepository askedSparePartRepository;
  private final GFInventoryClient gfInventoryClient;

  public AskedSparePartModel getAskedSparePartByCode(String code) {
    AskedSparePartEntity askedSparePartEntity =
        askedSparePartRepository
            .getByCode(code)
            .orElseThrow(() -> new ResourceNotFoundException("sparePartCode by code", code));
    AskedSparePartModel model = quotationAskMapper.toAskedSparePartModel(askedSparePartEntity);
    model.setQuotationAskId(askedSparePartEntity.getQuotationAsk().getId());
    return model;
  }

  public void createProductClient(List<ProductModel> products, Long tenantId) {
    //    this.checkDuplicateNames(products);
    CreateProductDto createProductDto = new CreateProductDto();
    List<CreateProductDto.Product> productList = new ArrayList<>();
    products.forEach(
        x -> {
          CreateProductDto.Product product = new CreateProductDto.Product();
          product.setProductType(ProductTypeEnum.SPARE_PART);
          product.setTenantId(tenantId);
          product.setName(x.getName());
          product.setUnit(x.getUnit());
          productList.add(product);
        });
    createProductDto.setProducts(productList);
    if (!CollectionUtils.isEmpty(createProductDto.getProducts())) {
      log.info("Call api create product inventory: {}", createProductDto.getProducts());
      gfInventoryClient.createProductInventoryClient(createProductDto);
    }
  }

  public Long getProductByIdClient(String name, Segment segment, Long tenantId) {
    ApiResponse<Long> response =
        gfInventoryClient.getProductIdClient(
            name, tenantId, ProductTypeEnum.SPARE_PART.name(), segment.name());
    return response.getData();
  }

  public ProductInventoryModel getProductInfoClient(Long productId) {
    ApiResponse<ProductInventoryModel> response = gfInventoryClient.getProductInfoClient(productId);
    return response.getData();
  }

  private void checkDuplicateNames(List<ProductModel> products) {
    Set<String> duplicates =
        products.stream()
            .collect(Collectors.groupingBy(ProductModel::getName, Collectors.counting()))
            .entrySet()
            .stream()
            .filter(e -> e.getValue() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());

    if (!duplicates.isEmpty()) {
      throw new BusinessException(
          BusinessMessage.IAM_037, "Tên sản phẩm bị trùng: " + String.join(", ", duplicates));
    }
  }
}
