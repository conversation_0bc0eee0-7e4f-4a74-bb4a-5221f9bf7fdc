package com.actechx.gf.app.service.purchase;

import com.actechx.common.enums.BusinessMessage;
import com.actechx.common.exception.BusinessException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.request.purchase.UpdateCartRequestDto;
import com.actechx.gf.adapter.controller.form.response.purchase.CartResponseDto;
import com.actechx.gf.adapter.persistence.purchase.cart.CartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.app.mapper.purchase.CartMapper;
import com.actechx.gf.app.service.CommonService;
import com.actechx.gf.app.utils.QuotationUtil;
import com.actechx.gf.domain.model.enums.Segment;
import com.actechx.gf.domain.model.purchaserequest.Cart;
import com.actechx.gf.domain.model.purchaserequest.CartAdd;
import com.actechx.gf.domain.model.purchaserequest.ProductInventoryModel;
import com.actechx.gf.domain.model.purchaserequest.SparePartPriceItemModel;
import com.actechx.gf.domain.model.quotationask.AddSparePartLineItemModel;
import com.actechx.gf.domain.model.quotationask.AskedSparePart;
import com.actechx.gf.domain.model.quotationask.AskedSparePartModel;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.repository.AddedSparePartPriceLineItemRepository;
import com.actechx.gf.domain.repository.BiddedSparePartRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import com.actechx.gf.domain.repository.SparePartPriceLineItemRepository;
import com.actechx.gf.domain.repository.purchase.CartRepository;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CartApplicationService {
  private final CartRepository repository;
  private final CommonService commonService;
  private final SparePartPriceLineItemRepository sparePartPriceLineItemRepository;
  private final QuotationAskRepository quotationAskRepository;
  private final BiddedSparePartRepository biddedSparePartRepository;
  private final AddedSparePartPriceLineItemRepository addedSparePartPriceLineItemRepository;
  private final CartMapper cartMapper;

  @Transactional
  public int addCartService(Long sparePartPriceLineItemId) {
    SparePartPriceLineItemEntity sparePartPriceLineItemEntity =
        sparePartPriceLineItemRepository
            .getByIdAndDetailStatusTrue(sparePartPriceLineItemId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "SparePartPriceLineItemEntity", sparePartPriceLineItemId));
    if (!sparePartPriceLineItemEntity.getReceiveDetailStatus()) {
      throw new BusinessException(
          BusinessMessage.IAM_037, "Sản phẩm chưa có phản hồi báo giá chi tiết");
    }
    List<CartAdd> cartAdds = new ArrayList<>();
    this.setSparePartLineItem(cartAdds, sparePartPriceLineItemEntity);
    this.setAddSparePartLineItem(cartAdds, sparePartPriceLineItemEntity);
    cartAdds = new ArrayList<>(new LinkedHashSet<>(cartAdds));
    return repository.addCartRepo(cartAdds);
  }

  @Transactional
  public List<CartResponseDto> getAllService() {
    List<CartResponseDto> cartResponseDtos = new ArrayList<>();
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    List<Cart> carts = repository.getAllByPurchaserId(tenantId);
    Map<Long, List<Cart>> groupedBySupplierId =
        carts.stream().collect(Collectors.groupingBy(Cart::getSupplierId));
    groupedBySupplierId.forEach(
        (supplierId, cartList) -> {
          CartResponseDto cartResponseDto = new CartResponseDto();
          cartResponseDto.setTenantId(supplierId);
          cartResponseDto.setName("Nhà Cung Cấp " + supplierId);
          List<CartResponseDto.SparePartCartDto> sparePartDtos = new ArrayList<>();
          cartList.forEach(
              x -> {
                CartResponseDto.SparePartCartDto dto = new CartResponseDto.SparePartCartDto();
                dto.setId(x.getId());
                dto.setTenantId(x.getSupplierId());
                ProductInventoryModel productInventoryModel =
                    commonService.getProductInfoClient(x.getProductId());
                dto.setName(productInventoryModel.getName());
                dto.setUnit(productInventoryModel.getUnit());
                dto.setSegment(productInventoryModel.getSegment());
                dto.setQuantity(x.getQuantity());
                SparePartPriceItemModel sparePartPriceItemModel = this.getInfoSparePartPrice(x);
                dto.setMinQuantity(sparePartPriceItemModel.getQuantity());
                dto.setPrice(sparePartPriceItemModel.getPriceTotal());
                dto.setIsPicked(x.getIsPicked());
                dto.setRefPrCode(x.getRefPrCode());
                dto.setNote(sparePartPriceItemModel.getNote());
                sparePartDtos.add(dto);
              });
          cartResponseDto.setSpareParts(sparePartDtos);
          cartResponseDtos.add(cartResponseDto);
        });
    return cartResponseDtos;
  }

  @Transactional
  public void updateByIdService(UpdateCartRequestDto request) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    CartEntity cartEntity =
        repository
            .getByIdAndPurchaserId(request.getId(), tenantId)
            .orElseThrow(
                () -> new ResourceNotFoundException("cartEntity get by id", request.getId()));
    Cart cart = cartMapper.toCart(cartEntity);
    SparePartPriceItemModel sparePartPriceItemModel = this.getInfoSparePartPrice(cart);
    if (request.getQuantity() < sparePartPriceItemModel.getQuantity()) {
      throw new BusinessException(
          BusinessMessage.IAM_037,
          "Quantity must be great than or equal to " + sparePartPriceItemModel.getQuantity());
    }
    cartEntity.setQuantity(request.getQuantity());
    repository.update(cartEntity);
  }

  @Transactional
  public void deleteByIdService(Long id) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    CartEntity cartEntity =
        repository
            .getByIdAndPurchaserId(id, tenantId)
            .orElseThrow(() -> new ResourceNotFoundException("cartEntity get by id", id));
    cartEntity.setDeleted(true);
    repository.delete(cartEntity);
  }

  public void addRefPrCodeAndDelete(List<Cart> carts, String prCode) {
    List<Long> ids = carts.stream().map(Cart::getId).toList();
    repository.addRefPrCodeAndDelete(ids, prCode);
  }

  private void setSparePartLineItem(
      List<CartAdd> cartAdds, SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    CartAdd cartAdd = new CartAdd();
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    AskedSparePartModel askedSparePartModel =
        commonService.getAskedSparePartByCode(sparePartPriceLineItemEntity.getSparePartInputCode());
    Long productId =
        commonService.getProductByIdClient(
            askedSparePartModel.getPartNameInput(),
            sparePartPriceLineItemEntity.getSegment(),
            tenantId);
    cartAdd.setProductId(productId);
    cartAdd.setQuotationAskId(askedSparePartModel.getQuotationAskId());
    cartAdd.setPurchaserId(tenantId);
    cartAdd.setSupplierId(sparePartPriceLineItemEntity.getQuotationBid().getTenantId());
    cartAdd.setQuantity(sparePartPriceLineItemEntity.getQuantity());
    cartAdds.add(cartAdd);
  }

  private void setAddSparePartLineItem(
      List<CartAdd> cartAdds, SparePartPriceLineItemEntity sparePartPriceLineItemEntity) {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    AskedSparePartModel askedSparePartModel =
        commonService.getAskedSparePartByCode(sparePartPriceLineItemEntity.getSparePartInputCode());
    List<AddSparePartLineItemModel> addSparePartLineItemModels =
        QuotationUtil.getAddSparePartPriceLineItemsReceiveDetailStatusTrue(
            sparePartPriceLineItemEntity);
    addSparePartLineItemModels.forEach(
        x -> {
          CartAdd cartAdd = new CartAdd();
          Long productId =
              commonService.getProductByIdClient(x.getPartNameInput(), x.getSegment(), tenantId);
          cartAdd.setProductId(productId);
          cartAdd.setQuotationAskId(askedSparePartModel.getQuotationAskId());
          cartAdd.setPurchaserId(tenantId);
          cartAdd.setSupplierId(sparePartPriceLineItemEntity.getQuotationBid().getTenantId());
          cartAdd.setQuantity(x.getQuantity());
          cartAdds.add(cartAdd);
        });
  }

  public SparePartPriceItemModel getInfoSparePartPrice(Cart cart) {
    ProductInventoryModel productInventoryModel =
        commonService.getProductInfoClient(cart.getProductId());
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByIdAndTenantId(cart.getQuotationAskId(), SecurityUtils.getCurrentTenantIdAsLong())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException("QuotationAsk by id", cart.getQuotationAskId()));
    String name = productInventoryModel.getName();
    List<AskedSparePart> spareParts =
        quotationAsk.getSpareParts().stream()
            .filter(x -> x.getPartNameInput().equals(name))
            .toList();
    String quotationAskCode = quotationAsk.getCode();
    SparePartPriceItemModel model = new SparePartPriceItemModel();
    model.setPartNameInput(name);
    model.setPartNameUnit(productInventoryModel.getUnit());
    model.setSegment(productInventoryModel.getSegment());
    Long supplierId = cart.getSupplierId();
    Segment segment = productInventoryModel.getSegment();
    // phụ tùng đính kèm
    if (spareParts.isEmpty()) {
      BiddedSparePartEntity biddedSparePartEntity =
          biddedSparePartRepository
              .getBidedSparePartByName(name, supplierId, quotationAskCode)
              .orElseThrow(
                  () ->
                      new ResourceNotFoundException(
                          "Bidded spare part by name",
                          name + " - " + supplierId + " - " + quotationAskCode));
      AddedSparePartPriceLineItemEntity addedSparePartPriceLineItemEntity =
          addedSparePartPriceLineItemRepository
              .getAddSparePartPriceLineItemProposal(
                  biddedSparePartEntity.getCode(), segment, quotationAskCode, supplierId)
              .orElseThrow(
                  () ->
                      new ResourceNotFoundException(
                          "add spare part by name",
                          biddedSparePartEntity.getCode()
                              + " - "
                              + segment
                              + " - "
                              + quotationAskCode
                              + " - "
                              + supplierId));
      model.setCode(addedSparePartPriceLineItemEntity.getSparePartInputCode());
      model.setQuantity(addedSparePartPriceLineItemEntity.getQuantity());
      model.setMaterialPrice(addedSparePartPriceLineItemEntity.getMaterialPrice());
      model.setServicingPrice(addedSparePartPriceLineItemEntity.getServicingPrice());
      model.setNote(addedSparePartPriceLineItemEntity.getNote());
    } else if (spareParts.size() == 1) {
      // phụ tùng chính
      AskedSparePart askedSparePart = spareParts.getFirst();
      String sparePartCode = askedSparePart.getCode();
      SparePartPriceLineItemEntity sparePartPriceLineItemEntity =
          sparePartPriceLineItemRepository
              .getSparePartPriceLineItemProposal(
                  sparePartCode, segment, quotationAskCode, supplierId)
              .orElseThrow(
                  () ->
                      new ResourceNotFoundException(
                          "SparePartPriceLineItemEntity get by ",
                          sparePartCode
                              + " - "
                              + segment
                              + " - "
                              + quotationAskCode
                              + " - "
                              + supplierId));
      model.setCode(sparePartCode);
      model.setQuantity(sparePartPriceLineItemEntity.getQuantity());
      model.setMaterialPrice(sparePartPriceLineItemEntity.getMaterialPrice());
      model.setServicingPrice(sparePartPriceLineItemEntity.getServicingPrice());
      model.setNote(sparePartPriceLineItemEntity.getNote());
    } else {
      throw new ResourceNotFoundException("SparePart many value by name", name);
    }
    model.setPriceTotal(model.getMaterialPrice().add(model.getServicingPrice()));
    return model;
  }
}
