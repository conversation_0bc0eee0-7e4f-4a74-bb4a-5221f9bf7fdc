package com.actechx.gf.app.service;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DashboardApplicationService {

  private final QuotationAskRepository quotationAskRepository;
  private final PurchaseOrderRepository purchaseOrderRepository;

  /**
   * L<PERSON>y thống kê dashboard cho garage
   * @return DashboardStatsResponseDto chứa các thông tin thống kê
   */
  public DashboardStatsResponseDto getDashboardStats() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting dashboard stats for tenantId: {}", tenantId);

    // Tính thời gian tuần hiện tại
    Instant startOfWeek = DateTimeUtils.getStartOfCurrentWeek();
    Instant endOfWeek = DateTimeUtils.getEndOfCurrentWeek();
    
    log.debug("Week range: {} to {}", startOfWeek, endOfWeek);

    // 1. Số yêu cầu báo giá garage đã tạo trong tuần này
    Long totalQuotationRequestsThisWeek = quotationAskRepository
        .countByTenantIdAndCreatedAtBetween(tenantId, startOfWeek, endOfWeek);

    // 2. Số yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này
    Long quotationRequestsPricingThisWeek = quotationAskRepository
        .countByTenantIdAndStatusAndCreatedAtBetween(tenantId, QuotationStatus.PRICING, startOfWeek, endOfWeek);

    // 3. Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING)
    Long purchaseOrdersInShipping = purchaseOrderRepository
        .countByPurchaserIdAndStatus(tenantId, POStatusEnum.IN_SHIPPING);

    // 4. Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) với thời gian hoàn thành trong tuần này
    Long purchaseOrdersCompletedThisWeek = purchaseOrderRepository
        .countByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfWeek, endOfWeek);

    log.info("Dashboard stats for tenantId {}: totalQuotationRequests={}, quotationRequestsPricing={}, " +
        "purchaseOrdersInShipping={}, purchaseOrdersCompleted={}", 
        tenantId, totalQuotationRequestsThisWeek, quotationRequestsPricingThisWeek, 
        purchaseOrdersInShipping, purchaseOrdersCompletedThisWeek);

    return DashboardStatsResponseDto.builder()
        .totalQuotationRequestsThisWeek(totalQuotationRequestsThisWeek)
        .quotationRequestsPricingThisWeek(quotationRequestsPricingThisWeek)
        .purchaseOrdersInShipping(purchaseOrdersInShipping)
        .purchaseOrdersCompletedThisWeek(purchaseOrdersCompletedThisWeek)
        .build();
  }
}
