package com.actechx.gf.app.service;

import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.controller.form.response.DashboardStatsResponseDto;
import com.actechx.gf.adapter.controller.form.response.SpendingOverviewResponseDto;
import com.actechx.gf.app.utils.DateTimeUtils;
import com.actechx.gf.app.utils.MoneyUtils;
import com.actechx.gf.domain.model.enums.POStatusEnum;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.repository.PurchaseOrderRepository;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DashboardApplicationService {

  private final QuotationAskRepository quotationAskRepository;
  private final PurchaseOrderRepository purchaseOrderRepository;

  /**
   * Lấy thống kê dashboard cho garage
   * @return DashboardStatsResponseDto chứa các thông tin thống kê
   */
  public DashboardStatsResponseDto getDashboardStats() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting dashboard stats for tenantId: {}", tenantId);

    // Tính thời gian tuần hiện tại
    Instant startOfWeek = DateTimeUtils.getStartOfCurrentWeek();
    Instant endOfWeek = DateTimeUtils.getEndOfCurrentWeek();
    
    log.debug("Week range: {} to {}", startOfWeek, endOfWeek);

    // 1. Số yêu cầu báo giá garage đã tạo trong tuần này
    Long totalQuotationRequestsThisWeek = quotationAskRepository
        .countByTenantIdAndCreatedAtBetween(tenantId, startOfWeek, endOfWeek);

    // 2. Số yêu cầu báo giá garage ở trạng thái "Yêu cầu giá chi tiết" (PRICING) trong tuần này
    Long quotationRequestsPricingThisWeek = quotationAskRepository
        .countByTenantIdAndStatusAndCreatedAtBetween(tenantId, QuotationStatus.PRICING, startOfWeek, endOfWeek);

    // 3. Số đơn hàng ở trạng thái "Đang giao hàng" (IN_SHIPPING)
    Long purchaseOrdersInShipping = purchaseOrderRepository
        .countByPurchaserIdAndStatus(tenantId, POStatusEnum.IN_SHIPPING);

    // 4. Số đơn hàng ở trạng thái "Hoàn thành" (CLOSED) với thời gian hoàn thành trong tuần này
    Long purchaseOrdersCompletedThisWeek = purchaseOrderRepository
        .countByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfWeek, endOfWeek);

    log.info("Dashboard stats for tenantId {}: totalQuotationRequests={}, quotationRequestsPricing={}, " +
        "purchaseOrdersInShipping={}, purchaseOrdersCompleted={}", 
        tenantId, totalQuotationRequestsThisWeek, quotationRequestsPricingThisWeek, 
        purchaseOrdersInShipping, purchaseOrdersCompletedThisWeek);

    return DashboardStatsResponseDto.builder()
        .totalQuotationRequestsThisWeek(totalQuotationRequestsThisWeek)
        .quotationRequestsPricingThisWeek(quotationRequestsPricingThisWeek)
        .purchaseOrdersInShipping(purchaseOrdersInShipping)
        .purchaseOrdersCompletedThisWeek(purchaseOrdersCompletedThisWeek)
        .build();
  }

  /**
   * Lấy tổng quan số liệu chi tiêu cho garage
   * @return SpendingOverviewResponseDto chứa thông tin chi tiêu theo tuần, tháng, năm
   */
  public SpendingOverviewResponseDto getSpendingOverview() {
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    log.info("Getting spending overview for tenantId: {}", tenantId);

    // Tính thời gian các khoảng
    Instant startOfWeek = DateTimeUtils.getStartOfCurrentWeek();
    Instant endOfWeek = DateTimeUtils.getEndOfCurrentWeek();
    Instant startOfMonth = DateTimeUtils.getStartOfCurrentMonth();
    Instant endOfMonth = DateTimeUtils.getEndOfCurrentMonth();
    Instant startOfYear = DateTimeUtils.getStartOfCurrentYear();
    Instant endOfYear = DateTimeUtils.getEndOfCurrentYear();

    log.debug("Time ranges - Week: {} to {}, Month: {} to {}, Year: {} to {}",
        startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear);

    // Tính tổng chi tiêu cho từng khoảng thời gian (chỉ đơn hàng hoàn thành - CLOSED)
    BigDecimal totalSpentThisWeek = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfWeek, endOfWeek);

    BigDecimal totalSpentThisMonth = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfMonth, endOfMonth);

    BigDecimal totalSpentThisYear = purchaseOrderRepository
        .sumTotalAmountByPurchaserIdAndStatusAndUpdatedAtBetween(tenantId, POStatusEnum.CLOSED, startOfYear, endOfYear);

    // Xử lý null values
    totalSpentThisWeek = totalSpentThisWeek != null ? totalSpentThisWeek : BigDecimal.ZERO;
    totalSpentThisMonth = totalSpentThisMonth != null ? totalSpentThisMonth : BigDecimal.ZERO;
    totalSpentThisYear = totalSpentThisYear != null ? totalSpentThisYear : BigDecimal.ZERO;

    // Làm tròn số tiền
    BigDecimal totalSpentThisWeekRounded = MoneyUtils.formatMoney(totalSpentThisWeek);
    BigDecimal totalSpentThisMonthRounded = MoneyUtils.formatMoney(totalSpentThisMonth);
    BigDecimal totalSpentThisYearRounded = MoneyUtils.formatMoney(totalSpentThisYear);

    // Tạo chuỗi hiển thị với đơn vị
    String totalSpentThisWeekDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisWeek);
    String totalSpentThisMonthDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisMonth);
    String totalSpentThisYearDisplay = MoneyUtils.formatMoneyWithUnit(totalSpentThisYear);

    // Tạo chuỗi thời gian cập nhật (tuần hiện tại)
    String updateTime = formatUpdateTime(startOfWeek, endOfWeek);

    log.info("Spending overview for tenantId {}: week={}, month={}, year={}",
        tenantId, totalSpentThisWeek, totalSpentThisMonth, totalSpentThisYear);

    return SpendingOverviewResponseDto.builder()
        .totalSpentThisWeek(totalSpentThisWeek)
        .totalSpentThisWeekRounded(totalSpentThisWeekRounded)
        .totalSpentThisMonth(totalSpentThisMonth)
        .totalSpentThisMonthRounded(totalSpentThisMonthRounded)
        .totalSpentThisYear(totalSpentThisYear)
        .totalSpentThisYearRounded(totalSpentThisYearRounded)
        .updateTime(updateTime)
        .totalSpentThisWeekDisplay(totalSpentThisWeekDisplay)
        .totalSpentThisMonthDisplay(totalSpentThisMonthDisplay)
        .totalSpentThisYearDisplay(totalSpentThisYearDisplay)
        .build();
  }

  /**
   * Format thời gian cập nhật theo định dạng dd/MM/yyyy - dd/MM/yyyy
   * @param startTime Thời gian bắt đầu
   * @param endTime Thời gian kết thúc
   * @return Chuỗi thời gian đã format
   */
  private String formatUpdateTime(Instant startTime, Instant endTime) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    LocalDateTime startDateTime = DateTimeUtils.convertInstantToLocalDateTime(startTime);
    LocalDateTime endDateTime = DateTimeUtils.convertInstantToLocalDateTime(endTime);

    return String.format("%s - %s",
        startDateTime.format(formatter),
        endDateTime.format(formatter));
  }
}
