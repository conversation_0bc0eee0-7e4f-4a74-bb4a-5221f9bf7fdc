package com.actechx.gf.app.service;

import com.actechx.common.enums.BusinessMessage;
import com.actechx.common.exception.BusinessException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.gf.adapter.controller.form.request.CreateQuotationBidDto;
import com.actechx.gf.adapter.controller.form.response.QuotationBidResponseDto;
import com.actechx.gf.adapter.persistence.DBUtils;
import com.actechx.gf.app.mapper.QuotationBidMapper;
import com.actechx.gf.domain.model.common.ProductModel;
import com.actechx.gf.domain.model.enums.BidType;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.quotationask.AskedSparePart;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.model.quotationbid.BiddedSparePart;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import com.actechx.gf.domain.repository.QuotationAskRepository;
import com.actechx.gf.domain.repository.QuotationBidRepository;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class QuotationBidApplicationService {
  private final QuotationAskRepository quotationAskRepository;
  private final QuotationBidRepository quotationBidRepository;
  private final QuotationBidMapper quotationBidMapper;
  private final CommonService commonService;
  private final DBUtils dbUtils;

  @Transactional
  public QuotationBidResponseDto createQuotationBid(CreateQuotationBidDto request) {
    log.info("POST create QuotationBid, CreateQuotationBidRequest is {}", request);
    this.validateDataRequest(request);
    var quotationBid =
        quotationBidRepository.findByQuotationAskCodeAndTenantId(
            request.getQuotationAskCode(), request.getTenantId());
    preValidate(request);
    if (quotationBid.isEmpty()) {
      return handleCreateQuotationBid(request);
    } else {
      return handleUpdateQuotationBid(request, quotationBid.get());
    }
  }

  private QuotationBidResponseDto handleCreateQuotationBid(CreateQuotationBidDto request) {
    Long id = dbUtils.getNextSequence("quotation_bid_id");
    var sparePartPriceLineItems =
        request.getSparePartPriceLineItems().stream().map(quotationBidMapper::toDomain).toList();
    var addedSparePartPriceLineItems =
        request.getAddedSparePartPriceLineItems().stream()
            .map(quotationBidMapper::toDomainAdded)
            .toList();
    var biddedSpareParts =
        request.getBiddedSpareParts().stream().map(quotationBidMapper::toDomain).toList();

    QuotationBid quotationBid =
        new QuotationBid(
            id,
            request.getTenantId(),
            request.getTenantType(),
            request.getQuotationAskCode(),
            request.getBidType(),
            request.getStatus(),
            request.getNote(),
            sparePartPriceLineItems,
            addedSparePartPriceLineItems,
            biddedSpareParts,
            request.getCreatedBy());

    // Update QuotationAskStatus
    updateQuotationAskStatusByBid(
        quotationBid.getQuotationAskCode(),
        quotationBid.getBidType(),
        request.getCreatedBy(),
        biddedSpareParts);

    // Save to repository
    QuotationBid savedQuotationBid = quotationBidRepository.store(quotationBid);

    // Map to response DTO
    return quotationBidMapper.toResponse(savedQuotationBid);
  }

  private QuotationBidResponseDto handleUpdateQuotationBid(
      CreateQuotationBidDto request, QuotationBid existingQuotationBid) {
    var sparePartPriceLineItems =
        request.getSparePartPriceLineItems().stream().map(quotationBidMapper::toDomain).toList();
    var addedSparePartPriceLineItems =
        request.getAddedSparePartPriceLineItems().stream()
            .map(quotationBidMapper::toDomainAdded)
            .toList();
    var biddedSpareParts =
        request.getBiddedSpareParts().stream().map(quotationBidMapper::toDomain).toList();
    existingQuotationBid.update(
        request.getTenantType(),
        request.getQuotationAskCode(),
        request.getBidType(),
        request.getStatus(),
        request.getNote(),
        sparePartPriceLineItems,
        addedSparePartPriceLineItems,
        biddedSpareParts,
        request.getCreatedBy());

    // Save updated quotation bid
    QuotationBid updatedQuotationBid = quotationBidRepository.store(existingQuotationBid);

    // Update QuotationAskStatus
    updateQuotationAskStatusByBid(
        updatedQuotationBid.getQuotationAskCode(),
        updatedQuotationBid.getBidType(),
        request.getCreatedBy(),
        biddedSpareParts);

    // Map to response DTO
    return quotationBidMapper.toResponse(updatedQuotationBid);
  }

  private void updateQuotationAskStatusByBid(
      String quotationAskCode,
      BidType bidType,
      String updatedBy,
      List<BiddedSparePart> biddedSpareParts) {
    var quotationAsk =
        quotationAskRepository
            .findByCode(quotationAskCode)
            .orElseThrow(
                () -> new ResourceNotFoundException("QuotationAsk", "code", quotationAskCode));
    if (QuotationStatus.ASKING.equals(quotationAsk.getStatus()) && BidType.BIDDED.equals(bidType)) {
      quotationAsk.updateStatus(QuotationStatus.BIDDING, updatedBy);
    }
    quotationAsk.setUpdatedAt(Instant.now());
    quotationAskRepository.store(quotationAsk, true);

    if (!CollectionUtils.isEmpty(biddedSpareParts)) {
      commonService.createProductClient(
          biddedSpareParts.stream()
              .map(b -> new ProductModel(b.getPartNameInput(), b.getPartNameUnit()))
              .toList(),
          quotationAsk.getTenantId());
    }
  }

  private void preValidate(CreateQuotationBidDto request) {
    var quotationAsk =
        quotationAskRepository
            .findByCode(request.getQuotationAskCode())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationAsk", "code", request.getQuotationAskCode()));
    var existsSparePartCodes =
        quotationAsk.getSpareParts().stream().map(AskedSparePart::getCode).toList();
    for (var sparePart : request.getSparePartPriceLineItems()) {
      if (existsSparePartCodes.stream()
          .noneMatch(sparePartCode -> sparePart.getSparePartInputCode().equals(sparePartCode))) {
        throw new ResourceNotFoundException("SparePart", sparePart.getSparePartInputCode());
      }
    }
  }

  private void validateDataRequest(CreateQuotationBidDto request) {
    if (CollectionUtils.isEmpty(request.getSparePartPriceLineItems())) {
      request.setSparePartPriceLineItems(List.of());
    } else {
      request.setSparePartPriceLineItems(
          request.getSparePartPriceLineItems().stream().filter(x -> x.getPrice() != null).toList());
    }
    if (CollectionUtils.isEmpty(request.getBiddedSpareParts())) {
      request.setBiddedSpareParts(List.of());
    }
    if (CollectionUtils.isEmpty(request.getAddedSparePartPriceLineItems())) {
      request.setAddedSparePartPriceLineItems(List.of());
    } else {
      request.setAddedSparePartPriceLineItems(
          request.getAddedSparePartPriceLineItems().stream()
              .filter(x -> x.getPrice() != null)
              .toList());
    }
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByCode(request.getQuotationAskCode())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationAsk", "code", request.getQuotationAskCode()));

    List<String> sparePartCodesDb =
        quotationAsk.getSpareParts().stream().map(AskedSparePart::getCode).toList();
    List<String> sparePartPriceLineItemCodesRequest =
        request.getSparePartPriceLineItems().stream()
            .map(CreateQuotationBidDto.SparePartPriceLineItemsDto::getSparePartInputCode)
            .toList();
    List<String> notInValidCodes =
        sparePartPriceLineItemCodesRequest.stream()
            .filter(x -> !sparePartCodesDb.contains(x))
            .toList();
    if (!CollectionUtils.isEmpty(notInValidCodes)) {
      throw new BusinessException(BusinessMessage.IAM_037, notInValidCodes);
    }

    List<String> refCodes =
        request.getBiddedSpareParts().stream()
            .map(CreateQuotationBidDto.BiddedSparePartsDto::getRefCode)
            .toList();
    List<String> notInValidRefcodes =
        refCodes.stream().filter(x -> !sparePartPriceLineItemCodesRequest.contains(x)).toList();
    if (!CollectionUtils.isEmpty(notInValidRefcodes)) {
      throw new BusinessException(BusinessMessage.IAM_037, notInValidRefcodes);
    }

    List<String> addSparePartPriceLineItemCodesRequest =
        request.getAddedSparePartPriceLineItems().stream()
            .map(CreateQuotationBidDto.SparePartPriceLineItemsDto::getSparePartInputCode)
            .toList();
    List<String> bidSparePartCodes =
        request.getBiddedSpareParts().stream()
            .map(CreateQuotationBidDto.BiddedSparePartsDto::getCode)
            .toList();
    List<String> notAddSparePartCodesInvalid =
        addSparePartPriceLineItemCodesRequest.stream()
            .filter(x -> !bidSparePartCodes.contains(x))
            .toList();
    if (!CollectionUtils.isEmpty(notAddSparePartCodesInvalid)) {
      throw new BusinessException(BusinessMessage.IAM_037, notAddSparePartCodesInvalid);
    }

    Set<Long> bidTenantIdsRequest =
        request.getBiddedSpareParts().stream()
            .map(CreateQuotationBidDto.BiddedSparePartsDto::getTenantId)
            .collect(Collectors.toSet());
    List<Long> notBidTenantIds =
        bidTenantIdsRequest.stream().filter(x -> !request.getTenantId().equals(x)).toList();
    if (!CollectionUtils.isEmpty(notBidTenantIds)) {
      throw new BusinessException(BusinessMessage.IAM_037, notBidTenantIds);
    }

    Set<String> uniqueCodes = new HashSet<>();
    List<String> duplicatedCodes =
        bidSparePartCodes.stream().filter(code -> !uniqueCodes.add(code)).toList();
    if (!duplicatedCodes.isEmpty()) {
      throw new BusinessException(BusinessMessage.IAM_037, duplicatedCodes);
    }

    Map<String, List<CreateQuotationBidDto.SparePartPriceLineItemsDto>> grouped =
        request.getSparePartPriceLineItems().stream()
            .collect(
                Collectors.groupingBy(x -> x.getSparePartInputCode() + " - " + x.getSegment()));
    Set<String> duplicatedKeys =
        grouped.entrySet().stream()
            .filter(entry -> entry.getValue().size() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    if (!CollectionUtils.isEmpty(duplicatedKeys)) {
      throw new BusinessException(BusinessMessage.IAM_037, duplicatedKeys);
    }
  }
}
