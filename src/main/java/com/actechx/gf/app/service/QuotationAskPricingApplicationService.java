package com.actechx.gf.app.service;

import com.actechx.common.dto.ApiResponse;
import com.actechx.common.enums.BusinessMessage;
import com.actechx.common.exception.BusinessException;
import com.actechx.common.exception.ResourceNotFoundException;
import com.actechx.common.security.utils.SecurityUtils;
import com.actechx.gf.adapter.client.AgentClient;
import com.actechx.gf.adapter.client.request.QuotationAskPricingClientRequest;
import com.actechx.gf.adapter.controller.form.request.QuotationAskPricingProposalRequest;
import com.actechx.gf.adapter.controller.form.request.QuotationAskPricingRequestDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingProposalResponse;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingProposalResponseDto;
import com.actechx.gf.adapter.controller.form.response.QuotationAskPricingResponseDto;
import com.actechx.gf.adapter.persistence.quotation.ask.AskedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.AddedSparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.BiddedSparePartEntity;
import com.actechx.gf.adapter.persistence.quotation.bid.SparePartPriceLineItemEntity;
import com.actechx.gf.adapter.persistence.quotation.pricing.QuotationAskPricingProposalEntity;
import com.actechx.gf.app.mapper.QuotationAskPricingMapper;
import com.actechx.gf.domain.model.enums.QuotationBidStatus;
import com.actechx.gf.domain.model.enums.QuotationStatus;
import com.actechx.gf.domain.model.quotationask.QuotationAsk;
import com.actechx.gf.domain.model.quotationbid.QuotationBid;
import com.actechx.gf.domain.model.quotationpricing.PricingNumberSuccess;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposal;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingProposalResponseDomain;
import com.actechx.gf.domain.model.quotationpricing.QuotationAskPricingRequest;
import com.actechx.gf.domain.repository.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class QuotationAskPricingApplicationService {
  private final QuotationAskPricingMapper pricingMapper;
  private final AgentClient agentClient;
  private final QuotationAskRepository quotationAskRepository;
  private final QuotationAskPricingRequestRepository quotationAskPricingRequestRepository;
  private final QuotationAskPricingProposalRepository quotationAskPricingProposalRepository;
  private final BiddedSparePartRepository biddedSparePartRepository;
  private final QuotationBidRepository quotationBidRepository;
  private final SparePartPriceLineItemRepository sparePartPriceLineItemRepository;
  private final AddedSparePartPriceLineItemRepository addedSparePartPriceLineItemRepository;
  private final AskedSparePartRepository askedSparePartRepository;

  @Transactional
  public QuotationAskPricingResponseDto quotationAskPricingRequestService(
      QuotationAskPricingRequestDto request) {
    this.removeDuplicateAddSparePartLineItems(request);
    Long tenantId = SecurityUtils.getCurrentTenantIdAsLong();
    Long quotationAskId = request.getId();
    QuotationAsk quotationAsk =
        quotationAskRepository
            .findByIdAndTenantId(quotationAskId, tenantId)
            .orElseThrow(
                () -> new ResourceNotFoundException("QuotationAsk Pricing", request.getId()));
    String quotationAskCode = quotationAsk.getCode();
    this.checkQuotationAskStatus(quotationAsk.getStatus());
    QuotationAskPricingRequest quotationAskPricingRequest = new QuotationAskPricingRequest();
    quotationAskPricingRequest.setQuotationAskCode(quotationAsk.getCode());
    quotationAskPricingRequest.setOriginTenantId(tenantId);
    List<QuotationAskPricingRequest.AskedSparePartPricing> askedSparePartPricings =
        new ArrayList<>();
    request
        .getSpareParts()
        .forEach(
            sparePart -> {
              AskedSparePartEntity askedSparePartEntity =
                  askedSparePartRepository
                      .findByIdAndTenantIdAndQuotationAskIdRepo(
                          sparePart.getId(), tenantId, quotationAskId)
                      .orElseThrow(
                          () ->
                              new ResourceNotFoundException(
                                  "AskedSparePart get by id", sparePart.getId()));
              String sparePartCode = askedSparePartEntity.getCode();
              List<Long> sparePartPriceLineItemIds =
                  sparePartPriceLineItemRepository.findBySparePartInputCodeAndQuotationCodeRepo(
                      sparePartCode, quotationAskCode);
              List<QuotationAskPricingRequestDto.SparePartLineItem> sparePartLineItems =
                  sparePart.getSparePartLineItems();
              sparePartLineItems.forEach(
                  sparePartLineItem -> {
                    // lấy danh sách phụ tùng chính được chọn
                    List<QuotationAskPricingRequestDto.SparePartLineItemSegment>
                        sparePartLineItemSegmentsTrue =
                            sparePartLineItem.getSparePartLineItemSegments().stream()
                                .filter(x -> x.getDetailStatus().equals(Boolean.TRUE))
                                .toList();
                    if (CollectionUtils.isEmpty(sparePartLineItemSegmentsTrue)) {
                      return;
                    }
                    List<Long> sparePartLineItemRequestIds =
                        sparePartLineItemSegmentsTrue.stream()
                            .map(QuotationAskPricingRequestDto.SparePartLineItemSegment::getId)
                            .toList();
                    List<Long> notInPriceList =
                        sparePartLineItemRequestIds.stream()
                            .filter(id -> !sparePartPriceLineItemIds.contains(id))
                            .toList();
                    if (!CollectionUtils.isEmpty(notInPriceList)) {
                      throw new ResourceNotFoundException(
                          "SparePartLineItems not found", notInPriceList);
                    }
                    // thêm phụ tùng chính
                    this.setSparePartLineItems(
                        askedSparePartPricings, sparePartLineItemSegmentsTrue);
                    // thêm phụ tùng đính kèm
                    this.setAddSparePartLineItem(
                        askedSparePartPricings, sparePartLineItemSegmentsTrue);
                  });
            });
    quotationAskPricingRequest.setAskedSpareParts(askedSparePartPricings);
    if (CollectionUtils.isEmpty(quotationAskPricingRequest.getAskedSpareParts())) {
      return null;
    }
    QuotationAskPricingResponseDto responseDto =
        quotationAskPricingRequestRepository.saveData(quotationAskPricingRequest);
    // call sang agent
    this.createPricingRequestClient(responseDto);
    return responseDto;
  }

  @Transactional
  public QuotationAskPricingProposalResponse quotationAskPricingProposalService(
      QuotationAskPricingProposalRequest request) {
    String quotationAskCode = request.getQuotationAskCode();
    Long replyTenantId = request.getReplyTenantId();
    QuotationBid quotationBid =
        quotationBidRepository
            .findByQuotationAskCodeAndTenantId(quotationAskCode, replyTenantId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        "QuotationBid code, tenantId", quotationAskCode + " - " + replyTenantId));
    this.checkQuotationBidStatus(quotationBid.getStatus());
    quotationBidRepository.updateStatus(quotationBid);
    Optional<QuotationAskPricingProposalEntity> quotationAskPricingProposalEntityOptional =
        quotationAskPricingProposalRepository.getByQuotationAskCodeAndReplyTenantId(
            quotationAskCode, replyTenantId);
    QuotationAskPricingProposal quotationAskPricingProposal =
        pricingMapper.toClientProposal(request);

    String type;
    PricingNumberSuccess pricingNumberSuccess;
    if (quotationAskPricingProposalEntityOptional.isEmpty()) {
      type = "create";
      pricingNumberSuccess =
          quotationAskPricingProposalRepository.createData(quotationAskPricingProposal);
    } else {
      type = "update";
      QuotationAskPricingProposalEntity quotationAskPricingProposalEntity =
          quotationAskPricingProposalEntityOptional.get();
      pricingNumberSuccess =
          quotationAskPricingProposalRepository.updateData(
              quotationAskPricingProposalEntity, quotationAskPricingProposal);
    }
    quotationBidRepository.updateQuotationBidStatusRepo(
        replyTenantId, quotationAskCode, QuotationBidStatus.PRICED);
    QuotationAskPricingProposalResponse response = new QuotationAskPricingProposalResponse();
    response.setType(type);
    response.setCreateNumber(pricingNumberSuccess.getCreateNumber());
    response.setUpdateNumber(pricingNumberSuccess.getUpdateNumber());
    return response;
  }

  @Transactional
  public QuotationAskPricingProposalResponseDto getQuotationAskPricingProposalService(
      Long sparePartPriceLineItemId) {
    QuotationAskPricingProposalResponseDomain responseDomain =
        quotationAskPricingProposalRepository.getQuotationAskPricingProposal(
            sparePartPriceLineItemId);
    return pricingMapper.toProposalResponsesDto(responseDomain);
  }

  private void removeDuplicateAddSparePartLineItems(QuotationAskPricingRequestDto request) {
    if (request == null || request.getSpareParts() == null) return;

    // Tập hợp để nhớ id nào với detailStatus=true đã xuất hiện
    Set<Long> seenTrueIds = new HashSet<>();

    for (QuotationAskPricingRequestDto.AddSparePartPricingRequestDto sparePart :
        request.getSpareParts()) {
      if (sparePart.getSparePartLineItems() == null) continue;

      for (QuotationAskPricingRequestDto.SparePartLineItem lineItem :
          sparePart.getSparePartLineItems()) {
        if (lineItem.getSparePartLineItemSegments() == null) continue;

        for (QuotationAskPricingRequestDto.SparePartLineItemSegment segment :
            lineItem.getSparePartLineItemSegments()) {
          if (segment.getAddSparePartLineItems() == null) continue;

          List<QuotationAskPricingRequestDto.AddSparePartLineItem> filtered =
              segment.getAddSparePartLineItems().stream()
                  .filter(
                      item -> {
                        if (Boolean.TRUE.equals(item.getDetailStatus())) {
                          // Nếu detailStatus=true thì check theo id
                          return seenTrueIds.add(item.getId());
                        }
                        // Nếu detailStatus=false thì giữ nguyên
                        return true;
                      })
                  .toList();

          segment.setAddSparePartLineItems(filtered);
        }
      }
    }
  }

  private void setSparePartLineItems(
      List<QuotationAskPricingRequest.AskedSparePartPricing> askedSparePartPricings,
      List<QuotationAskPricingRequestDto.SparePartLineItemSegment> sparePartLineItemSegments) {

    List<Integer> sparePartPriceLineItemQuantitys =
        sparePartLineItemSegments.stream()
            .map(QuotationAskPricingRequestDto.SparePartLineItemSegment::getQuantity)
            .toList();
    this.checkQuantity(sparePartPriceLineItemQuantitys);

    List<Long> sparePartPriceLineItemIds =
        sparePartLineItemSegments.stream()
            .map(QuotationAskPricingRequestDto.SparePartLineItemSegment::getId)
            .toList();
    List<QuotationBidStatus> quotationBidStatuses =
        Arrays.asList(QuotationBidStatus.OPEN, QuotationBidStatus.PRICED);
    List<SparePartPriceLineItemEntity> sparePartPriceLineItemEntities =
        sparePartPriceLineItemRepository.getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
            sparePartPriceLineItemIds, quotationBidStatuses);

    List<String> sparePartInputCodes =
        sparePartPriceLineItemEntities.stream()
            .map(SparePartPriceLineItemEntity::getSparePartInputCode)
            .toList();
    List<AskedSparePartEntity> askedSparePartEntities =
        askedSparePartRepository.getByCodeList(sparePartInputCodes);

    // thêm phụ tùng chính
    sparePartPriceLineItemEntities.forEach(
        sparePartPriceLineItemEntity -> {
          Long sparePartLineItemId = sparePartPriceLineItemEntity.getId();
          String sparePartInputCode = sparePartPriceLineItemEntity.getSparePartInputCode();

          AskedSparePartEntity askedSparePartEntity =
              askedSparePartEntities.stream()
                  .filter(x -> x.getCode().equals(sparePartInputCode))
                  .findFirst()
                  .orElseThrow(
                      () ->
                          new ResourceNotFoundException("AskedSparePart code", sparePartInputCode));

          QuotationAskPricingRequestDto.SparePartLineItemSegment sparePartLineItemSegment =
              sparePartLineItemSegments.stream()
                  .filter(x -> x.getId().equals(sparePartLineItemId))
                  .findFirst()
                  .orElseThrow(
                      () ->
                          new ResourceNotFoundException(
                              "SparePartLineItemId", sparePartLineItemId));

          QuotationAskPricingRequest.AskedSparePartPricing sparePartPricing =
              new QuotationAskPricingRequest.AskedSparePartPricing();
          sparePartPricing.setSparePartLineItemId(sparePartLineItemId);
          sparePartPricing.setCode(sparePartInputCode);
          sparePartPricing.setPartNameInput(askedSparePartEntity.getPartNameInput());
          sparePartPricing.setPartNameUnit(askedSparePartEntity.getPartNameUnit());
          sparePartPricing.setQuantity(sparePartLineItemSegment.getQuantity());
          sparePartPricing.setTenantId(
              sparePartPriceLineItemEntity.getQuotationBid().getTenantId());
          sparePartPricing.setSegment(sparePartPriceLineItemEntity.getSegment());
          askedSparePartPricings.add(sparePartPricing);
        });
  }

  private void setAddSparePartLineItem(
      List<QuotationAskPricingRequest.AskedSparePartPricing> askedSparePartPricings,
      List<QuotationAskPricingRequestDto.SparePartLineItemSegment> sparePartLineItemSegments) {
    sparePartLineItemSegments.forEach(
        sparePartLineItemSegment -> {
          // lấy danh sách phụ tùng đính kèm được chọn
          List<QuotationAskPricingRequestDto.AddSparePartLineItem> addSparePartLineItemsTrue =
              sparePartLineItemSegment.getAddSparePartLineItems().stream()
                  .filter(x -> x.getDetailStatus().equals(Boolean.TRUE))
                  .toList();
          if (CollectionUtils.isEmpty(addSparePartLineItemsTrue)) {
            return;
          }

          List<Integer> addSparePartLineItemsQuantity =
              addSparePartLineItemsTrue.stream()
                  .map(QuotationAskPricingRequestDto.AddSparePartLineItem::getQuantity)
                  .toList();
          this.checkQuantity(addSparePartLineItemsQuantity);

          List<Long> addSparePartLineItemIds =
              addSparePartLineItemsTrue.stream()
                  .map(QuotationAskPricingRequestDto.AddSparePartLineItem::getId)
                  .toList();
          List<QuotationBidStatus> quotationBidStatuses =
              Arrays.asList(QuotationBidStatus.OPEN, QuotationBidStatus.PRICED);
          List<AddedSparePartPriceLineItemEntity> addedSparePartPriceLineItemEntities =
              addedSparePartPriceLineItemRepository
                  .getListByIdInAndDetailStatusFalseAndQuotationBidStatusIn(
                      addSparePartLineItemIds, quotationBidStatuses);

          // thêm phụ tùng đính kèm
          addedSparePartPriceLineItemEntities.forEach(
              addedSparePartPriceLineItemEntity -> {
                String sparePartInputCode =
                    addedSparePartPriceLineItemEntity.getSparePartInputCode();
                BiddedSparePartEntity biddedSparePartEntity =
                    biddedSparePartRepository
                        .getByCode(sparePartInputCode)
                        .orElseThrow(
                            () ->
                                new ResourceNotFoundException(
                                    "BiddedSparePartEntity get code", sparePartInputCode));
                Long addedSparePartPriceLineItemEntityId =
                    addedSparePartPriceLineItemEntity.getId();
                QuotationAskPricingRequestDto.AddSparePartLineItem addSparePartLineItem =
                    addSparePartLineItemsTrue.stream()
                        .filter(x -> x.getId().equals(addedSparePartPriceLineItemEntityId))
                        .findFirst()
                        .orElseThrow(
                            () ->
                                new ResourceNotFoundException(
                                    "AddSparePartLineItem", addedSparePartPriceLineItemEntityId));
                QuotationAskPricingRequest.AskedSparePartPricing sparePartPricing =
                    new QuotationAskPricingRequest.AskedSparePartPricing();
                sparePartPricing.setAddSparePartLineItemId(addedSparePartPriceLineItemEntityId);
                sparePartPricing.setCode(sparePartInputCode);
                sparePartPricing.setPartNameInput(biddedSparePartEntity.getPartNameInput());
                sparePartPricing.setPartNameUnit(biddedSparePartEntity.getPartNameUnit());
                sparePartPricing.setQuantity(addSparePartLineItem.getQuantity());
                sparePartPricing.setRefCode(biddedSparePartEntity.getRefCode());
                sparePartPricing.setTenantId(biddedSparePartEntity.getTenantId());
                sparePartPricing.setSegment(addedSparePartPriceLineItemEntity.getSegment());
                askedSparePartPricings.add(sparePartPricing);
              });
        });
  }

  private void createPricingRequestClient(QuotationAskPricingResponseDto responseDto) {
    List<QuotationAskPricingResponseDto.AskedSparePartPricingResponse> askedSpareParts =
        responseDto.getAskedSpareParts();
    Set<Long> tenantIds =
        askedSpareParts.stream()
            .map(QuotationAskPricingResponseDto.AskedSparePartPricingResponse::getTenantId)
            .collect(Collectors.toSet());
    String quotationAskCode = responseDto.getQuotationAskCode();
    Long originTenantId = responseDto.getOriginTenantId();
    LocalDateTime createdAt = responseDto.getCreatedAt();
    String createdBy = responseDto.getCreatedBy();
    List<QuotationAskPricingClientRequest> quotationAskPricingClientRequests = new ArrayList<>();
    tenantIds.forEach(
        x -> {
          QuotationAskPricingClientRequest quotationAskPricingClientRequest =
              new QuotationAskPricingClientRequest();
          List<QuotationAskPricingResponseDto.AskedSparePartPricingResponse>
              tenantIdAskedSpareParts =
                  askedSpareParts.stream().filter(a -> a.getTenantId().equals(x)).toList();
          quotationAskPricingClientRequest.setQuotationAskCode(quotationAskCode);
          quotationAskPricingClientRequest.setOriginTenantId(originTenantId);
          quotationAskPricingClientRequest.setTargetTenantId(x);
          quotationAskPricingClientRequest.setCreatedAt(createdAt);
          quotationAskPricingClientRequest.setCreatedBy(createdBy);
          List<QuotationAskPricingClientRequest.AskedSparePartPricingClientRequest>
              askedSparePartPricingClientRequests =
                  pricingMapper.toClientListRequest(tenantIdAskedSpareParts);
          quotationAskPricingClientRequest.setAskedSpareParts(askedSparePartPricingClientRequests);
          quotationAskPricingClientRequests.add(quotationAskPricingClientRequest);
        });
    log.info("Call agent pricing request number: {}", quotationAskPricingClientRequests.size());
    ApiResponse<String> agentClientAgent =
        agentClient.createPricingRequest(quotationAskPricingClientRequests);
    log.info("Call agent pricing request success: {}", agentClientAgent.getData());
  }

  private void checkQuantity(List<Integer> integers) {
    if (CollectionUtils.isEmpty(integers)) {
      throw new BusinessException(BusinessMessage.IAM_037, "Danh sách quantity phải khác null");
    }
    if (integers.stream().anyMatch(Objects::isNull)) {
      throw new BusinessException(
          BusinessMessage.IAM_037, "Quantity không được phép null: {}", integers);
    }
    if (integers.stream().anyMatch(i -> i < 1)) {
      throw new BusinessException(BusinessMessage.IAM_037, "Quantity phải lớn hơn 0: {}", integers);
    }
  }

  private void checkQuotationAskStatus(QuotationStatus status) {
    switch (status) {
      case QuotationStatus.BIDDING -> log.info("Pricing request first: {}", status);
      case QuotationStatus.PRICING -> log.info("Pricing request many: {}", status);
      default ->
          throw new BusinessException(
              BusinessMessage.IAM_037, "Pricing request status invalid: {}", status);
    }
  }

  private void checkQuotationBidStatus(QuotationBidStatus status) {
    switch (status) {
      case QuotationBidStatus.OPEN -> log.info("Pricing proposal first: {}", status);
      case QuotationBidStatus.PRICED -> log.info("Pricing proposal many: {}", status);
      default ->
          throw new BusinessException(
              BusinessMessage.IAM_037, "Pricing request status invalid: {}", status);
    }
  }
}
